name: Build CI Builder Image

on:
  push:
    paths:
      - '.github/docker/ci-builder.Dockerfile'
      - '.github/workflows/build-ci-image.yml'
    branches:
      - main
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: '强制重新构建镜像'
        type: boolean
        default: false

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: moegolibrary/moego-ci-builder

jobs:
  build-and-push:
    runs-on: ubuntu-24.04-2core
    permissions:
      contents: read
      packages: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=raw,value=latest,enable={{is_default_branch}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: .github/docker/ci-builder.Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64 