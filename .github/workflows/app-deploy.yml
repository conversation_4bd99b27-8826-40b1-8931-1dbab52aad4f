name: App deploy workflow
run-name: "Deploy ${{ inputs.app-dir }} apps:[${{ inputs.apps }}] to ${{ inputs.env }} with image tag:<${{ inputs.image-tag }}>"

on:
  workflow_dispatch:
    inputs:
      app-dir:
        description: "App directory"
        required: true
        type: choice
        default: "backend"
        options:
          - backend
          - frontend
      apps:
        description: "Apps to deploy, separated by blank space"
        required: true
        type: string
      env:
        description: "Env to deploy"
        required: true
        type: choice
        default: "testing"
        options:
          - testing
          - staging
          - production
          - devops
      image-tag:
        description: "Image tag"
        required: true
        type: string
      skip-canary:
        type: boolean
        required: false
        default: false
        description: "Skip canary deployment"
  workflow_call:
    inputs:
      app-dir:
        required: true
        type: string
      apps:
        required: true
        type: string
      env:
        required: true
        type: string
      image-tag:
        required: true
        type: string
      skip-canary:
        type: boolean
        required: false
        default: false
        description: "Skip canary deployment"

jobs:
  check:
    runs-on: ubuntu-24.04-2core
    steps:
      - name: Check env
        run: |
          if [ "${{ inputs.env }}" == "devops" ]; then
            allowed_apps="devops-console moego-gemini-mcp-client gemini_mcp_client aistudio moego-aistudio moego-cs-page-watcher cs_page_watcher devops-auth"
            if ! echo "$allowed_apps" | grep -q "${{ inputs.apps }}"; then
              echo "Error: apps='${{ inputs.apps }}' can not access env='${{ inputs.env }}'"
              exit 1
            fi
          fi

  deploy:
    needs: check
    runs-on: ubuntu-24.04-2core
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup GitHub
        uses: MoeGolibrary/moego-actions-tool/.github/actions/setup-github/@production
        with:
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
        continue-on-error: true

      - name: Prepare AWS ECR
        run: |
          aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 693727988157.dkr.ecr.us-west-2.amazonaws.com
        continue-on-error: true

      - uses: clowdhaus/argo-cd-action/@main
        env:
          GITHUB_TOKEN: ${{ secrets.ADMIN_TOKEN_GITHUB }}
        with:
          command: version
          options: --client

      - name: Clone apps scripts
        shell: bash
        run: |
          mkdir .moego-deploy-repos && cd .moego-deploy-repos
          git clone https://github.com/MoeGolibrary/moego-k8s-apps.git

      - name: Configure moego-k8s-apps
        shell: bash
        run: |
          chmod +x .moego-deploy-repos/moego-k8s-apps/scripts/*.sh

      - name: Set ArgoCD
        shell: bash
        run: |
          argocd login argocd.devops.moego.pet:443 --username ${{ secrets.ARGOCD_CI_USERNAME }} --password ${{ secrets.ARGOCD_CI_PASSWORD }} --grpc-web
          argocd version

      - name: Prepare Deploy
        if: inputs.apps != ''
        id: prepare_deploy
        run: |
          repo_name=$(echo "${{ github.repository }}" | awk -F/ '{print $2}')
          branch_name=${{ github.ref_name }}
          image_tag=${{ inputs.image-tag }}
          sha=${{ github.sha }}
          environment=${{ inputs.env }}
          service_branch_image_canary_pairs=""
          service_branch_pairs=""
          app_dir=${{ inputs.app-dir }}/app
          
          if [ "${{ inputs.skip-canary }}" == "true" ]; then
            canary="false"
          else
            canary="true"
          fi

          for app in ${{ inputs.apps }}; do
            if [ -d "${app_dir}/${app}" ]; then
              spec_name=$(yq '.spec.name' ${app_dir}/${app}/metadata.yaml)
              cd=$(yq '.spec.cd' ${app_dir}/${app}/metadata.yaml)
              # cd 不为true 则跳过
              if [ "${cd}" != "true" ]; then
                continue
              fi
              # 如果不存在.moego-deploy-repos/moego-k8s-apps/apps/${spec_name}目录
              if [ ! -d ".moego-deploy-repos/moego-k8s-apps/apps/${spec_name}" ]; then
                echo "Create app ${spec_name} in moego-k8s-apps"
                app_type=svc
                if [ "${{ inputs.app-dir }}" == "frontend" ]; then
                  app_type=web
                fi
                (cd .moego-deploy-repos/moego-k8s-apps/ && make create-app APP_NAME=${spec_name} APP_TYPE=${app_type} PUSH_TO_GIT=true || exit 1)
              fi
              # 如果service_branch_image_canary_pairs不为空，则添加空格
              if [ ! -z "${service_branch_image_canary_pairs}" ]; then
                service_branch_image_canary_pairs="${service_branch_image_canary_pairs} "
                service_branch_pairs="${service_branch_pairs} "
              fi
              service_branch_image_canary_pairs="${service_branch_image_canary_pairs} ${spec_name}:${branch_name},${image_tag},${canary},${sha}"
              service_branch_pairs="${service_branch_pairs} ${spec_name}:${branch_name}"
            fi
          done

          echo "REPO_NAME=${repo_name}" >> $GITHUB_ENV
          echo "ENVIRONMENT=${environment}" >> $GITHUB_ENV
          echo "SERVICE_BRANCH_IMAGE_CANARY_PAIRS=${service_branch_image_canary_pairs}" >> $GITHUB_ENV
          echo "SERVICE_BRANCH_PAIRS=${service_branch_pairs}" >> $GITHUB_ENV

      - name: Update YAML Files
        id: update_yaml
        run: |
          # service_branch_image_canary_pairs 为空
          if [ -z "${{ env.SERVICE_BRANCH_IMAGE_CANARY_PAIRS }}" ]; then
            echo "No services to update."
            exit 0
          fi
          build_id="${{ github.ref_name }}-${{ github.run_id }}-${{ github.run_attempt }}"
          cd .moego-deploy-repos/moego-k8s-apps
          bash ./scripts/deploy-services.sh \
              --repo=${{ env.REPO_NAME }} \
              --env=${{ env.ENVIRONMENT }} \
              --build-id=${build_id} \
              --service-branch-image-canary-pairs="${{ env.SERVICE_BRANCH_IMAGE_CANARY_PAIRS }}"
          cd -

      - name: Sync ArgoCD
        run: |
          # service_branch_pairs 为空
          if [ -z "${{ env.SERVICE_BRANCH_PAIRS }}" ]; then
            echo "No services to sync."
            exit 0
          fi

          cd .moego-deploy-repos/moego-k8s-apps
          bash ./scripts/sync-services.sh \
              --env=${{ env.ENVIRONMENT }} \
              --service-branch-pairs="${{ env.SERVICE_BRANCH_PAIRS }}"
          cd -
