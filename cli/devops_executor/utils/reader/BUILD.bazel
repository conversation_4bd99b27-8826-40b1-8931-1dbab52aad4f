load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "reader",
    srcs = [
        "aws_secrets_reader.go",
        "default_pairs_reader.go",
        "k8s_secrets_reader.go",
        "reader.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/utils/reader",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/utils",
        "//cli/devops_executor/utils/aws",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//:secretsmanager",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//types",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
    ],
)
