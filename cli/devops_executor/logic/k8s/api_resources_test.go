package k8s_test

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/version"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/k8s"
	utilsk8s "github.com/MoeGolibrary/moego/cli/devops_executor/utils/k8s"
)

func TestClusterAPIResources(t *testing.T) {
	mockDiscovery := &MockDiscovery{
		serverVersion: &version.Info{
			Major:      "1",
			Minor:      "32",
			GitVersion: "v1.32.0",
		},
		serverGroups: &v1.APIGroupList{
			Groups: []v1.APIGroup{
				{
					Name: "apps",
					Versions: []v1.GroupVersionForDiscovery{
						{GroupVersion: "apps/v1", Version: "v1"},
					},
					PreferredVersion: v1.GroupVersionForDiscovery{
						GroupVersion: "apps/v1",
						Version:      "v1",
					},
				},
			},
		},
		resources: map[string]*v1.APIResourceList{
			"v1": {
				GroupVersion: "v1",
				APIResources: []v1.APIResource{
					{Name: "pods", Namespaced: true, Kind: "Pod"},
				},
			},
			"apps/v1": {
				GroupVersion: "apps/v1",
				APIResources: []v1.APIResource{
					{Name: "deployments", Namespaced: true, Kind: "Deployment"},
				},
			},
		},
	}

	client := &MockK8SClient{
		Interface: fake.NewSimpleClientset(),
		discovery: mockDiscovery,
	}

	originalCreateClient := utilsk8s.CreateClient
	defer func() { utilsk8s.CreateClient = originalCreateClient }()

	utilsk8s.CreateClient = func(cluster string) (kubernetes.Interface, error) {
		return client, nil
	}

	params := k8s.APIResourcesParams{
		Cluster: "development",
		Sort:    true,
		Output:  "testdata/output/api-resources.yaml",
	}
	err := k8s.ClusterAPIResources(context.Background(), params)
	require.Nil(t, err)

	utilsk8s.CreateClient = func(_ string) (kubernetes.Interface, error) {
		return nil, errors.New("create k8s client failed")
	}
	params.Output = ""
	err = k8s.ClusterAPIResources(context.Background(), params)
	require.Equal(t, true, err != nil)

	os.RemoveAll("testdata/output")
}

// mock kubernetes.Interface
type MockK8SClient struct {
	kubernetes.Interface
	discovery discovery.DiscoveryInterface
}

func (c *MockK8SClient) Discovery() discovery.DiscoveryInterface {
	return c.discovery
}

// mock discovery.DiscoveryInterface
type MockDiscovery struct {
	discovery.DiscoveryInterface
	serverVersion *version.Info
	serverGroups  *v1.APIGroupList
	resources     map[string]*v1.APIResourceList
}

func (d *MockDiscovery) ServerVersion() (*version.Info, error) {
	return d.serverVersion, nil
}

func (d *MockDiscovery) ServerGroups() (*v1.APIGroupList, error) {
	return d.serverGroups, nil
}

func (d *MockDiscovery) ServerResourcesForGroupVersion(groupVersion string) (*v1.APIResourceList, error) {
	if res, ok := d.resources[groupVersion]; ok {
		return res, nil
	}
	return nil, nil
}
