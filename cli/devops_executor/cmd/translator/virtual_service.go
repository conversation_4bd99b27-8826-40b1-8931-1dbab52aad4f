package translator

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator/istio"
)

var translateVirtualServiceParams istio.TranslateVirtualServiceParams

var VirtaulServiceCmd = &cobra.Command{
	Use:   "istio/virtualservice",
	Short: "Translate virtualservice resource file for istio.",
	RunE: func(cmd *cobra.Command, args []string) error {
		return istio.TranslateVirtualService(cmd.Context(), translateVirtualServiceParams)
	},
}

func init() {
	txtHosts := "[required] Specify a path, which can be a file or a directory, and each file in the path describes the routing rules for host."
	txtTemplates := "[required] Specify a path, which can be a file or a directory, and each file in the path stores a routing rule template so that it can be referenced repeatedly."
	txtOutput := "[required] Specify the output directory of the VirtualServices resource."

	VirtaulServiceCmd.Flags().SortFlags = false
	VirtaulServiceCmd.Flags().StringVar(&translateVirtualServiceParams.Hosts, "hosts", "", txtHosts)
	VirtaulServiceCmd.Flags().StringVar(&translateVirtualServiceParams.Templates, "templates", "", txtTemplates)
	VirtaulServiceCmd.Flags().StringVarP(&translateVirtualServiceParams.Output, "output", "o", "", txtOutput)
	VirtaulServiceCmd.MarkFlagRequired("hosts")
	VirtaulServiceCmd.MarkFlagRequired("templates")
	VirtaulServiceCmd.MarkFlagRequired("output")
}
