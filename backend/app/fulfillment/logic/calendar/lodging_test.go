package calendar

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	date "google.golang.org/genproto/googleapis/type/date"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	calendarpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// TestLogic_buildCalendarLodgingTypes 测试buildCalendarLodgingTypes函数
func TestLogic_buildCalendarLodgingTypes(t *testing.T) {
	// 创建测试用的时间范围
	startTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2024, 1, 31, 23, 59, 59, 0, time.UTC)
	logic := &Logic{}

	// 准备测试数据
	lodgingTypes := []*offeringpb.LodgingType{
		{
			Id:                1,
			Name:              "Standard Room",
			LodgingUnitType:   offeringpb.LodgingUnitType_ROOM,
			MaxPetNum:         2,
			MaxPetTotalWeight: 50,
			PetSizeFilter:     true,
			PetSizeIds:        []int64{1, 2},
		},
		{
			Id:                2,
			Name:              "Premium Suite",
			LodgingUnitType:   offeringpb.LodgingUnitType_AREA,
			MaxPetNum:         4,
			MaxPetTotalWeight: 100,
			PetSizeFilter:     false,
			PetSizeIds:        []int64{},
		},
	}

	lodgingUnits := []*offeringpb.LodgingUnit{
		{
			Id:            101,
			Name:          "Room A1",
			LodgingTypeId: 1,
		},
		{
			Id:            102,
			Name:          "Room A2",
			LodgingTypeId: 1,
		},
		{
			Id:            201,
			Name:          "Suite B1",
			LodgingTypeId: 2,
		},
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			LodgingID: 101, // 匹配第一个lodging unit (LodgingTypeId: 1)
		},
		{
			ID:        2,
			LodgingID: 201, // 匹配第三个lodging unit (LodgingTypeId: 2)
		},
	}

	t.Run("正常情况", func(t *testing.T) {
		result := logic.buildCalendarLodgingTypes(lodgingTypes, lodgingUnits, fulfillments, startTime, endTime)

		assert.Len(t, result, 2, "应该返回2个LodgingType")

		// 验证第一个LodgingType
		lodgingType1 := result[0]
		assert.Equal(t, int64(1), lodgingType1.Id)
		assert.Equal(t, "Standard Room", lodgingType1.Name)
		assert.Equal(t, int32(offeringpb.LodgingUnitType_ROOM), lodgingType1.UnitType)
		assert.Equal(t, int32(2), lodgingType1.MaxPetNumber)
		assert.Equal(t, int32(50), lodgingType1.MaxPetTotalNumber)
		assert.True(t, lodgingType1.NeedPetSizeFilter)
		assert.Equal(t, []int64{1, 2}, lodgingType1.PetSizeIds)
		assert.NotNil(t, lodgingType1.DailyCapacityUsed)

		// 验证第二个LodgingType
		lodgingType2 := result[1]
		assert.Equal(t, int64(2), lodgingType2.Id)
		assert.Equal(t, "Premium Suite", lodgingType2.Name)
		assert.Equal(t, int32(offeringpb.LodgingUnitType_AREA), lodgingType2.UnitType)
		assert.Equal(t, int32(4), lodgingType2.MaxPetNumber)
		assert.Equal(t, int32(100), lodgingType2.MaxPetTotalNumber)
		assert.False(t, lodgingType2.NeedPetSizeFilter)
		assert.Empty(t, lodgingType2.PetSizeIds)
		assert.NotNil(t, lodgingType2.DailyCapacityUsed)
	})

	t.Run("空输入", func(t *testing.T) {
		result := logic.buildCalendarLodgingTypes(nil, nil, nil, startTime, endTime)
		assert.Empty(t, result)
	})

	t.Run("只有lodgingTypes", func(t *testing.T) {
		result := logic.buildCalendarLodgingTypes(lodgingTypes, nil, nil, startTime, endTime)
		assert.Len(t, result, 2)

		// 验证所有字段都正确设置
		for _, lodgingType := range result {
			assert.NotNil(t, lodgingType.DailyCapacityUsed)
			assert.Len(t, lodgingType.DailyCapacityUsed, 31, "应该包含31天的数据")
		}
	})
}

// TestLogic_buildCalendarLodgingUnits 测试buildCalendarLodgingUnits函数
func TestLogic_buildCalendarLodgingUnits(t *testing.T) {
	logic := &Logic{}

	lodgingUnits := []*offeringpb.LodgingUnit{
		{
			Id:            101,
			Name:          "Room A1",
			LodgingTypeId: 1,
		},
		{
			Id:            102,
			Name:          "Room A2",
			LodgingTypeId: 1,
		},
		{
			Id:            201,
			Name:          "Suite B1",
			LodgingTypeId: 2,
		},
	}

	t.Run("正常情况", func(t *testing.T) {
		result := logic.buildCalendarLodgingUnits(lodgingUnits)

		assert.Len(t, result, 3, "应该返回3个LodgingUnit")

		// 验证第一个LodgingUnit
		unit1 := result[0]
		assert.Equal(t, int64(101), unit1.Id)
		assert.Equal(t, "Room A1", unit1.Name)
		assert.Equal(t, int64(1), unit1.LodgingTypeId)

		// 验证第二个LodgingUnit
		unit2 := result[1]
		assert.Equal(t, int64(102), unit2.Id)
		assert.Equal(t, "Room A2", unit2.Name)
		assert.Equal(t, int64(1), unit2.LodgingTypeId)

		// 验证第三个LodgingUnit
		unit3 := result[2]
		assert.Equal(t, int64(201), unit3.Id)
		assert.Equal(t, "Suite B1", unit3.Name)
		assert.Equal(t, int64(2), unit3.LodgingTypeId)
	})

	t.Run("空输入", func(t *testing.T) {
		result := logic.buildCalendarLodgingUnits(nil)
		assert.Empty(t, result)
	})
}

// TestLogic_buildCalendarLodgingAppointments 测试buildCalendarLodgingAppointments函数
func TestLogic_buildCalendarLodgingAppointments(t *testing.T) {
	logic := &Logic{}

	appointmentStartTime := time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC)
	appointmentEndTime := time.Date(2024, 1, 1, 16, 0, 0, 0, time.UTC)

	appointments := []*appointment.Appointment{
		{
			ID:         1,
			CustomerID: 1001,
			ColorCode:  "#FF0000",
			StartTime:  appointmentStartTime,
			EndTime:    appointmentEndTime,
		},
		{
			ID:         2,
			CustomerID: 1002,
			ColorCode:  "#00FF00",
			StartTime:  appointmentStartTime,
			EndTime:    appointmentEndTime,
		},
		{
			ID:         3,
			CustomerID: 1003,
			ColorCode:  "#0000FF",
			StartTime:  appointmentStartTime,
			EndTime:    appointmentEndTime,
		},
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:                1,
			AppointmentID:     1,
			LodgingID:         101, // 有住宿
			ServiceInstanceID: 2001,
		},
		{
			ID:                2,
			AppointmentID:     2,
			LodgingID:         201, // 有住宿
			ServiceInstanceID: 2002,
		},
		{
			ID:                3,
			AppointmentID:     3,
			LodgingID:         0, // 没有住宿，应该被过滤掉
			ServiceInstanceID: 2003,
		},
	}

	lodgingUnits := []*offeringpb.LodgingUnit{
		{
			Id:            101,
			Name:          "Room A1",
			LodgingTypeId: 1,
		},
		{
			Id:            201,
			Name:          "Suite B1",
			LodgingTypeId: 2,
		},
	}

	t.Run("正常情况", func(t *testing.T) {
		result := logic.buildCalendarLodgingAppointments(appointments, fulfillments, lodgingUnits)

		assert.Len(t, result, 2, "应该返回2个LodgingAppointmentInfo（只有有住宿的）")

		// 验证第一个Appointment
		appointment1 := result[0]
		assert.Equal(t, int64(1), appointment1.Id)
		assert.Equal(t, int64(1001), appointment1.CustomerId)
		assert.Equal(t, "#FF0000", appointment1.ColorCode)
		assert.Equal(t, int64(2001), appointment1.ServiceInstanceId)
		assert.Equal(t, int64(101), appointment1.LodgingUnitId)
		assert.Equal(t, int64(1), appointment1.LodgingTypeId)

		// 验证第二个Appointment
		appointment2 := result[1]
		assert.Equal(t, int64(2), appointment2.Id)
		assert.Equal(t, int64(1002), appointment2.CustomerId)
		assert.Equal(t, "#00FF00", appointment2.ColorCode)
		assert.Equal(t, int64(2002), appointment2.ServiceInstanceId)
		assert.Equal(t, int64(201), appointment2.LodgingUnitId)
		assert.Equal(t, int64(2), appointment2.LodgingTypeId)
	})

	t.Run("空输入", func(t *testing.T) {
		result := logic.buildCalendarLodgingAppointments(nil, nil, nil)
		assert.Empty(t, result)
	})

	t.Run("没有住宿的appointments", func(t *testing.T) {
		fulfillmentsNoLodging := []*fulfillment.Fulfillment{
			{
				ID:                1,
				AppointmentID:     1,
				LodgingID:         0, // 没有住宿
				ServiceInstanceID: 2001,
			},
		}

		result := logic.buildCalendarLodgingAppointments(appointments, fulfillmentsNoLodging, lodgingUnits)
		assert.Empty(t, result, "应该返回空列表，因为没有有住宿的appointments")
	})

	t.Run("appointment去重", func(t *testing.T) {
		// 多个fulfillments指向同一个appointment
		fulfillmentsDuplicate := []*fulfillment.Fulfillment{
			{
				ID:                1,
				AppointmentID:     1,
				LodgingID:         101,
				ServiceInstanceID: 2001,
			},
			{
				ID:                2,
				AppointmentID:     1, // 同一个appointment
				LodgingID:         102,
				ServiceInstanceID: 2002,
			},
		}

		result := logic.buildCalendarLodgingAppointments(appointments, fulfillmentsDuplicate, lodgingUnits)
		assert.Len(t, result, 1, "appointment应该去重，只出现一次")
		assert.Equal(t, int64(1), result[0].Id)
	})
}

// TestLogic_NewByParams 测试NewByParams函数（无数据库依赖）
func TestLogic_NewByParams(t *testing.T) {
	// 使用nil参数测试，不会触发数据库连接
	logic := NewByParams(nil, nil, nil, nil, nil, nil)
	assert.NotNil(t, logic)
	assert.Nil(t, logic.appointmentRepo)
	assert.Nil(t, logic.fulfillmentRepo)
	assert.Nil(t, logic.lodgingReader)
	assert.Nil(t, logic.serviceInstanceCli)
	assert.Nil(t, logic.petCli)
	assert.Nil(t, logic.customerCli)
}

// TestLogic_calculateDailyCapacityUsed 测试DailyCapacityUsed计算功能
func TestLogic_calculateDailyCapacityUsed(t *testing.T) {
	logic := &Logic{}
	startTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2024, 1, 3, 23, 59, 59, 0, time.UTC) // 3天范围

	t.Run("AREA类型 - 计算宠物数量", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_AREA,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
			{Id: 102, LodgingTypeId: 1},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
			{
				ID:        2,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 1, 14, 0, 0, 0, time.UTC),
			},
			{
				ID:        3,
				LodgingID: 102,
				StartTime: time.Date(2024, 1, 2, 9, 0, 0, 0, time.UTC),
			},
		}

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 验证结果
		assert.Len(t, result, 3, "应该有3天的数据")
		assert.Equal(t, "2024-01-01", result[0].Date)
		assert.Equal(t, int32(2), result[0].Used, "第一天应该有2只宠物")
		assert.Equal(t, "2024-01-02", result[1].Date)
		assert.Equal(t, int32(1), result[1].Used, "第二天应该有1只宠物")
		assert.Equal(t, "2024-01-03", result[2].Date)
		assert.Equal(t, int32(0), result[2].Used, "第三天应该没有宠物")
	})

	t.Run("ROOM类型 - 计算占用房间数", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_ROOM,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
			{Id: 102, LodgingTypeId: 1},
			{Id: 103, LodgingTypeId: 1},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
			{
				ID:        2,
				LodgingID: 101, // 同一个房间，多只宠物
				StartTime: time.Date(2024, 1, 1, 14, 0, 0, 0, time.UTC),
			},
			{
				ID:        3,
				LodgingID: 102,
				StartTime: time.Date(2024, 1, 2, 9, 0, 0, 0, time.UTC),
			},
		}

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 验证结果 - ROOM类型只计算被占用的房间数，不计算宠物数
		assert.Len(t, result, 3, "应该有3天的数据")
		assert.Equal(t, "2024-01-01", result[0].Date)
		assert.Equal(t, int32(1), result[0].Used, "第一天应该有1个房间被占用（101房间）")
		assert.Equal(t, "2024-01-02", result[1].Date)
		assert.Equal(t, int32(1), result[1].Used, "第二天应该有1个房间被占用（102房间）")
		assert.Equal(t, "2024-01-03", result[2].Date)
		assert.Equal(t, int32(0), result[2].Used, "第三天应该没有房间被占用")
	})

	t.Run("没有fulfillments的情况", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_AREA,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
		}

		fulfillments := []*fulfillment.Fulfillment{}

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 验证所有日期的值都是0
		assert.Len(t, result, 3, "应该有3天的数据")
		for i, entry := range result {
			assert.Equal(t, int32(0), entry.Used, "日期 %s 的容量使用应该为0", entry.Date)
			if i == 0 {
				assert.Equal(t, "2024-01-01", entry.Date, "第一个条目应该是2024-01-01")
			}
		}
	})

	t.Run("验证日期排序", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_AREA,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
		}

		// 创建跨越多天的fulfillments
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 3, 10, 0, 0, 0, time.UTC), // 最后一天
			},
			{
				ID:        2,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), // 第一天
			},
			{
				ID:        3,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 2, 10, 0, 0, 0, time.UTC), // 中间一天
			},
		}

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 验证结果包含所有日期，并且按日期排序
		assert.Len(t, result, 3, "应该有3天的数据")

		// 验证日期按照时间顺序排列
		assert.Equal(t, "2024-01-01", result[0].Date, "第一个条目应该是2024-01-01")
		assert.Equal(t, int32(1), result[0].Used, "第一天应该有1只宠物")
		assert.Equal(t, "2024-01-02", result[1].Date, "第二个条目应该是2024-01-02")
		assert.Equal(t, int32(1), result[1].Used, "第二天应该有1只宠物")
		assert.Equal(t, "2024-01-03", result[2].Date, "第三个条目应该是2024-01-03")
		assert.Equal(t, int32(1), result[2].Used, "第三天应该有1只宠物")

		// 验证日期是按升序排列的
		for i := 1; i < len(result); i++ {
			assert.True(t, result[i-1].Date < result[i].Date, "日期应该按升序排列: %s 应该在 %s 之前", result[i-1].Date, result[i].Date)
		}
	})

	t.Run("验证日期排序 - 使用有序遍历", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_AREA,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
		}

		// 创建跨越多天的fulfillments，故意打乱顺序
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 3, 10, 0, 0, 0, time.UTC), // 最后一天
			},
			{
				ID:        2,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), // 第一天
			},
			{
				ID:        3,
				LodgingID: 101,
				StartTime: time.Date(2024, 1, 2, 10, 0, 0, 0, time.UTC), // 中间一天
			},
		}

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 验证结果包含所有日期，并且已经按日期排序
		assert.Len(t, result, 3, "应该有3天的数据")

		// 验证每个条目的日期和使用量
		assert.Equal(t, "2024-01-01", result[0].Date, "第一个条目应该是2024-01-01")
		assert.Equal(t, int32(1), result[0].Used, "第一天应该有1只宠物")
		assert.Equal(t, "2024-01-02", result[1].Date, "第二个条目应该是2024-01-02")
		assert.Equal(t, int32(1), result[1].Used, "第二天应该有1只宠物")
		assert.Equal(t, "2024-01-03", result[2].Date, "第三个条目应该是2024-01-03")
		assert.Equal(t, int32(1), result[2].Used, "第三天应该有1只宠物")

		// 验证日期顺序：已经按日期升序排列
		expectedDates := []string{"2024-01-01", "2024-01-02", "2024-01-03"}
		actualDates := make([]string, len(result))
		for i, entry := range result {
			actualDates[i] = entry.Date
		}
		assert.Equal(t, expectedDates, actualDates, "日期应该按顺序排列")
	})

	t.Run("验证JSON输出顺序", func(t *testing.T) {
		lodgingType := &offeringpb.LodgingType{
			Id:              1,
			LodgingUnitType: offeringpb.LodgingUnitType_AREA,
		}

		lodgingUnits := []*offeringpb.LodgingUnit{
			{Id: 101, LodgingTypeId: 1},
		}

		// 创建一些随机日期的fulfillments来测试排序
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				LodgingID: 101,
				StartTime: time.Date(2025, 9, 19, 10, 0, 0, 0, time.UTC), // 2025-09-19
			},
			{
				ID:        2,
				LodgingID: 101,
				StartTime: time.Date(2025, 9, 13, 14, 0, 0, 0, time.UTC), // 2025-09-13
			},
			{
				ID:        3,
				LodgingID: 101,
				StartTime: time.Date(2025, 9, 24, 9, 0, 0, 0, time.UTC), // 2025-09-24
			},
			{
				ID:        4,
				LodgingID: 101,
				StartTime: time.Date(2025, 9, 15, 16, 0, 0, 0, time.UTC), // 2025-09-15
			},
			{
				ID:        5,
				LodgingID: 101,
				StartTime: time.Date(2025, 9, 12, 8, 0, 0, 0, time.UTC), // 2025-09-12
			},
		}

		startTime := time.Date(2025, 9, 12, 0, 0, 0, 0, time.UTC)
		endTime := time.Date(2025, 9, 24, 23, 59, 59, 0, time.UTC)

		result := logic.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		// 序列化为 JSON
		jsonData, err := json.Marshal(result)
		assert.NoError(t, err, "JSON序列化应该成功")

		// 打印JSON输出以供调试
		fmt.Printf("JSON输出: %s\n", string(jsonData))

		// 验证结果的正确性
		assert.Len(t, result, 13, "应该有13天的数据")

		// 创建一个map便于查找
		resultMap := make(map[string]int32)
		for _, entry := range result {
			resultMap[entry.Date] = entry.Used
		}

		assert.Equal(t, int32(1), resultMap["2025-09-12"], "2025-09-12应该有1只宠物")
		assert.Equal(t, int32(1), resultMap["2025-09-13"], "2025-09-13应该有1只宠物")
		assert.Equal(t, int32(0), resultMap["2025-09-14"], "2025-09-14应该没有宠物")
		assert.Equal(t, int32(1), resultMap["2025-09-15"], "2025-09-15应该有1只宠物")
		assert.Equal(t, int32(1), resultMap["2025-09-19"], "2025-09-19应该有1只宠物")
		assert.Equal(t, int32(1), resultMap["2025-09-24"], "2025-09-24应该有1只宠物")

		// 验证数组是按日期排序的
		assert.Equal(t, "2025-09-12", result[0].Date, "第一个条目应该是2025-09-12")
		assert.Equal(t, "2025-09-13", result[1].Date, "第二个条目应该是2025-09-13")
		assert.Equal(t, "2025-09-24", result[12].Date, "最后一个条目应该是2025-09-24")
	})
}

// TestLogic_generateDateRange 测试日期范围生成功能
func TestLogic_generateDateRange(t *testing.T) {
	logic := &Logic{}

	t.Run("生成3天的日期范围", func(t *testing.T) {
		startTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
		endTime := time.Date(2024, 1, 3, 23, 59, 59, 0, time.UTC)

		result := logic.generateDateRange(startTime, endTime)

		expected := []string{"2024-01-01", "2024-01-02", "2024-01-03"}
		assert.Equal(t, expected, result, "日期应该按顺序生成")
	})

	t.Run("生成单天日期范围", func(t *testing.T) {
		startTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
		endTime := time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC)

		result := logic.generateDateRange(startTime, endTime)

		expected := []string{"2024-01-01"}
		assert.Equal(t, expected, result, "单天日期应该正确生成")
	})

	t.Run("跨月日期范围", func(t *testing.T) {
		startTime := time.Date(2024, 1, 30, 0, 0, 0, 0, time.UTC)
		endTime := time.Date(2024, 2, 2, 23, 59, 59, 0, time.UTC)

		result := logic.generateDateRange(startTime, endTime)

		expected := []string{"2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02"}
		assert.Equal(t, expected, result, "跨月日期应该按顺序生成")
	})
}

// MockLodgingReader 简单的mock实现
type MockLodgingReader struct {
	shouldError bool
}

func (m *MockLodgingReader) GetLodgingTypeList(ctx context.Context, companyID int64, filter *offeringpb.LodgingTypeFilter) (*offeringpb.GetLodgingTypeListResponse, error) {
	if m.shouldError {
		return nil, errors.New("mock error")
	}

	// 根据filter返回对应的lodging types
	var lodgingTypes []*offeringpb.LodgingType
	if filter != nil && len(filter.LodgingTypeIds) > 0 {
		// 如果有指定的IDs，返回对应的types
		for _, id := range filter.LodgingTypeIds {
			lodgingTypes = append(lodgingTypes, &offeringpb.LodgingType{
				Id:   id,
				Name: fmt.Sprintf("Mock Room %d", id),
			})
		}
	} else {
		// 默认返回一个lodging type
		lodgingTypes = []*offeringpb.LodgingType{
			{
				Id:   1,
				Name: "Mock Room",
			},
		}
	}

	return &offeringpb.GetLodgingTypeListResponse{
		LodgingTypes: lodgingTypes,
	}, nil
}

func (m *MockLodgingReader) ListLodgingUnit(ctx context.Context, businessID int64) (*offeringpb.ListLodgingUnitResponse, error) {
	if m.shouldError {
		return nil, errors.New("mock error")
	}

	// 返回默认的units
	units := []*offeringpb.LodgingUnit{
		{
			Id:            101,
			Name:          "Mock Unit 1",
			LodgingTypeId: 1,
		},
		{
			Id:            102,
			Name:          "Mock Unit 2",
			LodgingTypeId: 2,
		},
	}

	return &offeringpb.ListLodgingUnitResponse{
		LodgingUnits: units,
	}, nil
}

// TestLogic_getLodgingTypes 测试getLodgingTypes函数
func TestLogic_getLodgingTypes(t *testing.T) {
	ctx := context.Background()

	t.Run("成功获取lodging types", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: false}
		logic := &Logic{lodgingReader: mockReader}

		lodgingTypeIDs := []int64{1, 2}
		result, err := logic.getLodgingTypes(ctx, 100, lodgingTypeIDs)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, int64(1), result[0].Id)
		assert.Equal(t, "Mock Room 1", result[0].Name)
	})

	t.Run("获取lodging types失败", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: true}
		logic := &Logic{lodgingReader: mockReader}

		lodgingTypeIDs := []int64{1}
		result, err := logic.getLodgingTypes(ctx, 100, lodgingTypeIDs)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "mock error")
	})

	t.Run("空lodging type IDs", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: false}
		logic := &Logic{lodgingReader: mockReader}

		result, err := logic.getLodgingTypes(ctx, 100, []int64{})
		assert.NoError(t, err)
		assert.Len(t, result, 1) // 默认返回一个lodging type
		assert.Equal(t, int64(1), result[0].Id)
		assert.Equal(t, "Mock Room", result[0].Name)
	})
}

// TestLogic_getLodgingUnits 测试getLodgingUnits函数
func TestLogic_getLodgingUnits(t *testing.T) {
	ctx := context.Background()

	t.Run("成功获取lodging units", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: false}
		logic := &Logic{lodgingReader: mockReader}

		lodgingIDs := []int64{1, 2}
		result, err := logic.getLodgingUnits(ctx, 200, lodgingIDs)
		assert.NoError(t, err)
		assert.Len(t, result, 2) // 返回默认的2个units
		assert.Equal(t, int64(101), result[0].Id)
		assert.Equal(t, "Mock Unit 1", result[0].Name)
	})

	t.Run("获取lodging units失败", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: true}
		logic := &Logic{lodgingReader: mockReader}

		lodgingIDs := []int64{1}
		result, err := logic.getLodgingUnits(ctx, 200, lodgingIDs)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "mock error")
	})

	t.Run("空lodging IDs", func(t *testing.T) {
		mockReader := &MockLodgingReader{shouldError: false}
		logic := &Logic{lodgingReader: mockReader}

		result, err := logic.getLodgingUnits(ctx, 200, []int64{})
		assert.NoError(t, err)
		assert.Len(t, result, 2) // 现在总是返回默认的units
		assert.Equal(t, int64(101), result[0].Id)
		assert.Equal(t, "Mock Unit 1", result[0].Name)
	})
}

// MockAppointmentRepo 简单的appointment mock实现
type MockAppointmentRepo struct {
	shouldError bool
	data        []*appointment.Appointment
}

func (m *MockAppointmentRepo) List(ctx context.Context, param *appointment.BaseParam, filter *appointment.Filter) ([]*appointment.Appointment, error) {
	if m.shouldError {
		return nil, errors.New("appointment mock error")
	}
	return m.data, nil
}

func (m *MockAppointmentRepo) Count(ctx context.Context, param *appointment.BaseParam, filter *appointment.Filter) (int64, error) {
	return int64(len(m.data)), nil
}

func (m *MockAppointmentRepo) Create(ctx context.Context, appointment *appointment.Appointment) error {
	return nil
}

func (m *MockAppointmentRepo) BatchCreate(ctx context.Context, appointments []*appointment.Appointment) error {
	return nil
}

func (m *MockAppointmentRepo) Update(ctx context.Context, appointment *appointment.Appointment) error {
	return nil
}

func (m *MockAppointmentRepo) GetByID(ctx context.Context, id int) (*appointment.Appointment, error) {
	return nil, nil
}

func (m *MockAppointmentRepo) GetByIDs(ctx context.Context, ids []int64) ([]*appointment.Appointment, error) {
	if m.shouldError {
		return nil, errors.New("appointment mock error")
	}

	var result []*appointment.Appointment
	for _, data := range m.data {
		for _, id := range ids {
			if int64(data.ID) == id {
				result = append(result, data)
				break
			}
		}
	}
	return result, nil
}

// MockFulfillmentRepo 简单的fulfillment mock实现
type MockFulfillmentRepo struct {
	shouldError bool
	data        []*fulfillment.Fulfillment
}

func (m *MockFulfillmentRepo) List(ctx context.Context, param *fulfillment.BaseParam, filter *fulfillment.Filter) ([]*fulfillment.Fulfillment, error) {
	if m.shouldError {
		return nil, errors.New("fulfillment mock error")
	}
	return m.data, nil
}

func (m *MockFulfillmentRepo) Count(ctx context.Context, param *fulfillment.BaseParam, filter *fulfillment.Filter) (int64, error) {
	return int64(len(m.data)), nil
}

func (m *MockFulfillmentRepo) BatchCreate(ctx context.Context, fulfillments []*fulfillment.Fulfillment) error {
	return nil
}

func (m *MockFulfillmentRepo) Update(ctx context.Context, fulfillment *fulfillment.Fulfillment) error {
	return nil
}

func (m *MockFulfillmentRepo) DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error {
	return nil
}

func (m *MockFulfillmentRepo) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*fulfillment.Fulfillment, error) {
	return nil, nil
}

func (m *MockFulfillmentRepo) GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*fulfillment.Fulfillment, error) {
	return nil, nil
}

// TestLogic_queryAppointmentAndFulfillment 测试queryAppointmentAndFulfillment函数
func TestLogic_queryAppointmentAndFulfillment(t *testing.T) {
	ctx := context.Background()

	t.Run("成功查询", func(t *testing.T) {
		appointmentData := []*appointment.Appointment{
			{ID: 1, CustomerID: 1001},
		}
		fulfillmentData := []*fulfillment.Fulfillment{
			{ID: 1, LodgingID: 100, AppointmentID: 1}, // 添加AppointmentID
		}

		logic := &Logic{
			appointmentRepo: &MockAppointmentRepo{data: appointmentData},
			fulfillmentRepo: &MockFulfillmentRepo{data: fulfillmentData},
		}

		req := &calendarpb.GetLodgingCalendarRequest{
			CompanyId:  100,
			BusinessId: 200,
		}

		appointments, fulfillments, err := logic.queryAppointmentAndFulfillment(ctx, req)
		assert.NoError(t, err)
		assert.Len(t, appointments, 1)
		assert.Len(t, fulfillments, 1)
		assert.Equal(t, int(1), appointments[0].ID)
		assert.Equal(t, int64(1), fulfillments[0].ID)
	})

	t.Run("没有fulfillments", func(t *testing.T) {
		logic := &Logic{
			appointmentRepo: &MockAppointmentRepo{},
			fulfillmentRepo: &MockFulfillmentRepo{data: []*fulfillment.Fulfillment{}},
		}

		req := &calendarpb.GetLodgingCalendarRequest{
			CompanyId:  100,
			BusinessId: 200,
		}

		appointments, fulfillments, err := logic.queryAppointmentAndFulfillment(ctx, req)
		assert.NoError(t, err)
		assert.Empty(t, appointments)
		assert.Empty(t, fulfillments)
	})

	t.Run("appointment查询失败", func(t *testing.T) {
		fulfillmentData := []*fulfillment.Fulfillment{
			{ID: 1, LodgingID: 100, AppointmentID: 1},
		}

		logic := &Logic{
			appointmentRepo: &MockAppointmentRepo{shouldError: true},
			fulfillmentRepo: &MockFulfillmentRepo{data: fulfillmentData},
		}

		req := &calendarpb.GetLodgingCalendarRequest{
			CompanyId:  100,
			BusinessId: 200,
		}

		appointments, fulfillments, err := logic.queryAppointmentAndFulfillment(ctx, req)
		assert.Error(t, err)
		assert.Nil(t, appointments)
		assert.Nil(t, fulfillments)
		assert.Contains(t, err.Error(), "appointment mock error")
	})

	t.Run("fulfillment查询失败", func(t *testing.T) {
		logic := &Logic{
			appointmentRepo: &MockAppointmentRepo{},
			fulfillmentRepo: &MockFulfillmentRepo{shouldError: true},
		}

		req := &calendarpb.GetLodgingCalendarRequest{
			CompanyId:  100,
			BusinessId: 200,
		}

		appointments, fulfillments, err := logic.queryAppointmentAndFulfillment(ctx, req)
		assert.Error(t, err)
		assert.Nil(t, appointments)
		assert.Nil(t, fulfillments)
		assert.Contains(t, err.Error(), "fulfillment mock error")
	})
}

// TestLogic_GetLodgingCalendar_EmptyFulfillments 测试GetLodgingCalendar空fulfillments情况
func TestLogic_GetLodgingCalendar_EmptyFulfillments(t *testing.T) {
	ctx := context.Background()

	// 返回空的fulfillment数据
	fulfillmentData := []*fulfillment.Fulfillment{}

	logic := &Logic{
		appointmentRepo: &MockAppointmentRepo{},
		fulfillmentRepo: &MockFulfillmentRepo{data: fulfillmentData},
		lodgingReader:   &MockLodgingReader{},
	}

	req := &calendarpb.GetLodgingCalendarRequest{
		CompanyId:  100,
		BusinessId: 200,
	}

	result, err := logic.GetLodgingCalendar(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	// 现在会返回2个lodging types，因为mock返回2个lodging units，对应2个lodging type IDs
	assert.Len(t, result.LodgingTypes, 2) // Mock会返回2个lodging types

	// 验证lodging type的基本信息（不依赖顺序）
	lodgingTypeMap := make(map[int64]*calendarpb.LodgingType)
	for _, info := range result.LodgingTypes {
		lodgingTypeMap[info.Id] = info
	}

	// 验证ID为1的lodging type
	type1, exists := lodgingTypeMap[1]
	assert.True(t, exists, "应该包含ID为1的lodging type")
	assert.Equal(t, "Mock Room 1", type1.Name)

	// 验证ID为2的lodging type
	type2, exists := lodgingTypeMap[2]
	assert.True(t, exists, "应该包含ID为2的lodging type")
	assert.Equal(t, "Mock Room 2", type2.Name)
}

// MockServiceInstanceCli 简单的service instance mock实现
type MockServiceInstanceCli struct {
	shouldError bool
	data        []*serviceinstance.ServiceInstance
}

func (m *MockServiceInstanceCli) Create(ctx context.Context, si *serviceinstance.ServiceInstance) (int, error) {
	return 0, nil
}

func (m *MockServiceInstanceCli) BatchCreate(ctx context.Context, serviceInstances []*serviceinstance.ServiceInstance) error {
	return nil
}

func (m *MockServiceInstanceCli) Update(ctx context.Context, si *serviceinstance.ServiceInstance) error {
	return nil
}

func (m *MockServiceInstanceCli) List(ctx context.Context, baseParam *serviceinstance.BaseParam, filter *serviceinstance.Filter) ([]*serviceinstance.ServiceInstance, error) {
	return nil, nil
}

func (m *MockServiceInstanceCli) GetByID(ctx context.Context, id int) (*serviceinstance.ServiceInstance, error) {
	return nil, nil
}

func (m *MockServiceInstanceCli) GetByIDs(ctx context.Context, ids []int64) ([]*serviceinstance.ServiceInstance, error) {
	return nil, nil
}

func (m *MockServiceInstanceCli) Delete(ctx context.Context, id int) error {
	return nil
}

func (m *MockServiceInstanceCli) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*serviceinstance.ServiceInstance, error) {
	return nil, nil
}

func (m *MockServiceInstanceCli) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*serviceinstance.ServiceInstance, error) {
	if m.shouldError {
		return nil, errors.New("service instance mock error")
	}

	var result []*serviceinstance.ServiceInstance
	for _, data := range m.data {
		for _, id := range appointmentIDs {
			if int64(data.AppointmentID) == id {
				result = append(result, data)
				break
			}
		}
	}
	return result, nil
}

func (m *MockServiceInstanceCli) GetLatestByPetID(ctx context.Context, businessID, companyID int32, petID int64) (*serviceinstance.ServiceInstance, error) {
	if m.shouldError {
		return nil, errors.New("service instance mock error")
	}
	return nil, nil
}

// TestLogic_getServiceInstances 测试getServiceInstances函数
func TestLogic_getServiceInstances(t *testing.T) {
	ctx := context.Background()

	t.Run("成功获取ServiceInstances", func(t *testing.T) {
		// 准备测试数据
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         1001,
				CareType:      1,
				DateType:      1,
				RootParentID:  0,
				StartDate:     serviceinstance.TimeToPgDate(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
				EndDate:       serviceinstance.TimeToPgDate(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
				SpecificDates: serviceinstance.DateSlice{
					time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
					time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
				},
			},
			{
				ID:            2,
				AppointmentID: 2,
				PetID:         1002,
				CareType:      2,
				DateType:      2,
				RootParentID:  10, // 有root_parent_id，表示是分割住宿
				StartDate:     serviceinstance.TimeToPgDate(time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC)),
				EndDate:       serviceinstance.TimeToPgDate(time.Date(2024, 1, 7, 0, 0, 0, 0, time.UTC)),
				SpecificDates: nil,
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     101, // lodgingId > 0
			},
			{
				ID:            2,
				AppointmentID: 2,
				LodgingID:     102, // lodgingId > 0
			},
			{
				ID:            3,
				AppointmentID: 3,
				LodgingID:     0, // lodgingId = 0，应该被过滤掉
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.NoError(t, err)
		assert.Len(t, result, 2, "应该返回2个ServiceInstance")

		// 验证第一个ServiceInstance
		si1 := result[0]
		assert.Equal(t, int64(1), si1.Id)
		assert.False(t, si1.IsSplitLodging, "RootParentID为0，应该不是分割住宿")
		assert.Equal(t, calendarpb.DateType(1), si1.DateType)
		assert.Equal(t, offeringpb.CareCategory(1), si1.CareType)
		assert.NotNil(t, si1.StartDate)
		assert.Equal(t, int32(2024), si1.StartDate.Year)
		assert.Equal(t, int32(1), si1.StartDate.Month)
		assert.Equal(t, int32(1), si1.StartDate.Day)
		assert.NotNil(t, si1.EndDate)
		assert.Equal(t, int32(2024), si1.EndDate.Year)
		assert.Equal(t, int32(1), si1.EndDate.Month)
		assert.Equal(t, int32(3), si1.EndDate.Day)
		assert.Len(t, si1.SpecificDates, 2, "应该有2个特定日期")

		// 验证第二个ServiceInstance
		si2 := result[1]
		assert.Equal(t, int64(2), si2.Id)
		assert.True(t, si2.IsSplitLodging, "RootParentID不为0，应该是分割住宿")
		assert.Equal(t, calendarpb.DateType(2), si2.DateType)
		assert.Equal(t, offeringpb.CareCategory(2), si2.CareType)
		assert.NotNil(t, si2.StartDate)
		assert.Equal(t, int32(2024), si2.StartDate.Year)
		assert.Equal(t, int32(1), si2.StartDate.Month)
		assert.Equal(t, int32(5), si2.StartDate.Day)
		assert.NotNil(t, si2.EndDate)
		assert.Equal(t, int32(2024), si2.EndDate.Year)
		assert.Equal(t, int32(1), si2.EndDate.Month)
		assert.Equal(t, int32(7), si2.EndDate.Day)
		assert.Empty(t, si2.SpecificDates, "应该没有特定日期")
	})

	t.Run("没有符合条件的fulfillments", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     0, // lodgingId = 0，应该被过滤掉
			},
			{
				ID:            2,
				AppointmentID: 0, // appointmentId = 0，应该被过滤掉
				LodgingID:     101,
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.NoError(t, err)
		assert.Empty(t, result, "应该返回空的ServiceInstance列表")
	})

	t.Run("空的fulfillments", func(t *testing.T) {
		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{},
		}

		result, err := logic.getServiceInstances(ctx, []*fulfillment.Fulfillment{})
		assert.NoError(t, err)
		assert.Empty(t, result, "应该返回空的ServiceInstance列表")
	})

	t.Run("nil fulfillments", func(t *testing.T) {
		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{},
		}

		result, err := logic.getServiceInstances(ctx, nil)
		assert.NoError(t, err)
		assert.Empty(t, result, "应该返回空的ServiceInstance列表")
	})

	t.Run("ServiceInstance查询失败", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     101,
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{shouldError: true},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "service instance mock error")
	})

	t.Run("测试appointmentID去重", func(t *testing.T) {
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         1001,
				CareType:      1,
				DateType:      1,
			},
		}

		// 多个fulfillments指向同一个appointment
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     101,
			},
			{
				ID:            2,
				AppointmentID: 1, // 同一个appointment
				LodgingID:     102,
			},
			{
				ID:            3,
				AppointmentID: 1, // 同一个appointment
				LodgingID:     103,
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.NoError(t, err)
		assert.Len(t, result, 1, "即使有多个fulfillments指向同一个appointment，ServiceInstance也应该只返回一个")
	})

	t.Run("测试日期转换 - 无效日期", func(t *testing.T) {
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         1001,
				CareType:      1,
				DateType:      1,
				RootParentID:  0,
				StartDate:     serviceinstance.TimeToPgDate(time.Time{}), // 无效日期
				EndDate:       serviceinstance.TimeToPgDate(time.Time{}), // 无效日期
				SpecificDates: nil,
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     101,
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.NoError(t, err)
		assert.Len(t, result, 1)

		si := result[0]
		assert.Nil(t, si.StartDate, "无效的StartDate应该为nil")
		assert.Nil(t, si.EndDate, "无效的EndDate应该为nil")
	})

	t.Run("测试日期转换 - 空SpecificDates", func(t *testing.T) {
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         1001,
				CareType:      1,
				DateType:      1,
				RootParentID:  0,
				StartDate:     serviceinstance.TimeToPgDate(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
				EndDate:       serviceinstance.TimeToPgDate(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
				SpecificDates: serviceinstance.DateSlice{}, // 空的特定日期
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				AppointmentID: 1,
				LodgingID:     101,
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		result, err := logic.getServiceInstances(ctx, fulfillments)
		assert.NoError(t, err)
		assert.Len(t, result, 1)

		si := result[0]
		assert.Empty(t, si.SpecificDates, "空的SpecificDates应该返回空切片")
	})
}

// TestLogic_getPetIDsFromAppointments 测试getPetIDsFromAppointments函数
func TestLogic_getPetIDsFromAppointments(t *testing.T) {
	ctx := context.Background()

	t.Run("成功获取petIDs", func(t *testing.T) {
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         1001,
			},
			{
				ID:            2,
				AppointmentID: 1,
				PetID:         1002,
			},
			{
				ID:            3,
				AppointmentID: 2,
				PetID:         1001, // 重复的petID，应该去重
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		appointmentIDs := []int64{1, 2}
		result, err := logic.getPetIDsFromAppointments(ctx, appointmentIDs)
		assert.NoError(t, err)
		assert.Len(t, result, 2, "应该返回2个去重后的petID")
		assert.Contains(t, result, int64(1001))
		assert.Contains(t, result, int64(1002))
	})

	t.Run("空的appointmentIDs", func(t *testing.T) {
		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{},
		}

		result, err := logic.getPetIDsFromAppointments(ctx, []int64{})
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("nil appointmentIDs", func(t *testing.T) {
		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{},
		}

		result, err := logic.getPetIDsFromAppointments(ctx, nil)
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("ServiceInstance查询失败", func(t *testing.T) {
		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{shouldError: true},
		}

		appointmentIDs := []int64{1}
		result, err := logic.getPetIDsFromAppointments(ctx, appointmentIDs)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "service instance mock error")
	})

	t.Run("没有有效的petID", func(t *testing.T) {
		serviceInstanceData := []*serviceinstance.ServiceInstance{
			{
				ID:            1,
				AppointmentID: 1,
				PetID:         0, // 无效的petID
			},
		}

		logic := &Logic{
			serviceInstanceCli: &MockServiceInstanceCli{data: serviceInstanceData},
		}

		appointmentIDs := []int64{1}
		result, err := logic.getPetIDsFromAppointments(ctx, appointmentIDs)
		assert.NoError(t, err)
		assert.Empty(t, result, "应该返回空的petID列表")
	})
}

// TestLogic_analyzeFulfillmentData 测试analyzeFulfillmentData函数
func TestLogic_analyzeFulfillmentData(t *testing.T) {
	logic := &Logic{}

	t.Run("正常情况 - 分析fulfillment数据", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				PetID:     1001,
				CareType:  1, // BOARDING
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
			{
				ID:        2,
				PetID:     1002,
				CareType:  2, // DAYCARE
				StartTime: time.Date(2024, 1, 1, 14, 0, 0, 0, time.UTC),
			},
			{
				ID:        3,
				PetID:     1001, // 同一只宠物，不同日期
				CareType:  1,
				StartTime: time.Date(2024, 1, 2, 9, 0, 0, 0, time.UTC),
			},
			{
				ID:        4,
				PetID:     1003,
				CareType:  3, // 其他类型
				StartTime: time.Date(2024, 1, 2, 11, 0, 0, 0, time.UTC),
			},
		}

		dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

		// 验证总宠物数
		assert.Equal(t, 3, totalPetCount, "应该有3只不同的宠物")

		// 验证每日统计
		assert.Len(t, dailyCounts, 2, "应该有2天的数据")

		// 验证第一天的数据
		day1Count := dailyCounts[0]
		assert.Equal(t, "2024-01-01", day1Count.Date)
		assert.Equal(t, int32(2), day1Count.PetCountWithLodging, "第一天应该有2只需要住宿的宠物")
		assert.Equal(t, int32(1), day1Count.PetCareTypeCount[1], "第一天应该有1只BOARDING宠物")
		assert.Equal(t, int32(1), day1Count.PetCareTypeCount[2], "第一天应该有1只DAYCARE宠物")

		// 验证第二天的数据
		day2Count := dailyCounts[1]
		assert.Equal(t, "2024-01-02", day2Count.Date)
		assert.Equal(t, int32(1), day2Count.PetCountWithLodging, "第二天应该有1只需要住宿的宠物")
		assert.Equal(t, int32(1), day2Count.PetCareTypeCount[1], "第二天应该有1只BOARDING宠物")
		assert.Equal(t, int32(1), day2Count.PetCareTypeCount[3], "第二天应该有1只其他类型宠物")
	})

	t.Run("空的fulfillments", func(t *testing.T) {
		dailyCounts, totalPetCount := logic.analyzeFulfillmentData([]*fulfillment.Fulfillment{})
		assert.Empty(t, dailyCounts)
		assert.Equal(t, 0, totalPetCount)
	})

	t.Run("nil fulfillments", func(t *testing.T) {
		dailyCounts, totalPetCount := logic.analyzeFulfillmentData(nil)
		assert.Empty(t, dailyCounts)
		assert.Equal(t, 0, totalPetCount)
	})

	t.Run("验证日期排序", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:        1,
				PetID:     1001,
				CareType:  1,
				StartTime: time.Date(2024, 1, 3, 10, 0, 0, 0, time.UTC), // 第三天
			},
			{
				ID:        2,
				PetID:     1002,
				CareType:  1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), // 第一天
			},
			{
				ID:        3,
				PetID:     1003,
				CareType:  1,
				StartTime: time.Date(2024, 1, 2, 10, 0, 0, 0, time.UTC), // 第二天
			},
		}

		dailyCounts, _ := logic.analyzeFulfillmentData(fulfillments)

		// 验证日期按升序排列
		assert.Len(t, dailyCounts, 3)
		assert.Equal(t, "2024-01-01", dailyCounts[0].Date)
		assert.Equal(t, "2024-01-02", dailyCounts[1].Date)
		assert.Equal(t, "2024-01-03", dailyCounts[2].Date)
	})
}

// TestLogic_createPetAppointmentMap 测试createPetAppointmentMap函数
func TestLogic_createPetAppointmentMap(t *testing.T) {
	logic := &Logic{}

	t.Run("正常情况 - 创建pet到appointment的映射", func(t *testing.T) {
		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
			{
				ID:        2,
				StartTime: time.Date(2024, 1, 2, 14, 0, 0, 0, time.UTC),
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         1001,
				AppointmentID: 1,
			},
			{
				ID:            2,
				PetID:         1002,
				AppointmentID: 1, // 同一个appointment，不同宠物
			},
			{
				ID:            3,
				PetID:         1003,
				AppointmentID: 2,
			},
		}

		result := logic.createPetAppointmentMap(appointments, fulfillments)

		// 验证映射结果
		assert.Len(t, result, 3, "应该有3个宠物的映射")

		// 验证每个宠物的appointment时间
		assert.Equal(t, time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), result[1001])
		assert.Equal(t, time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), result[1002])
		assert.Equal(t, time.Date(2024, 1, 2, 14, 0, 0, 0, time.UTC), result[1003])
	})

	t.Run("空的appointments", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         1001,
				AppointmentID: 1,
			},
		}

		result := logic.createPetAppointmentMap([]*appointment.Appointment{}, fulfillments)
		assert.Empty(t, result, "应该返回空的映射")
	})

	t.Run("空的fulfillments", func(t *testing.T) {
		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
		}

		result := logic.createPetAppointmentMap(appointments, []*fulfillment.Fulfillment{})
		assert.Empty(t, result, "应该返回空的映射")
	})

	t.Run("nil appointments", func(t *testing.T) {
		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         1001,
				AppointmentID: 1,
			},
		}

		result := logic.createPetAppointmentMap(nil, fulfillments)
		assert.Empty(t, result, "应该返回空的映射")
	})

	t.Run("nil fulfillments", func(t *testing.T) {
		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
		}

		result := logic.createPetAppointmentMap(appointments, nil)
		assert.Empty(t, result, "应该返回空的映射")
	})

	t.Run("无效的fulfillment数据", func(t *testing.T) {
		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         0, // 无效的petID
				AppointmentID: 1,
			},
			{
				ID:            2,
				PetID:         1001,
				AppointmentID: 0, // 无效的appointmentID
			},
			{
				ID:            3,
				PetID:         1002,
				AppointmentID: 999, // 不存在的appointmentID
			},
		}

		result := logic.createPetAppointmentMap(appointments, fulfillments)
		assert.Empty(t, result, "应该返回空的映射，因为所有fulfillment数据都无效")
	})
}

// TestLogic_calculateVaccineStatus 测试calculateVaccineStatus函数
func TestLogic_calculateVaccineStatus(t *testing.T) {
	logic := &Logic{}

	t.Run("疫苗有效 - startDate < expirationDate", func(t *testing.T) {
		startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
		expirationDate := &date.Date{
			Year:  2024,
			Month: 2,
			Day:   1,
		}

		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_ACTIVE, result)
	})

	t.Run("疫苗过期 - startDate >= expirationDate", func(t *testing.T) {
		startDate := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)
		expirationDate := &date.Date{
			Year:  2024,
			Month: 2,
			Day:   1,
		}

		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_EXPIRED, result)
	})

	t.Run("疫苗过期 - startDate > expirationDate", func(t *testing.T) {
		startDate := time.Date(2024, 2, 2, 0, 0, 0, 0, time.UTC)
		expirationDate := &date.Date{
			Year:  2024,
			Month: 2,
			Day:   1,
		}

		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_EXPIRED, result)
	})

	t.Run("疫苗即将过期 - 30天内过期", func(t *testing.T) {
		// 设置当前时间为2024-01-15，过期时间为2024-02-01
		// 这样expirationDate.Add(30Day) = 2024-03-02，大于当前时间
		startDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
		expirationDate := &date.Date{
			Year:  2024,
			Month: 2,
			Day:   1,
		}

		// 由于这个测试依赖于当前时间，我们需要模拟时间
		// 这里我们测试startDate < expirationDate的情况，应该返回ACTIVE
		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_ACTIVE, result)
	})

	t.Run("nil expirationDate", func(t *testing.T) {
		startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)

		result := logic.calculateVaccineStatus(startDate, nil)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_UNSPECIFIED, result)
	})

	t.Run("边界情况 - 同一天", func(t *testing.T) {
		startDate := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC) // 中午
		expirationDate := &date.Date{
			Year:  2024,
			Month: 1,
			Day:   1,
		}

		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_EXPIRED, result)
	})

	t.Run("跨年情况", func(t *testing.T) {
		startDate := time.Date(2023, 12, 31, 0, 0, 0, 0, time.UTC)
		expirationDate := &date.Date{
			Year:  2024,
			Month: 1,
			Day:   1,
		}

		result := logic.calculateVaccineStatus(startDate, expirationDate)
		assert.Equal(t, calendarpb.VaccineStatus_VACCINE_STATUS_ACTIVE, result)
	})
}

// MockPetCli 简单的pet mock实现
type MockPetCli struct {
	shouldError bool
	data        []*businesscustomerpb.BusinessCustomerPetInfoModel
}

func (m *MockPetCli) GetPetInfo(ctx context.Context, companyID, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	if m.shouldError {
		return nil, errors.New("pet mock error")
	}
	for _, data := range m.data {
		if data.GetId() == petID {
			return data, nil
		}
	}
	return nil, errors.New("pet not found")
}

func (m *MockPetCli) BatchGetPetInfo(ctx context.Context, companyID int64, petIDs []int64) ([]*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	if m.shouldError {
		return nil, errors.New("pet mock error")
	}

	var result []*businesscustomerpb.BusinessCustomerPetInfoModel
	for _, data := range m.data {
		for _, id := range petIDs {
			if data.GetId() == id {
				result = append(result, data)
				break
			}
		}
	}
	return result, nil
}

// MockCustomerCli 简单的customer mock实现
type MockCustomerCli struct {
	shouldError bool
	vaccineData *businesscustomersvcpb.ListPetVaccineRecordResponse
}

func (m *MockCustomerCli) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error) {
	return nil, errors.New("not implemented")
}

func (m *MockCustomerCli) BatchCreateFeedingSchedule(ctx context.Context, companyID int64, feedingSchedules []*businesscustomerpb.BusinessPetFeedingScheduleDef) (*businesscustomersvcpb.BatchCreateFeedingScheduleResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockCustomerCli) BatchCreateMedicationSchedule(ctx context.Context, companyID int64, medicationSchedules []*businesscustomerpb.BusinessPetMedicationScheduleDef) (*businesscustomersvcpb.BatchCreateMedicationScheduleResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockCustomerCli) ListPetFeedingSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetFeedingScheduleResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockCustomerCli) ListPetMedicationSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetMedicationScheduleResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockCustomerCli) ListPetVaccineRecord(ctx context.Context, companyID, petID int64) (*businesscustomersvcpb.ListPetVaccineRecordResponse, error) {
	if m.shouldError {
		return nil, errors.New("customer mock error")
	}
	return m.vaccineData, nil
}

// TestLogic_getPetInfos 测试getPetInfos函数
func TestLogic_getPetInfos(t *testing.T) {
	ctx := context.Background()

	t.Run("成功获取宠物信息", func(t *testing.T) {
		petData := []*businesscustomerpb.BusinessCustomerPetInfoModel{
			{
				Id:         1001,
				PetName:    "小白",
				PetType:    1, // 狗
				AvatarPath: "/path/to/avatar1.jpg",
			},
			{
				Id:         1002,
				PetName:    "小黑",
				PetType:    2, // 猫
				AvatarPath: "/path/to/avatar2.jpg",
			},
		}

		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         1001,
				AppointmentID: 1,
			},
			{
				ID:            2,
				PetID:         1002,
				AppointmentID: 1,
			},
		}

		logic := &Logic{
			petCli:      &MockPetCli{data: petData},
			customerCli: &MockCustomerCli{}, // 空的疫苗数据
		}

		petIDs := []int64{1001, 1002}
		result, err := logic.getPetInfos(ctx, 100, petIDs, appointments, fulfillments)
		assert.NoError(t, err)
		assert.Len(t, result, 2)

		// 验证第一个宠物信息
		pet1 := result[0]
		assert.Equal(t, int64(1001), pet1.Id)
		assert.Equal(t, "小白", pet1.Name)
		assert.Equal(t, int32(1), pet1.Type)
		assert.Equal(t, "/path/to/avatar1.jpg", pet1.AvatarPath)
		assert.Empty(t, pet1.Vaccines, "应该没有疫苗信息")

		// 验证第二个宠物信息
		pet2 := result[1]
		assert.Equal(t, int64(1002), pet2.Id)
		assert.Equal(t, "小黑", pet2.Name)
		assert.Equal(t, int32(2), pet2.Type)
		assert.Equal(t, "/path/to/avatar2.jpg", pet2.AvatarPath)
		assert.Empty(t, pet2.Vaccines, "应该没有疫苗信息")
	})

	t.Run("空的petIDs", func(t *testing.T) {
		logic := &Logic{
			petCli:      &MockPetCli{},
			customerCli: &MockCustomerCli{},
		}

		result, err := logic.getPetInfos(ctx, 100, []int64{}, nil, nil)
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("nil petIDs", func(t *testing.T) {
		logic := &Logic{
			petCli:      &MockPetCli{},
			customerCli: &MockCustomerCli{},
		}

		result, err := logic.getPetInfos(ctx, 100, nil, nil, nil)
		assert.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("pet查询失败", func(t *testing.T) {
		logic := &Logic{
			petCli:      &MockPetCli{shouldError: true},
			customerCli: &MockCustomerCli{},
		}

		petIDs := []int64{1001}
		result, err := logic.getPetInfos(ctx, 100, petIDs, nil, nil)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "pet mock error")
	})

	t.Run("疫苗信息获取失败但不影响整体流程", func(t *testing.T) {
		petData := []*businesscustomerpb.BusinessCustomerPetInfoModel{
			{
				Id:         1001,
				PetName:    "小白",
				PetType:    1,
				AvatarPath: "/path/to/avatar1.jpg",
			},
		}

		appointments := []*appointment.Appointment{
			{
				ID:        1,
				StartTime: time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			},
		}

		fulfillments := []*fulfillment.Fulfillment{
			{
				ID:            1,
				PetID:         1001,
				AppointmentID: 1,
			},
		}

		logic := &Logic{
			petCli:      &MockPetCli{data: petData},
			customerCli: &MockCustomerCli{shouldError: true}, // 疫苗查询失败
		}

		petIDs := []int64{1001}
		result, err := logic.getPetInfos(ctx, 100, petIDs, appointments, fulfillments)
		assert.NoError(t, err, "疫苗查询失败不应该影响整体流程")
		assert.Len(t, result, 1)
		assert.Equal(t, int64(1001), result[0].Id)
		assert.Empty(t, result[0].Vaccines, "疫苗查询失败时应该返回空的疫苗列表")
	})
}
