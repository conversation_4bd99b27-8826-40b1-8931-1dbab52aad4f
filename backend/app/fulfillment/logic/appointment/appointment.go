package appointment

import (
	"context"
	"encoding/json"
	"time"

	"github.com/gogo/protobuf/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointmentSvc "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/business"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	feedingRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/servicecharge"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/offering"
	metadata "github.com/MoeGolibrary/moego/backend/app/fulfillment/utils/metadata"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	updateMessage = "更新成功"
	defaultOffset = 0
	defaultLimit  = 200
)

// ServiceInstanceGenerator 服务实例生成器参数
type ServiceInstanceGenerator struct {
	Req                  *pb.CreateAppointmentRequest
	AppointmentID        int
	PetID                int
	ServiceInstance      *pb.CreateServiceInstanceDef
	CareType             offeringpb.CareCategory
	ParentID             int
	RootParentID         int
	MainServiceStartTime time.Time
	MainServiceEndTime   time.Time
	DateType             int
	WorkMode             int
	Price                int
	Duration             int
}

func New() *Logic {
	tx := db.NewTxManager()

	return &Logic{
		appointmentCli:      appointment.New(),
		fulfillmentCli:      fulfillmentRepo.New(),
		serviceInstanceCli:  serviceinstance.New(),
		offeringServiceCli:  offering.New(),
		offeringCareTypeCli: offering.NewCareTypeReader(),
		customerCli:         customer.New(),
		feedingCli:          feedingRepo.New(),
		medicationCli:       medication.New(),
		serviceChargeCli:    servicecharge.New(),
		appointmentSvcCli:   appointmentSvc.New(),
		businessCli:         business.NewAPI(),
		tx:                  tx,
	}
}

func NewByParams(
	appointmentCli appointment.ReadWriter,
	fulfillmentCli fulfillmentRepo.ReadWriter,
	serviceInstanceCli serviceinstance.ReadWriter,
	offeringServiceCli offering.ReadWriter,
	offeringCareTypeCli offering.CareTypeReader,
	customerCli customer.ReadWriter,
	feedingCli feedingRepo.ReadWriter,
	medicationCli medication.ReadWriter,
	serviceChargeCli servicecharge.ReadWriter,
	appointmentSvcCli appointmentSvc.ReadWriter,
	businessCli business.API,
	tx db.TransactionManager,
) *Logic {
	return &Logic{
		appointmentCli:      appointmentCli,
		fulfillmentCli:      fulfillmentCli,
		serviceInstanceCli:  serviceInstanceCli,
		offeringServiceCli:  offeringServiceCli,
		offeringCareTypeCli: offeringCareTypeCli,
		customerCli:         customerCli,
		feedingCli:          feedingCli,
		medicationCli:       medicationCli,
		serviceChargeCli:    serviceChargeCli,
		appointmentSvcCli:   appointmentSvcCli,
		businessCli:         businessCli,
		tx:                  tx,
	}
}

type Logic struct {
	appointmentCli      appointment.ReadWriter
	fulfillmentCli      fulfillmentRepo.ReadWriter
	serviceInstanceCli  serviceinstance.ReadWriter
	offeringServiceCli  offering.ReadWriter
	offeringCareTypeCli offering.CareTypeReader
	customerCli         customer.ReadWriter
	feedingCli          feedingRepo.ReadWriter
	medicationCli       medication.ReadWriter
	serviceChargeCli    servicecharge.ReadWriter
	appointmentSvcCli   appointmentSvc.ReadWriter
	businessCli         business.API
	tx                  db.TransactionManager
}

type ServiceInstanceData struct {
	ServiceInstanceID        int64
	CreateServiceInstanceDef *pb.CreateServiceInstanceDef
	ServiceInstance          *serviceinstance.ServiceInstance
}

// PetScheduleData 收集的宠物计划数据
type PetScheduleData struct {
	FeedingRecords    []*feedingRepo.AppointmentPetFeeding
	MedicationRecords []*medication.AppointmentPetMedication
}

func (l *Logic) UpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest) (
	*pb.UpdateAppointmentResponse, error) {
	// 校验请求
	if err := verifyUpdateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 验证预约是否存在
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, int(req.GetAppointmentId()))
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "appointment not found: %v", err)
	}
	// 执行更新操作
	success, message, err := l.doUpdateAppointment(ctx, req, appointmentEntity)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateAppointmentResponse{
		Success: success,
		Message: message,
	}, nil
}

func (l *Logic) doUpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) (bool, string, error) {
	var success bool
	var message string

	// 正常环境，使用事务处理

	err := l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			// 更新预约基本信息
			if err := l.updateAppointmentBasicInfo(opCtx, req, appointmentEntity); err != nil {
				return err
			}
			// 处理服务操作
			if err := l.processServiceOperations(opCtx, req.GetServiceOperations(),
				int(req.GetAppointmentId()), req, appointmentEntity); err != nil {
				return err
			}
			// 重新计算ServiceItemType并更新
			if err := l.recalculateAndUpdateServiceItemType(opCtx, int(req.GetAppointmentId())); err != nil {
				return err
			}
			success = true
			message = updateMessage

			return nil
		},
	})
	if err != nil {
		return false, err.Error(), err
	}

	return success, message, nil
}

func (l *Logic) updateAppointmentBasicInfo(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	operations := req.GetAppointmentOperation()
	if operations == nil {
		return nil
	}
	// 更新开始时间
	if operations.StartTime != nil {
		appointmentEntity.StartTime = operations.StartTime.AsTime()
	}
	// 更新结束时间
	if operations.EndTime != nil {
		appointmentEntity.EndTime = operations.EndTime.AsTime()
	}
	// 更新颜色代码
	if operations.ColorCode != nil {
		appointmentEntity.ColorCode = operations.GetColorCode()
	}
	// 更新状态
	if operations.NewStatus != nil {
		appointmentEntity.Status = int(operations.GetNewStatus())
	}

	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func (l *Logic) processServiceOperations(ctx context.Context,
	serviceOperations []*pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	for _, operation := range serviceOperations {
		switch operation.GetOperationMode() {
		case pb.OperationMode_OPERATION_MODE_CREATE:
			if err := l.createServiceInstance(ctx, operation, appointmentID, req, appointmentEntity); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_UPDATE:
			if err := l.updateServiceInstance(ctx, operation); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_DELETE:
			if err := l.deleteServiceInstance(ctx, operation); err != nil {
				return err
			}
		}
	}

	return nil
}

/*
		createServiceInstance
		1. 新建一个serviceInstance，在serviceOperation填充新建信息即可，不用填充serviceOperation里面的parent_service_instance_id
	    2. 新建一个subServiceInstance（additionalService）
	       a. 如果其父serviceInstance存在，则需要新建一个serviceOperation，在里面填充parent_service_instance_id和subServiceInstance内容
	       b. 如果其父serviceInstance不存在，则需要先新建一个serviceOperation，先填充父serviceInstance信息，
	          然后在serviceOperation的subServiceInstance字段
*/
func (l *Logic) createServiceInstance(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	// 获取careType
	careType, err := l.getCareTypeByServiceTemplateID(ctx, operation.GetId())
	if err != nil {
		return err
	}
	// 创建主服务实例
	serviceInstance, err := l.buildServiceInstance(operation, appointmentID, req, appointmentEntity, careType)
	if err != nil {
		return err
	}
	// 设置父子关系
	if err := l.setParentChildRelationship(ctx, serviceInstance, operation); err != nil {
		return err
	}
	// 创建服务实例
	serviceInstanceID, err := l.serviceInstanceCli.Create(ctx, serviceInstance)
	if err != nil {
		return err
	}
	// 生成履约记录
	if err := l.createFulfillmentsForServiceInstance(ctx, serviceInstance, careType); err != nil {
		return err
	}
	// 处理子服务实例
	if err := l.processSubServiceInstances(ctx, operation, serviceInstanceID,
		appointmentID, req, appointmentEntity); err != nil {
		return err
	}

	return nil
}

// buildServiceInstance 构建服务实例
func (l *Logic) buildServiceInstance(operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, _ *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment,
	careType offeringpb.CareCategory) (*serviceinstance.ServiceInstance, error) {
	serviceInstance := &serviceinstance.ServiceInstance{
		CustomerID:       appointmentEntity.CustomerID,
		AppointmentID:    appointmentID,
		PetID:            0, // TODO: 需要从operation中获取pet_id，或者从现有数据中推断
		CareType:         int(careType),
		DateType:         int(operation.GetDateScheduleConfig().GetDateType()),
		ServiceFactoryID: int(operation.GetId()),
		StartDate:        serviceinstance.TimeToPgDate(operation.GetTimeConfig().GetStartTime().AsTime()),
		EndDate:          serviceinstance.TimeToPgDate(operation.GetTimeConfig().GetEndTime().AsTime()),
		WorkMode:         int(operation.GetWorkMode()),
		Price:            int(operation.GetPrice()),
		Duration:         int(operation.GetDuration()),
	}

	return serviceInstance, nil
}

// setParentChildRelationship 设置父子关系
func (l *Logic) setParentChildRelationship(ctx context.Context, serviceInstance *serviceinstance.ServiceInstance,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetParentServiceInstanceId() <= 0 {
		return nil
	}

	serviceInstance.ParentID = int(operation.GetParentServiceInstanceId())

	// 获取父服务的RootID，如果没有则使用父服务ID作为RootID
	parentService, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetParentServiceInstanceId()))
	if err != nil {
		return err
	}

	if parentService.RootParentID > 0 {
		serviceInstance.RootParentID = parentService.RootParentID
	} else {
		serviceInstance.RootParentID = parentService.ID
	}

	return nil
}

// createFulfillmentsForServiceInstance 为服务实例创建履约记录
func (l *Logic) createFulfillmentsForServiceInstance(ctx context.Context,
	serviceInstance *serviceinstance.ServiceInstance,
	careType offeringpb.CareCategory) error {
	fulfillments := l.generateFulfillmentRecords(serviceInstance, careType)
	if len(fulfillments) > 0 {
		if err := l.fulfillmentCli.BatchCreate(ctx, fulfillments); err != nil {
			return err
		}
	}

	return nil
}

// processSubServiceInstances 处理子服务实例
func (l *Logic) processSubServiceInstances(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	serviceInstanceID int, appointmentID int, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	for _, subOperation := range operation.GetSubServiceInstances() {
		subOperation.ParentServiceInstanceId = proto.Int64(int64(serviceInstanceID))
		if err := l.createServiceInstance(ctx, subOperation, appointmentID, req, appointmentEntity); err != nil {
			return err
		}
	}

	return nil
}

func (l *Logic) updateServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for update operation")
	}
	// 获取现有服务实例
	serviceInstance, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetServiceInstanceId()))
	if err != nil {
		return err
	}
	// 更新字段
	if operation.GetTimeConfig() != nil {
		if operation.GetTimeConfig().GetStartTime() != nil {
			serviceInstance.StartDate = serviceinstance.TimeToPgDate(operation.GetTimeConfig().GetStartTime().AsTime())
		}
		if operation.GetTimeConfig().GetEndTime() != nil {
			serviceInstance.EndDate = serviceinstance.TimeToPgDate(operation.GetTimeConfig().GetEndTime().AsTime())
		}
	}
	if operation.GetDateScheduleConfig() != nil {
		if operation.GetDateScheduleConfig().GetDateType() != pb.DateType_DATE_TYPE_UNSPECIFIED {
			serviceInstance.DateType = int(operation.GetDateScheduleConfig().GetDateType())
		}
	}
	if operation.GetWorkMode() != pb.WorkMode_WORK_MODE_UNSPECIFIED {
		serviceInstance.WorkMode = int(operation.GetWorkMode())
	}
	if operation.GetPrice() != 0 {
		serviceInstance.Price = int(operation.GetPrice())
	}
	if operation.GetDuration() != 0 {
		serviceInstance.Duration = int(operation.GetDuration())
	}

	return l.serviceInstanceCli.Update(ctx, serviceInstance)
}

func (l *Logic) deleteServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for delete operation")
	}

	// 删除相关的履约记录
	if err := l.fulfillmentCli.DeleteByServiceInstanceID(ctx, int64(operation.GetServiceInstanceId())); err != nil {
		return err
	}

	// 删除服务实例
	return l.serviceInstanceCli.Delete(ctx, int(operation.GetServiceInstanceId()))
}

func (l *Logic) getCareTypeByServiceTemplateID(_ context.Context, _ int64) (offeringpb.CareCategory, error) {
	// 调用offering服务获取careType
	// 暂时返回默认值
	return offeringpb.CareCategory_BOARDING, nil
}

func (l *Logic) recalculateAndUpdateServiceItemType(ctx context.Context, appointmentID int) error {
	// 获取预约下的所有服务实例
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, appointmentID)
	if err != nil {
		return err
	}

	// 收集所有唯一的careType
	uniqueCareTypes := make(map[offeringpb.CareCategory]bool)
	for _, si := range serviceInstances {
		careType := offeringpb.CareCategory(si.CareType)
		uniqueCareTypes[careType] = true
	}

	// 计算ServiceItemType
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}

	// 更新预约的ServiceItemType
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, appointmentID)
	if err != nil {
		return err
	}
	appointmentEntity.ServiceItemType = int(serviceItemType)

	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func verifyUpdateAppointmentRequest(req *pb.UpdateAppointmentRequest) error {
	if req.GetAppointmentId() <= 0 {
		return status.Error(codes.InvalidArgument, "appointment_id must be greater than 0")
	}
	// 验证预约操作
	if operations := req.GetAppointmentOperation(); operations != nil {
		if operations.StartTime != nil && operations.EndTime != nil {
			startTime := operations.StartTime.AsTime()
			endTime := operations.EndTime.AsTime()
			if startTime.After(endTime) {
				return status.Error(codes.InvalidArgument, "start_time must be before end_time")
			}
		}
	}
	// 验证服务操作
	for _, serviceOp := range req.GetServiceOperations() {
		if err := verifyServiceOperation(serviceOp); err != nil {
			return err
		}
	}

	return nil
}

func verifyServiceOperation(operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	switch operation.GetOperationMode() {
	case pb.OperationMode_OPERATION_MODE_CREATE:
		if operation.GetId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_id is required for create operation")
		}
		if operation.GetTimeConfig().GetStartTime() == nil || operation.GetTimeConfig().GetEndTime() == nil {
			return status.Error(codes.InvalidArgument, "start_time and end_time are required for create operation")
		}
	case pb.OperationMode_OPERATION_MODE_UPDATE, pb.OperationMode_OPERATION_MODE_DELETE:
		if operation.GetServiceInstanceId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_instance_id is required for update/delete operation")
		}
	}

	// 验证时间
	if operation.GetTimeConfig().GetStartTime() != nil &&
		operation.GetTimeConfig().GetEndTime() != nil {
		startTime := operation.GetTimeConfig().GetStartTime().AsTime()
		endTime := operation.GetTimeConfig().GetEndTime().AsTime()
		if startTime.After(endTime) {
			return status.Error(codes.InvalidArgument, "start_time must be before end_time")
		}
	}

	return nil
}

// GetAppointmentByIDs 根据ID列表获取预约信息
func (l *Logic) GetAppointmentByIDs(ctx context.Context, req *pb.GetAppointmentByIDsRequest) (
	*pb.GetAppointmentByIDsResponse, error) {
	companyID := metadata.GetCompanyID(ctx)
	// 校验请求
	if err := verifyGetAppointmentByIDsRequest(req); err != nil {
		return nil, err
	}
	// 如果 appointment_ids 为空，直接返回空结果
	if len(req.GetAppointmentIds()) == 0 {
		return &pb.GetAppointmentByIDsResponse{
			Appointments: []*pb.Appointment{},
		}, nil
	}
	// 查询数据库
	appointments, err := l.appointmentCli.GetByIDs(ctx, req.GetAppointmentIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get appointments: %v", err)
	}

	// 批量获取AutoAssign
	autoAssignMap, err := l.appointmentSvcCli.GetAutoAssignBatch(ctx, req.GetAppointmentIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get auto assign: %v", err)
	}

	// 批量获取等待列表
	waitListMap, err := l.appointmentSvcCli.GetWaitListByAppointment(ctx, companyID, req.GetAppointmentIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get wait list: %v", err)
	}

	// 转换为proto对象
	var pbAppointments []*pb.Appointment
	for _, appointment := range appointments {
		pbAppointment, err := l.convertAppointmentToPB(ctx, appointment, autoAssignMap, waitListMap.GetWaitList())
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert appointment: %v", err)
		}
		pbAppointments = append(pbAppointments, pbAppointment)
	}

	response := &pb.GetAppointmentByIDsResponse{
		Appointments: pbAppointments,
	}

	return response, nil
}

// GetLastAppointmentByPetID 根据宠物ID获取最新的预约信息
func (l *Logic) GetLastAppointmentByPetID(ctx context.Context, req *pb.GetLastAppointmentByPetIDRequest) (
	*pb.GetLastAppointmentByPetIDResponse, error) {
	// 校验请求
	if err := verifyGetLastAppointmentByPetIDRequest(req); err != nil {
		return nil, err
	}

	// 1. 通过 businessID、companyID、petID 查询最新的 service_instance
	serviceInstance, err := l.serviceInstanceCli.GetLatestByPetID(
		ctx,
		int32(req.GetBusinessId()),
		int32(req.GetCompanyId()),
		req.GetPetId(),
	)
	if err != nil {
		// 如果没有找到记录，返回空的appointment
		if err == gorm.ErrRecordNotFound {
			return &pb.GetLastAppointmentByPetIDResponse{
				Appointment: nil,
			}, nil
		}

		return nil, status.Errorf(codes.Internal, "failed to get latest service instance: %v", err)
	}

	// 2. 通过 service_instance 的 appointment_id 查询对应的 appointment
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, serviceInstance.AppointmentID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get appointment: %v", err)
	}

	// 3. 获取 AutoAssign 信息
	appointmentIDs := []int64{int64(appointmentEntity.ID)}
	autoAssignMap, err := l.appointmentSvcCli.GetAutoAssignBatch(ctx, appointmentIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get auto assign: %v", err)
	}

	// 4. 获取 WaitList 信息
	waitListMap, err := l.appointmentSvcCli.GetWaitListByAppointment(ctx, req.GetCompanyId(), appointmentIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get wait list: %v", err)
	}

	// 5. 转换为 proto 对象
	pbAppointment, err := l.convertAppointmentToPB(ctx, appointmentEntity, autoAssignMap, waitListMap.GetWaitList())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to convert appointment: %v", err)
	}

	return &pb.GetLastAppointmentByPetIDResponse{
		Appointment: pbAppointment,
	}, nil
}

func (l *Logic) ListAppointment(ctx context.Context, req *pb.ListAppointmentRequest) (
	*pb.ListAppointmentResponse, error) {
	// 校验请求
	if err := verifyListAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 构建查询参数
	param := &appointment.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
	}
	// 设置时间范围
	if req.GetStartTime() != nil {
		param.StartTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime() != nil {
		param.EndTime = req.GetEndTime().AsTime()
	}
	// 设置分页信息
	param.PaginationInfo = buildPaginationInfo(req)
	// 构建过滤条件
	filter, err := l.buildFilter(req)
	if err != nil {
		return nil, err
	}
	// 查询总数
	total, err := l.appointmentCli.Count(ctx, param, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count appointments: %v", err)
	}
	// 查询列表
	appointments, err := l.appointmentCli.List(ctx, param, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list appointments: %v", err)
	}

	// 批量获取AutoAssign
	appointmentIDs := make([]int64, 0, len(appointments))
	for _, appointment := range appointments {
		appointmentIDs = append(appointmentIDs, int64(appointment.ID))
	}
	autoAssignMap, err := l.appointmentSvcCli.GetAutoAssignBatch(ctx, appointmentIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get auto assign: %v", err)
	}
	// 批量获取等待列表
	waitListMap, err := l.appointmentSvcCli.GetWaitListByAppointment(ctx, int64(req.GetCompanyId()), appointmentIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get wait list: %v", err)
	}

	// 转换为 proto 对象
	var pbAppointments []*pb.Appointment
	for _, appointment := range appointments {
		pbAppointment, err := l.convertAppointmentToPB(ctx, appointment, autoAssignMap, waitListMap.GetWaitList())
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert appointment: %v", err)
		}
		pbAppointments = append(pbAppointments, pbAppointment)
	}
	// 计算是否最后一页
	isEnd := false
	if req.GetPagination() != nil {
		offset := req.GetPagination().GetOffset()
		limit := req.GetPagination().GetLimit()
		isEnd = int64(offset+limit) >= total
	} else {
		isEnd = int64(len(pbAppointments)) >= total
	}
	response := &pb.ListAppointmentResponse{
		Appointments: pbAppointments,
		Pagination:   req.GetPagination(),
		IsEnd:        isEnd,
		Total:        int32(total),
	}

	return response, nil
}

func (l *Logic) buildFilter(req *pb.ListAppointmentRequest) (*appointment.Filter, error) {
	filter := &appointment.Filter{}
	if req.GetFilter() != nil {
		// 处理状态过滤
		if len(req.GetFilter().GetStatuses()) > 0 {
			statuses := make([]int32, 0, len(req.GetFilter().GetStatuses()))
			for _, status := range req.GetFilter().GetStatuses() {
				statuses = append(statuses, int32(status))
			}
			filter.Statuses = statuses
		}
	}

	return filter, nil
}

func buildPaginationInfo(req *pb.ListAppointmentRequest) *appointment.PaginationInfo {
	if req.GetPagination() == nil {
		return &appointment.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &appointment.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

func verifyListAppointmentRequest(req *pb.ListAppointmentRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}
	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}
	// 验证时间范围
	if req.GetStartTime() != nil && req.GetEndTime() != nil {
		if req.GetStartTime().AsTime().After(req.GetEndTime().AsTime()) {
			return status.Errorf(codes.InvalidArgument, "start_time cannot be after end_time")
		}
	}
	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
	}

	return nil
}

func verifyGetAppointmentByIDsRequest(req *pb.GetAppointmentByIDsRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}
	for _, id := range req.GetAppointmentIds() {
		if id <= 0 {
			return status.Errorf(codes.InvalidArgument, "appointment_id must be greater than 0")
		}
	}

	return nil
}

func verifyGetLastAppointmentByPetIDRequest(req *pb.GetLastAppointmentByPetIDRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}
	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}
	if req.GetPetId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "pet_id must be greater than 0")
	}

	return nil
}

// convertAppointmentToPB 将数据库实体转换为protobuf格式
func (l *Logic) convertAppointmentToPB(ctx context.Context, entity *appointment.Appointment,
	autoAssignMap map[int64]*appointmentSvc.AutoAssign,
	waitListMap map[int64]*appointmentpb.WaitListCalendarView) (*pb.Appointment, error) {
	// 获取该appointment下的所有service instances
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, entity.ID)
	if err != nil {
		return nil, err
	}

	// 获取所有service的类型信息
	serviceTypeMap, err := l.getServiceTypeMap(ctx, serviceInstances)
	if err != nil {
		return nil, err
	}

	// 按pet_id分组service instances
	petServiceMap := make(map[int64][]*serviceinstance.ServiceInstance)
	for _, si := range serviceInstances {
		petID := int64(si.PetID)
		petServiceMap[petID] = append(petServiceMap[petID], si)
	}

	// 构建PetDetail列表
	var petDetails []*pb.PetDetail
	for petID, services := range petServiceMap {
		// 构建PetDetail，包含services和additional_services
		petDetail, err := l.buildPetDetailWithServices(ctx, services, serviceTypeMap, petID)
		if err != nil {
			return nil, err
		}
		petDetails = append(petDetails, petDetail)
	}

	// 构建并返回Appointment
	pbAppointment := &pb.Appointment{
		Id:              int64(entity.ID),
		BusinessId:      int64(entity.BusinessID),
		CompanyId:       int64(entity.CompanyID),
		CustomerId:      int64(entity.CustomerID),
		Status:          pb.AppointmentState(entity.Status),
		ServiceItemType: int32(entity.ServiceItemType),
		ColorCode:       entity.ColorCode,
		StartTime:       timestamppb.New(entity.StartTime),
		EndTime:         timestamppb.New(entity.EndTime),
		Pets:            petDetails,
	}

	// 填充AutoAssign
	if autoAssign, ok := autoAssignMap[int64(entity.ID)]; ok && autoAssign != nil {
		pbAppointment.AutoAssign = &pb.AutoAssign{
			Id:              autoAssign.ID,
			StaffId:         autoAssign.StaffID,
			AppointmentTime: autoAssign.AppointmentTime,
		}
	}

	// 填充WaitList
	if waitList, ok := waitListMap[int64(entity.ID)]; ok && waitList != nil {
		pbAppointment.WaitList = &pb.WaitList{
			Id:            waitList.GetId(),
			AppointmentId: waitList.GetAppointmentId(),
		}
	}

	return pbAppointment, nil
}

// getServiceTypeMap 批量获取service类型信息
func (l *Logic) getServiceTypeMap(ctx context.Context,
	serviceInstances []*serviceinstance.ServiceInstance) (map[int64]offeringpb.Service_Type, error) {
	// 收集所有唯一的ServiceFactoryID
	serviceFactoryIDs := make(map[int64]bool)
	for _, si := range serviceInstances {
		serviceFactoryIDs[int64(si.ServiceFactoryID)] = true
	}

	// 批量查询service信息
	serviceTypeMap := make(map[int64]offeringpb.Service_Type)
	for serviceFactoryID := range serviceFactoryIDs {
		service, err := l.offeringServiceCli.GetService(ctx, serviceFactoryID)
		if err != nil {
			// 如果获取失败，记录日志但继续处理，默认为SERVICE类型
			serviceTypeMap[serviceFactoryID] = offeringpb.Service_SERVICE

			continue
		}
		serviceTypeMap[serviceFactoryID] = service.GetType()
	}

	return serviceTypeMap, nil
}

// buildPetDetailWithServices 构建PetDetail，区分services和additional_services
func (l *Logic) buildPetDetailWithServices(ctx context.Context, services []*serviceinstance.ServiceInstance,
	serviceTypeMap map[int64]offeringpb.Service_Type, petID int64) (*pb.PetDetail, error) {
	// 获取并分组medications、feedings和service charges
	medicationMap, feedingMap, serviceChargeMap, err := l.fetchAndGroupScheduleData(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构建service instance映射并添加medications、feedings和service charges
	pbServiceMap, err := l.buildServiceInstanceMap(services, medicationMap, feedingMap, serviceChargeMap)
	if err != nil {
		return nil, err
	}

	// 分类services和additional_services
	rootServices, additionalServices := l.classifyServices(services, pbServiceMap, serviceTypeMap)

	// 构建addon关系
	l.buildAddonRelationships(services, pbServiceMap, serviceTypeMap)

	return &pb.PetDetail{
		PetId:              petID,
		Services:           rootServices,
		AdditionalServices: additionalServices,
	}, nil
}

// fetchAndGroupScheduleData 获取并分组medications、feedings和service charges数据
func (l *Logic) fetchAndGroupScheduleData(ctx context.Context, services []*serviceinstance.ServiceInstance) (
	map[int64][]*medication.AppointmentPetMedication,
	map[int64][]*feedingRepo.AppointmentPetFeeding,
	map[int64][]*servicecharge.ServiceCharge,
	error) {
	// 收集所有service instance IDs
	serviceInstanceIDs := make([]int64, 0, len(services))
	for _, si := range services {
		serviceInstanceIDs = append(serviceInstanceIDs, int64(si.ID))
	}

	// 批量获取medications
	medications, err := l.medicationCli.GetByServiceInstanceIDs(ctx, serviceInstanceIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	// 批量获取feedings
	feedings, err := l.feedingCli.GetByServiceInstanceIDs(ctx, serviceInstanceIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	// 批量获取service charges
	serviceCharges, err := l.serviceChargeCli.GetByServiceInstanceIDs(ctx, serviceInstanceIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	// 按service_instance_id分组medications
	medicationMap := make(map[int64][]*medication.AppointmentPetMedication)
	for _, med := range medications {
		medicationMap[med.PetDetailID] = append(medicationMap[med.PetDetailID], med)
	}

	// 按service_instance_id分组feedings
	feedingMap := make(map[int64][]*feedingRepo.AppointmentPetFeeding)
	for _, feed := range feedings {
		feedingMap[feed.PetDetailID] = append(feedingMap[feed.PetDetailID], feed)
	}

	// 按service_instance_id分组service charges
	serviceChargeMap := make(map[int64][]*servicecharge.ServiceCharge)
	for _, charge := range serviceCharges {
		serviceChargeMap[charge.ServiceInstanceID] = append(serviceChargeMap[charge.ServiceInstanceID], charge)
	}

	return medicationMap, feedingMap, serviceChargeMap, nil
}

// buildServiceInstanceMap 构建service instance映射并添加medications、feedings和service charges
func (l *Logic) buildServiceInstanceMap(
	services []*serviceinstance.ServiceInstance,
	medicationMap map[int64][]*medication.AppointmentPetMedication,
	feedingMap map[int64][]*feedingRepo.AppointmentPetFeeding,
	serviceChargeMap map[int64][]*servicecharge.ServiceCharge) (map[int]*pb.ServiceInstanceImpl, error) {
	pbServiceMap := make(map[int]*pb.ServiceInstanceImpl)

	for _, si := range services {
		pbService, err := l.convertToServiceInstanceImpl(si)
		if err != nil {
			return nil, err
		}

		// 添加medications
		if meds, exists := medicationMap[int64(si.ID)]; exists {
			pbService.Medications = l.convertMedicationsToPB(meds)
		}

		// 添加feedings
		if feeds, exists := feedingMap[int64(si.ID)]; exists {
			pbService.Feedings = l.convertFeedingsToPB(feeds)
		}

		// 添加service charges
		if charges, exists := serviceChargeMap[int64(si.ID)]; exists {
			pbService.Charges = l.convertServiceChargesToPB(charges)
		}

		pbServiceMap[si.ID] = pbService
	}

	return pbServiceMap, nil
}

// classifyServices 分类services和additional_services
func (l *Logic) classifyServices(
	services []*serviceinstance.ServiceInstance,
	pbServiceMap map[int]*pb.ServiceInstanceImpl,
	serviceTypeMap map[int64]offeringpb.Service_Type) ([]*pb.ServiceInstanceImpl, []*pb.ServiceInstanceImpl) {
	var rootServices []*pb.ServiceInstanceImpl
	var additionalServices []*pb.ServiceInstanceImpl

	for _, si := range services {
		pbService := pbServiceMap[si.ID]
		serviceType := serviceTypeMap[int64(si.ServiceFactoryID)]

		if si.ParentID == 0 {
			// ParentID=0 的是主服务
			rootServices = append(rootServices, pbService)
		} else if serviceType != offeringpb.Service_ADD_ON {
			// ParentID!=0 且不是addon的是additional service
			additionalServices = append(additionalServices, pbService)
		}
		// addon类型的会在下面作为子节点添加到父服务中
	}

	return rootServices, additionalServices
}

// buildAddonRelationships 构建addon关系，将addon类型的服务添加到父服务的Addons字段
func (l *Logic) buildAddonRelationships(
	services []*serviceinstance.ServiceInstance,
	pbServiceMap map[int]*pb.ServiceInstanceImpl,
	serviceTypeMap map[int64]offeringpb.Service_Type) {
	for _, si := range services {
		if si.ParentID == 0 {
			continue
		}

		serviceType := serviceTypeMap[int64(si.ServiceFactoryID)]
		if serviceType == offeringpb.Service_ADD_ON {
			pbService := pbServiceMap[si.ID]
			if parentPbService, exists := pbServiceMap[si.ParentID]; exists {
				parentPbService.Addons = append(parentPbService.Addons, pbService)
			}
		}
	}
}

// buildServiceInstanceImpl 构建pb.ServiceInstanceImpl对象
func (l *Logic) buildServiceInstanceImpl(si *serviceinstance.ServiceInstance,
	setTimeField func(*pb.ServiceInstanceImpl)) *pb.ServiceInstanceImpl {
	workMode := pb.WorkMode(si.WorkMode)
	price := int64(si.Price)
	duration := durationpb.New(time.Duration(si.Duration) * time.Minute)

	impl := &pb.ServiceInstanceImpl{
		ServiceInstanceId: int64(si.ID),
		ServiceTemplateId: int64(si.ServiceFactoryID),
		CareType:          offeringpb.CareCategory(si.CareType),
		Addons:            []*pb.ServiceInstanceImpl{},
		WorkMode:          &workMode,
		Price:             &price,
		Duration:          duration,
	}

	// 设置Time字段
	setTimeField(impl)

	return impl
}

// convertToServiceInstanceImpl 将ServiceInstance转换为pb.ServiceInstanceImpl
func (l *Logic) convertToServiceInstanceImpl(si *serviceinstance.ServiceInstance) (*pb.ServiceInstanceImpl, error) {
	timeConfig := &pb.TimeConfig{}
	dateScheduleConfig := &pb.DateScheduleConfig{}

	if si.TimeConfig != nil {
		if err := json.Unmarshal([]byte(*si.TimeConfig), timeConfig); err != nil {
			return nil, err
		}

		return l.buildServiceInstanceImpl(si, func(impl *pb.ServiceInstanceImpl) {
			impl.Time = &pb.ServiceInstanceImpl_TimeConfig{
				TimeConfig: timeConfig,
			}
		}), nil
	}

	if si.ScheduleConfig != nil {
		if err := json.Unmarshal([]byte(*si.ScheduleConfig), dateScheduleConfig); err != nil {
			return nil, err
		}
	}

	return l.buildServiceInstanceImpl(si, func(impl *pb.ServiceInstanceImpl) {
		impl.Time = &pb.ServiceInstanceImpl_DateScheduleConfig{
			DateScheduleConfig: dateScheduleConfig,
		}
	}), nil
}

// convertMedicationsToPB 将用药记录转换为proto格式
func (l *Logic) convertMedicationsToPB(
	medications []*medication.AppointmentPetMedication) []*pb.AppointmentPetMedicationScheduleDef {
	if len(medications) == 0 {
		return nil
	}

	result := make([]*pb.AppointmentPetMedicationScheduleDef, 0, len(medications))
	for _, med := range medications {
		pbMed := &pb.AppointmentPetMedicationScheduleDef{
			MedicationAmount: med.MedicationAmount,
			MedicationUnit:   med.MedicationUnit,
			MedicationName:   med.MedicationName,
		}

		// 设置可选字段
		if med.MedicationNote != "" {
			pbMed.MedicationNote = &med.MedicationNote
		}

		// 处理日期选择配置
		if med.DateType > 0 {
			selectedDate := &pb.AppointmentPetMedicationScheduleDef_SelectedDateDef{
				DateType: pb.FeedingMedicationScheduleDateType(med.DateType),
			}

			// 如果是特定日期类型，解析specific_dates
			if med.DateType == medication.DateTypeSpecificDate && med.SpecificDates != "" {
				var specificDates []string
				if err := json.Unmarshal([]byte(med.SpecificDates), &specificDates); err == nil {
					selectedDate.SpecificDates = specificDates
				}
			}

			pbMed.SelectedDate = selectedDate
		}

		result = append(result, pbMed)
	}

	return result
}

// convertFeedingsToPB 将喂养记录转换为proto格式
func (l *Logic) convertFeedingsToPB(
	feedings []*feedingRepo.AppointmentPetFeeding) []*pb.AppointmentPetFeedingScheduleDef {
	if len(feedings) == 0 {
		return nil
	}

	result := make([]*pb.AppointmentPetFeedingScheduleDef, 0, len(feedings))
	for _, feed := range feedings {
		pbFeed := &pb.AppointmentPetFeedingScheduleDef{
			FeedingAmount: feed.FeedingAmount,
			FeedingUnit:   feed.FeedingUnit,
			FeedingType:   feed.FeedingType,
			FeedingSource: feed.FeedingSource,
		}

		// 设置可选字段
		if feed.FeedingInstruction != "" {
			pbFeed.FeedingInstruction = &feed.FeedingInstruction
		}
		if feed.FeedingNote != "" {
			pbFeed.FeedingNote = &feed.FeedingNote
		}

		result = append(result, pbFeed)
	}

	return result
}

// convertServiceChargesToPB 将服务费用转换为proto格式
func (l *Logic) convertServiceChargesToPB(charges []*servicecharge.ServiceCharge) []*pb.ServiceCharge {
	if len(charges) == 0 {
		return nil
	}

	result := make([]*pb.ServiceCharge, 0, len(charges))
	for _, charge := range charges {
		pbCharge := &pb.ServiceCharge{
			Name:        charge.Name,
			Amount:      charge.Amount,
			Description: charge.Description,
			ChargeType:  charge.ChargeType,
		}
		result = append(result, pbCharge)
	}

	return result
}
