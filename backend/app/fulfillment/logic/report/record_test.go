package report

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/config"
	appointmentMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment/mock"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	reportMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report/mock"
	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	sendrecordMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord/mock"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	messageMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message/mock"
	organizationMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/organization/mock"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_BuildEmailSubject(t *testing.T) {
	logic := &Logic{}

	// 创建测试用的 summaryInfo
	summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessName: "Test Pet Salon",
		},
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetName: "Buddy",
		},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_GROOMING,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Template: &fulfillmentpb.FulfillmentReportTemplate{
				Title: "Custom Grooming Report",
			},
		},
		AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
			PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
				{
					PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
						{
							StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
								StaffFirstName: "John",
								StaffLastName:  "Doe",
							},
						},
					},
				},
			},
		},
	}

	// 测试自定义主题
	t.Run("custom_subject", func(t *testing.T) {
		subject := "Custom Subject for {PetName} at {BusinessName}"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Custom Subject for Buddy at Test Pet Salon", result)
	})

	// 测试空主题 - 首次发送
	t.Run("empty_subject_first_send", func(t *testing.T) {
		result, err := logic.BuildEmailSubject("", summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy's grooming Report at Test Pet Salon", result)
	})

	// 测试空主题 - 重发
	t.Run("empty_subject_resend", func(t *testing.T) {
		resendSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: summaryInfo.BusinessInfo,
			PetInfo:      summaryInfo.PetInfo,
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CareType: offeringpb.CareCategory_GROOMING,
				Status:   fulfillmentpb.ReportStatus_SENT, // 已发送状态
				Template: summaryInfo.FulfillmentReport.Template,
			},
			AppointmentInfo: summaryInfo.AppointmentInfo,
		}

		result, err := logic.BuildEmailSubject("", resendSummaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "[Updated] Buddy's Custom Grooming Report at Test Pet Salon", result)
	})

	// 测试日常报告
	t.Run("daily_report", func(t *testing.T) {
		dailySummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: summaryInfo.BusinessInfo,
			PetInfo:      summaryInfo.PetInfo,
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CareType: offeringpb.CareCategory_DAYCARE,
				Status:   fulfillmentpb.ReportStatus_DRAFT,
				Template: &fulfillmentpb.FulfillmentReportTemplate{
					Title: "", // 空标题，使用默认
				},
			},
			AppointmentInfo: summaryInfo.AppointmentInfo,
		}

		result, err := logic.BuildEmailSubject("", dailySummaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy's daily Report at Test Pet Salon", result)
	})

	// 测试所有占位符替换
	t.Run("all_placeholders", func(t *testing.T) {
		subject := "{PetName} - {Title} - {BusinessName} - {MainStaff} - {CareType}"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy - Custom Grooming Report - Test Pet Salon - John - grooming", result)
	})
}

func TestLogic_BuildSmsSendContent(t *testing.T) {
	// 由于 BuildSmsSendContent 依赖于配置文件，我们通过 mock 的方式来测试其逻辑
	// 这里主要测试占位符替换和模板生成逻辑

	// 创建测试用的 summaryInfo
	summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessName: "Test Pet Salon",
		},
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetName: "Buddy",
		},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_GROOMING,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Uuid:     "test-uuid-123",
			Template: &fulfillmentpb.FulfillmentReportTemplate{
				Title: "Custom Grooming Report",
			},
		},
		AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
			PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
				{
					PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
						{
							StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
								StaffFirstName: "John",
								StaffLastName:  "Doe",
							},
						},
					},
				},
			},
		},
	}

	// 测试配置错误的情况
	t.Run("config_error", func(t *testing.T) {
		logic := &Logic{}

		// 由于配置为空，会导致空指针异常，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.BuildSmsSendContent(summaryInfo)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Empty(t, result)
		}
	})

	// 测试配置错误的护理类型（现在 BOARDING 被当作日常报告处理）
	t.Run("config_error_for_boarding_care_type", func(t *testing.T) {
		logic := &Logic{}

		boardingSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: summaryInfo.BusinessInfo,
			PetInfo:      summaryInfo.PetInfo,
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CareType: offeringpb.CareCategory_BOARDING, // 现在被当作日常报告处理
				Status:   fulfillmentpb.ReportStatus_DRAFT,
				Uuid:     "test-uuid-123",
				Template: summaryInfo.FulfillmentReport.Template,
			},
			AppointmentInfo: summaryInfo.AppointmentInfo,
		}

		// 由于配置为空，会导致空指针异常，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.BuildSmsSendContent(boardingSummaryInfo)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Empty(t, result)
		}
	})
}

func TestLogic_getMainStaffNames(t *testing.T) {
	logic := &Logic{}

	// 测试有员工信息的情况
	t.Run("with_staff_info", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "John",
									StaffLastName:  "Doe",
								},
							},
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Jane",
									StaffLastName:  "Smith",
								},
							},
						},
					},
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Bob",
									StaffLastName:  "Wilson",
								},
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 3)
		assert.Contains(t, result, "John")
		assert.Contains(t, result, "Jane")
		assert.Contains(t, result, "Bob")
	})

	// 测试没有员工信息的情况
	t.Run("without_staff_info", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: nil,
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 0)
	})

	// 测试空的 AppointmentInfo
	t.Run("nil_appointment_info", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: nil,
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 0)
	})

	// 测试员工名字为空的情况
	t.Run("empty_staff_first_name", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "", // 空名字
									StaffLastName:  "Doe",
								},
							},
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Jane",
									StaffLastName:  "Smith",
								},
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 1)
		assert.Contains(t, result, "Jane")
		assert.NotContains(t, result, "")
	})
}

func TestLogic_ListSendReportRecords(t *testing.T) {
	ctx := context.Background()
	companyID := int64(1)
	businessID := int64(2)

	// 创建测试数据
	sendRecord := &sendrecordrepo.SendRecord{
		ID:            1,
		ReportID:      100,
		CompanyID:     companyID,
		BusinessID:    businessID,
		AppointmentID: 200,
		PetID:         300,
		SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
		SentTime:      time.Now(),
		SentBy:        1,
		ErrorMessage:  lo.ToPtr(""),
		IsSentSuccess: lo.ToPtr(true),
		ContentJSON:   `{"photos":[],"videos":[],"feedbacks":[]}`,
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}

	// 测试成功获取发送记录列表
	t.Run("successful_list_send_records", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)
		reportRepo := reportMock.NewMockReadWriter(ctrl)
		organizationRepo := organizationMock.NewMockReadWriter(ctrl)
		appointmentRepo := appointmentMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			sendRecordRepo:   sendRecordRepo,
			reportRepo:       reportRepo,
			organizationRepo: organizationRepo,
			appointmentRepo:  appointmentRepo,
		}

		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  &companyID,
			BusinessId: &businessID,
			Filter: &fulfillmentpb.ListSendReportRecordsFilter{
				AppointmentIds: []int64{200},
			},
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  20,
			},
		}

		// 创建测试用的 report 数据
		testReport := &reportrepo.Report{
			ID:            100,
			CompanyID:     companyID,
			BusinessID:    businessID,
			AppointmentID: 200,
			PetID:         300,
			CareType:      int32(offeringpb.CareCategory_BOARDING),
			ServiceDate:   "2024-01-15",
			Status:        "draft",
			UUID:          "test-uuid",
			ContentJSON:   `{"photos":[],"videos":[],"feedbacks":[]}`,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}

		// Mock 期望调用
		sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
		sendRecordRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sendrecordrepo.SendRecord{sendRecord}, nil)
		organizationRepo.EXPECT().GetCompanyPreferenceSetting(ctx, int64(1)).Return(&organizationpb.CompanyPreferenceSettingModel{
			TimeZone: &organizationpb.TimeZone{
				Name: "America/Los_Angeles",
			},
		}, nil)
		mockAppointments := []*appointmentpb.AppointmentModel{
			{
				Id: 200,
			},
		}
		appointmentRepo.EXPECT().GetPetAppointments(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockAppointments, nil)
		// Mock count
		reportRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)

		// Mock second List call for grooming reports
		reportRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*reportrepo.Report{testReport}, nil)

		resp, err := logic.ListSendReportRecords(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.SendRecords, 1)
		assert.Equal(t, int32(1), resp.Total)
		assert.Equal(t, sendRecord.ReportID, resp.SendRecords[0].ReportId)
		assert.Equal(t, sendRecord.AppointmentID, resp.SendRecords[0].AppointmentId)
		assert.Equal(t, sendRecord.PetID, resp.SendRecords[0].PetId)
		assert.Equal(t, fulfillmentpb.SendMethod(sendRecord.SendMethod), resp.SendRecords[0].SendMethod)
	})

	// 测试请求验证失败
	t.Run("invalid_request", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(0)), // 无效的 company_id
			BusinessId: &businessID,
		}

		resp, err := logic.ListSendReportRecords(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "company_id must be greater than 0")
	})

	// 测试仓库错误
	t.Run("repository_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			sendRecordRepo: sendRecordRepo,
		}

		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  &companyID,
			BusinessId: &businessID,
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  20,
			},
		}

		// Mock 期望调用返回错误
		sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), assert.AnError)

		resp, err := logic.ListSendReportRecords(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "failed to count send records")
	})

	// 测试列表查询错误
	t.Run("list_query_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			sendRecordRepo: sendRecordRepo,
		}

		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  &companyID,
			BusinessId: &businessID,
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  20,
			},
		}

		// Mock 期望调用 - Count 成功，List 失败
		sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
		sendRecordRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, assert.AnError)

		resp, err := logic.ListSendReportRecords(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "failed to list send records")
	})

	// 测试空结果
	t.Run("empty_result", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			sendRecordRepo: sendRecordRepo,
		}

		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  &companyID,
			BusinessId: &businessID,
		}

		// Mock 期望调用 - 返回空结果
		sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)
		sendRecordRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sendrecordrepo.SendRecord{}, nil)

		resp, err := logic.ListSendReportRecords(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, int32(0), resp.Total)
		assert.Len(t, resp.SendRecords, 0)
	})
}

func TestLogic_verifyListSendReportRecordsRequest(t *testing.T) {
	logic := &Logic{}

	// 测试有效请求
	t.Run("valid_request", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(1)),
			BusinessId: lo.ToPtr(int64(2)),
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  20,
			},
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.NoError(t, err)
	})

	// 测试无效请求
	t.Run("nil_request", func(t *testing.T) {
		err := logic.verifyListSendReportRecordsRequest(nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "request cannot be nil")
	})

	t.Run("invalid_company_id", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(0)),
			BusinessId: lo.ToPtr(int64(2)),
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "company_id must be greater than 0")
	})

	t.Run("invalid_business_id", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(1)),
			BusinessId: lo.ToPtr(int64(0)),
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "business_id must be greater than 0")
	})

	// 测试分页参数验证
	t.Run("negative_offset", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(1)),
			BusinessId: lo.ToPtr(int64(2)),
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: -1, // 负数偏移量
				Limit:  20,
			},
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "pagination offset cannot be negative")
	})

	t.Run("negative_limit", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(1)),
			BusinessId: lo.ToPtr(int64(2)),
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  -1, // 负数限制
			},
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "pagination limit cannot be negative")
	})

	// 测试边界值
	t.Run("zero_pagination_values", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			CompanyId:  lo.ToPtr(int64(1)),
			BusinessId: lo.ToPtr(int64(2)),
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 0,
				Limit:  0, // 零值是允许的
			},
		}

		err := logic.verifyListSendReportRecordsRequest(req)
		assert.NoError(t, err)
	})
}

func TestLogic_buildSendRecordFilter(t *testing.T) {
	logic := &Logic{}

	// 测试空过滤器
	t.Run("nil_filter", func(t *testing.T) {
		filter := logic.buildSendRecordFilter(nil)
		assert.NotNil(t, filter)
		assert.Empty(t, filter.AppointmentIDs)
		assert.Empty(t, filter.PetIDs)
		assert.Empty(t, filter.CareTypes)
		assert.Empty(t, filter.SendMethods)
	})

	// 测试完整过滤器
	t.Run("complete_filter", func(t *testing.T) {
		pbFilter := &fulfillmentpb.ListSendReportRecordsFilter{
			AppointmentIds: []int64{1, 2, 3},
			PetIds:         []int64{4, 5, 6},
			CareTypes:      []offeringpb.CareCategory{offeringpb.CareCategory_BOARDING},
			SendMethod:     lo.ToPtr(fulfillmentpb.SendMethod_SMS),
		}

		filter := logic.buildSendRecordFilter(pbFilter)
		assert.NotNil(t, filter)
		assert.Equal(t, []int64{1, 2, 3}, filter.AppointmentIDs)
		assert.Equal(t, []int64{4, 5, 6}, filter.PetIDs)
		assert.Equal(t, []int32{int32(offeringpb.CareCategory_BOARDING.Number())}, filter.CareTypes)
		assert.Equal(t, []int32{int32(fulfillmentpb.SendMethod_SMS.Number())}, filter.SendMethods)
	})

	// 测试部分过滤器
	t.Run("partial_filter", func(t *testing.T) {
		pbFilter := &fulfillmentpb.ListSendReportRecordsFilter{
			AppointmentIds: []int64{100},
			// 其他字段为空
		}

		filter := logic.buildSendRecordFilter(pbFilter)
		assert.NotNil(t, filter)
		assert.Equal(t, []int64{100}, filter.AppointmentIDs)
		assert.Empty(t, filter.PetIDs)
		assert.Empty(t, filter.CareTypes)
		assert.Empty(t, filter.SendMethods)
	})

	// 测试多个护理类型
	t.Run("multiple_care_types", func(t *testing.T) {
		pbFilter := &fulfillmentpb.ListSendReportRecordsFilter{
			CareTypes: []offeringpb.CareCategory{
				offeringpb.CareCategory_GROOMING,
				offeringpb.CareCategory_DAYCARE,
			},
		}

		filter := logic.buildSendRecordFilter(pbFilter)
		assert.NotNil(t, filter)
		expectedCareTypes := []int32{
			int32(offeringpb.CareCategory_GROOMING.Number()),
			int32(offeringpb.CareCategory_DAYCARE.Number()),
		}
		assert.Equal(t, expectedCareTypes, filter.CareTypes)
	})

	// 测试未指定的发送方式
	t.Run("unspecified_send_method", func(t *testing.T) {
		pbFilter := &fulfillmentpb.ListSendReportRecordsFilter{
			SendMethod: lo.ToPtr(fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED),
		}

		filter := logic.buildSendRecordFilter(pbFilter)
		assert.NotNil(t, filter)
		assert.Empty(t, filter.SendMethods) // 未指定的发送方式不应该被添加到过滤器中
	})

	// 测试空数组
	t.Run("empty_arrays", func(t *testing.T) {
		pbFilter := &fulfillmentpb.ListSendReportRecordsFilter{
			AppointmentIds: []int64{},                   // 空数组
			PetIds:         []int64{},                   // 空数组
			CareTypes:      []offeringpb.CareCategory{}, // 空数组
		}

		filter := logic.buildSendRecordFilter(pbFilter)
		assert.NotNil(t, filter)
		assert.Empty(t, filter.AppointmentIDs)
		assert.Empty(t, filter.PetIDs)
		assert.Empty(t, filter.CareTypes)
	})
}

func TestLogic_buildSendRecordPaginationInfo(t *testing.T) {
	logic := &Logic{}

	// 测试空分页信息
	t.Run("nil_pagination", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{}
		paginationInfo := logic.buildSendRecordPaginationInfo(req)
		assert.NotNil(t, paginationInfo)
		assert.Equal(t, int32(0), paginationInfo.Offset)
		assert.Equal(t, int32(50), paginationInfo.Limit) // 默认值是 50
	})

	// 测试有效分页信息
	t.Run("valid_pagination", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 10,
				Limit:  50,
			},
		}
		paginationInfo := logic.buildSendRecordPaginationInfo(req)
		assert.NotNil(t, paginationInfo)
		assert.Equal(t, int32(10), paginationInfo.Offset)
		assert.Equal(t, int32(50), paginationInfo.Limit)
	})

	// 测试无效 limit - 注意：当前实现直接返回请求中的值，不做验证
	t.Run("invalid_limit", func(t *testing.T) {
		req := &fulfillmentpb.ListSendReportRecordsRequest{
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: 10,
				Limit:  0, // 无效的 limit
			},
		}
		paginationInfo := logic.buildSendRecordPaginationInfo(req)
		assert.NotNil(t, paginationInfo)
		assert.Equal(t, int32(10), paginationInfo.Offset)
		assert.Equal(t, int32(0), paginationInfo.Limit) // 当前实现直接返回请求中的值
	})
}

func TestLogic_BuildEmailSubject_AdditionalCases(t *testing.T) {
	logic := &Logic{}

	// 创建测试用的 summaryInfo
	summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessName: "Test Pet Salon",
		},
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetName: "Buddy",
		},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_GROOMING,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Template: &fulfillmentpb.FulfillmentReportTemplate{
				Title: "Custom Grooming Report",
			},
		},
		AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
			PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
				{
					PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
						{
							StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
								StaffFirstName: "John",
								StaffLastName:  "Doe",
							},
						},
					},
				},
			},
		},
	}

	t.Run("subject_with_special_characters", func(t *testing.T) {
		subject := "{PetName}'s Report @ {BusinessName} - {CareType}"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy's Report @ Test Pet Salon - grooming", result)
	})

	t.Run("subject_with_multiple_same_placeholders", func(t *testing.T) {
		subject := "{PetName} at {BusinessName} - {PetName} Report"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy at Test Pet Salon - Buddy Report", result)
	})

	t.Run("subject_with_no_placeholders", func(t *testing.T) {
		subject := "Standard Report"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Standard Report", result)
	})

	t.Run("subject_with_unknown_placeholders", func(t *testing.T) {
		subject := "{PetName} - {UnknownPlaceholder} - {BusinessName}"
		result, err := logic.BuildEmailSubject(subject, summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Buddy - {UnknownPlaceholder} - Test Pet Salon", result)
	})
}

func TestLogic_GetMainStaffNames(t *testing.T) {
	logic := &Logic{}

	t.Run("successful_get_staff_names", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "John",
									StaffLastName:  "Doe",
								},
							},
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Jane",
									StaffLastName:  "Smith",
								},
							},
						},
					},
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Bob",
									StaffLastName:  "Wilson",
								},
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 3)
		assert.Contains(t, result, "John")
		assert.Contains(t, result, "Jane")
		assert.Contains(t, result, "Bob")
	})

	t.Run("empty_appointment_info", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 0)
	})

	t.Run("staff_with_empty_names", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "", // 空名字
									StaffLastName:  "Doe",
								},
							},
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Jane",
									StaffLastName:  "Smith",
								},
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 1)
		assert.Contains(t, result, "Jane")
		assert.NotContains(t, result, "")
	})

	t.Run("nil_staff_info", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			AppointmentInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
				PetService: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
					{
						PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
							{
								StaffInfo: nil, // nil staff info
							},
							{
								StaffInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
									StaffFirstName: "Jane",
									StaffLastName:  "Smith",
								},
							},
						},
					},
				},
			},
		}

		result := logic.getMainStaffNames(summaryInfo)

		assert.Len(t, result, 1)
		assert.Contains(t, result, "Jane")
	})
}

func TestLogic_GetReportClientURL(t *testing.T) {
	logic := &Logic{}

	t.Run("grooming_care_type_no_config", func(t *testing.T) {
		// 由于配置为空，会导致空指针异常，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.getReportClientURL(offeringpb.CareCategory_GROOMING)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Empty(t, result)
		}
	})

	t.Run("daycare_care_type_no_config", func(t *testing.T) {
		// 由于配置为空，会导致空指针异常，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.getReportClientURL(offeringpb.CareCategory_DAYCARE)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Empty(t, result)
		}
	})

	t.Run("boarding_care_type_treated_as_daycare", func(t *testing.T) {
		// BOARDING 类型现在被当作日常报告处理，会访问配置
		// 由于配置为空，会导致空指针异常，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.getReportClientURL(offeringpb.CareCategory_BOARDING)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Empty(t, result)
		}
	})
}

func TestLogic_AdditionalSimpleMethods(t *testing.T) {
	logic := &Logic{}

	t.Run("test_string_replacement_methods", func(t *testing.T) {
		// 测试字符串替换相关的方法
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Buddy",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CareType: offeringpb.CareCategory_GROOMING,
				Template: &fulfillmentpb.FulfillmentReportTemplate{
					Title: "Test Template",
				},
			},
		}

		// 测试不同的占位符组合
		testCases := []struct {
			input    string
			expected string
		}{
			{"{BusinessName}", "Test Business"},
			{"{PetName}", "Buddy"},
			{"{Title}", "Test Template"},
			{"{CareType}", "grooming"},
			{"Hello {PetName}!", "Hello Buddy!"},
			{"No placeholders", "No placeholders"},
		}

		for _, tc := range testCases {
			result, err := logic.BuildEmailSubject(tc.input, summaryInfo)
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, result)
		}

		// 测试空字符串
		result, err := logic.BuildEmailSubject("", summaryInfo)
		assert.NoError(t, err)
		assert.NotEmpty(t, result) // 空字符串会使用默认模板
	})

	t.Run("test_care_type_conversion", func(t *testing.T) {
		// 测试护理类型转换（通过 BuildEmailSubject 间接测试）
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		result, err := logic.BuildEmailSubject("{CareType}", summaryInfo)
		assert.NoError(t, err)
		assert.Equal(t, "grooming", result)

		// 测试 daycare
		summaryInfo.FulfillmentReport.CareType = offeringpb.CareCategory_DAYCARE
		result, err = logic.BuildEmailSubject("{CareType}", summaryInfo)
		assert.NoError(t, err)
		assert.Equal(t, "daily", result) // 实际返回的是 "daily"
	})
}

func TestLogic_EdgeCasesAndBoundaries(t *testing.T) {
	logic := &Logic{}

	t.Run("build_email_subject_with_nil_summary", func(t *testing.T) {
		// 测试 nil summaryInfo 的处理
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为 summaryInfo 为 nil
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.BuildEmailSubject("Hello {PetName}", nil)

		// 如果没有 panic，则检查结果
		if err == nil {
			assert.NotEmpty(t, result) // 可能会使用默认值
		}
	})

	t.Run("build_email_subject_with_empty_subject", func(t *testing.T) {
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Buddy",
			},
		}

		result, err := logic.BuildEmailSubject("", summaryInfo)

		assert.NoError(t, err)
		assert.NotEmpty(t, result) // 空字符串会使用默认模板
	})

	t.Run("build_email_subject_with_partial_summary_info", func(t *testing.T) {
		// 测试部分信息缺失的情况
		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Buddy",
			},
			// BusinessInfo 为 nil
		}

		result, err := logic.BuildEmailSubject("Hello {PetName} from {BusinessName}", summaryInfo)

		assert.NoError(t, err)
		assert.Equal(t, "Hello Buddy from ", result) // BusinessName 应该被替换为空字符串
	})
}

func TestLogic_SendSmsMessage(t *testing.T) {
	ctx := context.Background()

	t.Run("report_not_found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo: reportRepo,
		}

		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessId: 2,
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CompanyId: 1,
			},
		}
		req := &fulfillmentpb.SendFulfillmentReportRequest{
			FulfillmentReportId: 1,
			StaffId:             100,
		}

		// Mock report not found
		reportRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, nil)

		result, err := logic.SendSmsMessage(ctx, summaryInfo, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "report not found")
	})

	t.Run("report_repo_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo: reportRepo,
		}

		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessId: 2,
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CompanyId: 1,
			},
		}
		req := &fulfillmentpb.SendFulfillmentReportRequest{
			FulfillmentReportId: 1,
			StaffId:             100,
		}

		// Mock database error
		reportRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))

		result, err := logic.SendSmsMessage(ctx, summaryInfo, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("missing_send_record_repo", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)
		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo:     reportRepo,
			sendRecordRepo: sendRecordRepo,
		}

		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessId: 2,
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				CompanyId: 1,
			},
		}
		req := &fulfillmentpb.SendFulfillmentReportRequest{
			FulfillmentReportId: 1,
			StaffId:             100,
		}

		// Mock successful report retrieval
		mockReport := &reportrepo.Report{
			ID:            1,
			CompanyID:     1,
			BusinessID:    2,
			CustomerID:    100,
			AppointmentID: 200,
			PetID:         300,
			CareType:      int32(offeringpb.CareCategory_BOARDING.Number()),
			ServiceDate:   "2024-01-15",
			Status:        "draft",
			TemplateJSON:  `{"careType": 2}`,
		}
		reportRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockReport, nil)

		// Mock send record history query but return error to simulate missing dependencies
		sendRecordRepo.EXPECT().FindByReportIDAndSendMethod(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("missing message repo"))

		result, err := logic.SendSmsMessage(ctx, summaryInfo, req)

		// 由于缺少其他依赖项（如 messageRepo），应该会出错
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "missing message repo")
	})

	t.Run("config_error_nil_config", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)
		sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo:     reportRepo,
			sendRecordRepo: sendRecordRepo,
		}

		summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessId:   2,
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Buddy",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:        lo.ToPtr(int64(1)),
				CompanyId: 1,
				Uuid:      "test-uuid-123",
				CareType:  offeringpb.CareCategory_BOARDING, // 现在被当作日常报告处理
				Status:    fulfillmentpb.ReportStatus_DRAFT,
				Template: &fulfillmentpb.FulfillmentReportTemplate{
					Title: "Daily Report",
				},
			},
		}
		req := &fulfillmentpb.SendFulfillmentReportRequest{
			FulfillmentReportId: 1,
			StaffId:             100,
		}

		// Mock successful report retrieval
		mockReport := &reportrepo.Report{
			ID:            1,
			CompanyID:     1,
			BusinessID:    2,
			CustomerID:    100,
			AppointmentID: 200,
			PetID:         300,
			CareType:      int32(offeringpb.CareCategory_BOARDING.Number()),
			ServiceDate:   "2024-01-15",
			Status:        "draft",
			TemplateJSON:  `{"careType": 2}`,
		}
		reportRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockReport, nil)

		// Mock send record history query - no existing record
		sendRecordRepo.EXPECT().FindByReportIDAndSendMethod(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

		// 由于配置为空，会发生 panic，我们需要捕获这个 panic
		defer func() {
			if r := recover(); r != nil {
				// 预期会发生 panic，因为配置为空
				assert.Contains(t, fmt.Sprintf("%v", r), "nil pointer")
			}
		}()

		result, err := logic.SendSmsMessage(ctx, summaryInfo, req)

		// 如果没有 panic，则应该是错误
		if err != nil {
			assert.Error(t, err)
			assert.Nil(t, result)
		}
	})
}

// 追加用例：补充覆盖 BuildSmsSendContent 成功路径、getReportClientURL 成功路径、
// getFulfillmentReport 的 UUID 分支，以及 SendEmailMessage 在 Daily 路径下的失败场景
func TestLogic_BuildSmsSendContent_Success(t *testing.T) {
	// 初始化配置，使用 testing 环境
	_ = os.Setenv(env.MOEGO_ENVIRONMENT, env.TESTING)

	// 尝试不同的配置路径，适应不同的运行环境
	configPaths := []string{
		"../../config",                   // 本地 go test
		"backend/app/fulfillment/config", // Bazel 环境
		"config",                         // 其他可能的路径
	}

	var configInitialized bool
	for _, path := range configPaths {
		func() {
			defer func() {
				if r := recover(); r != nil {
					// 这个路径失败，尝试下一个
				}
			}()
			config.Init(path)
			configInitialized = true
		}()
		if configInitialized {
			break
		}
	}

	if !configInitialized {
		t.Skip("Skipping test: unable to initialize config with any known path")
	}

	logic := &Logic{}

	// Grooming 成功路径
	summaryInfoGrooming := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessName: "Test Pet Salon",
		},
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetName: "Buddy",
		},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_GROOMING,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Uuid:     "uuid-xyz",
			Template: &fulfillmentpb.FulfillmentReportTemplate{Title: "Custom Grooming Report"},
		},
	}

	content, err := logic.BuildSmsSendContent(summaryInfoGrooming)
	assert.NoError(t, err)
	assert.NotEmpty(t, content)
	assert.Contains(t, content, "https://my.t2.moego.dev/grooming/report/")
	assert.Contains(t, content, "uuid-xyz")

	// Daycare 成功路径
	summaryInfoDaily := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{BusinessName: "Test Business"},
		PetInfo:      &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{PetName: "Buddy"},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_DAYCARE,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Uuid:     "uuid-abc",
			Template: &fulfillmentpb.FulfillmentReportTemplate{},
		},
	}
	content, err = logic.BuildSmsSendContent(summaryInfoDaily)
	assert.NoError(t, err)
	assert.NotEmpty(t, content)
	assert.Contains(t, content, "https://client.t2.moego.dev/daily/report/")
	assert.Contains(t, content, "uuid-abc")

	// Boarding 按 Daily 处理
	summaryInfoBoarding := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{BusinessName: "Test Business"},
		PetInfo:      &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{PetName: "Buddy"},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			CareType: offeringpb.CareCategory_BOARDING,
			Status:   fulfillmentpb.ReportStatus_DRAFT,
			Uuid:     "uuid-boarding",
			Template: &fulfillmentpb.FulfillmentReportTemplate{},
		},
	}
	content, err = logic.BuildSmsSendContent(summaryInfoBoarding)
	assert.NoError(t, err)
	assert.NotEmpty(t, content)
	assert.Contains(t, content, "https://client.t2.moego.dev/daily/report/")
	assert.Contains(t, content, "uuid-boarding")
}

func TestLogic_getReportClientURL_Success(t *testing.T) {
	_ = os.Setenv(env.MOEGO_ENVIRONMENT, env.TESTING)

	// 尝试不同的配置路径，适应不同的运行环境
	configPaths := []string{
		"../../config",                   // 本地 go test
		"backend/app/fulfillment/config", // Bazel 环境
		"config",                         // 其他可能的路径
	}

	var configInitialized bool
	for _, path := range configPaths {
		func() {
			defer func() {
				if r := recover(); r != nil {
					// 这个路径失败，尝试下一个
				}
			}()
			config.Init(path)
			configInitialized = true
		}()
		if configInitialized {
			break
		}
	}

	if !configInitialized {
		t.Skip("Skipping test: unable to initialize config with any known path")
	}

	logic := &Logic{}
	url, err := logic.getReportClientURL(offeringpb.CareCategory_GROOMING)
	assert.NoError(t, err)
	assert.Equal(t, "https://my.t2.moego.dev/grooming/report/%s", url)

	url, err = logic.getReportClientURL(offeringpb.CareCategory_DAYCARE)
	assert.NoError(t, err)
	assert.Equal(t, "https://client.t2.moego.dev/daily/report/%s", url)
}

func TestLogic_SendEmailMessage_Daily_Error_CreateRecord(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)
	messageRepo := messageMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		sendRecordRepo: sendRecordRepo,
		messageRepo:    messageRepo,
	}

	// ListSendReportRecords -> Count=0, List=[]
	sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)
	sendRecordRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sendrecordrepo.SendRecord{}, nil)

	// SendDailyReportMessageByEmail 返回错误，避免触发后续状态更新
	messageRepo.EXPECT().SendDailyReportMessageByEmail(gomock.Any(), gomock.Any()).Return(nil, assert.AnError)

	// 创建记录
	sendRecordRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{BusinessId: 2},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			Id:            lo.ToPtr(int64(10)),
			CompanyId:     1,
			BusinessId:    2,
			AppointmentId: 100,
			PetId:         200,
			CareType:      offeringpb.CareCategory_DAYCARE,
		},
	}
	req := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: 10,
		StaffId:             999,
		RecipientEmails:     []string{"<EMAIL>"},
	}

	resp, err := logic.SendEmailMessage(ctx, summaryInfo, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int64(10), resp.GetSendResult().GetFulfillmentReportId())
	assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, resp.GetSendResult().GetSendMethod())
}

func TestLogic_SendEmailMessage_NilEmailSubject(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sendRecordRepo := sendrecordMock.NewMockReadWriter(ctrl)
	messageRepo := messageMock.NewMockReadWriter(ctrl)
	reportRepo := reportMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		sendRecordRepo: sendRecordRepo,
		messageRepo:    messageRepo,
		reportRepo:     reportRepo,
	}

	// ListSendReportRecords -> Count=0, List=[]
	sendRecordRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)
	sendRecordRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sendrecordrepo.SendRecord{}, nil)

	// SendDailyReportMessageByEmail - 验证主题生成但发送失败，避免触发后续状态更新
	messageRepo.EXPECT().SendDailyReportMessageByEmail(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, params *message.SendMessageByEmailParams) (*message.SendMessageByEmailResult, error) {
			// 验证主题不为空（应该使用默认主题）
			assert.NotEmpty(t, params.Subject)
			assert.Contains(t, params.Subject, "Report") // 默认主题应该包含 "Report"
			// 返回错误，避免触发状态更新
			return nil, assert.AnError
		})

	// 创建记录
	sendRecordRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	summaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessId:   2,
			BusinessName: "Test Pet Salon",
		},
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetName: "Buddy",
		},
		FulfillmentReport: &fulfillmentpb.FulfillmentReport{
			Id:            lo.ToPtr(int64(10)),
			CompanyId:     1,
			BusinessId:    2,
			AppointmentId: 100,
			PetId:         200,
			CareType:      offeringpb.CareCategory_DAYCARE,
			Status:        fulfillmentpb.ReportStatus_DRAFT,
		},
	}
	req := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: 10,
		StaffId:             999,
		RecipientEmails:     []string{"<EMAIL>"},
		// EmailSubject 故意设置为 nil 来测试默认主题生成
		EmailSubject: nil,
	}

	resp, err := logic.SendEmailMessage(ctx, summaryInfo, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int64(10), resp.GetSendResult().GetFulfillmentReportId())
	assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, resp.GetSendResult().GetSendMethod())
	// 发送失败，但测试的重点是验证 EmailSubject 为 nil 时不会崩溃
	assert.False(t, resp.GetSendResult().GetIsSentSuccess())
}

func TestLogic_IncreaseFulfillmentOpenedCount(t *testing.T) {
	ctx := context.Background()

	t.Run("successful_increase_count", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo: reportRepo,
		}

		req := &fulfillmentpb.IncreaseFulfillmentOpenedCountRequest{
			Uuid: "test-uuid-123",
		}

		// Mock successful increase
		reportRepo.EXPECT().IncreaseOpenedCount(gomock.Any(), "test-uuid-123").Return(nil)

		result, err := logic.IncreaseFulfillmentOpenedCount(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("empty_uuid", func(t *testing.T) {
		logic := &Logic{}

		req := &fulfillmentpb.IncreaseFulfillmentOpenedCountRequest{
			Uuid: "",
		}

		result, err := logic.IncreaseFulfillmentOpenedCount(ctx, req)

		assert.NoError(t, err) // 不应该返回错误，而是静默处理
		assert.NotNil(t, result)
	})

	t.Run("nil_request", func(t *testing.T) {
		logic := &Logic{}

		result, err := logic.IncreaseFulfillmentOpenedCount(ctx, nil)

		assert.NoError(t, err) // 不应该返回错误，而是静默处理
		assert.NotNil(t, result)
	})

	t.Run("share_uuid_prefix", func(t *testing.T) {
		logic := &Logic{}

		req := &fulfillmentpb.IncreaseFulfillmentOpenedCountRequest{
			Uuid: "s_test-uuid-123", // share 模式的 UUID
		}

		result, err := logic.IncreaseFulfillmentOpenedCount(ctx, req)

		assert.NoError(t, err) // share 模式不统计，但不返回错误
		assert.NotNil(t, result)
	})

	t.Run("repo_error_handled_gracefully", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo: reportRepo,
		}

		req := &fulfillmentpb.IncreaseFulfillmentOpenedCountRequest{
			Uuid: "test-uuid-123",
		}

		// Mock repo error
		reportRepo.EXPECT().IncreaseOpenedCount(gomock.Any(), "test-uuid-123").Return(errors.New("database error"))

		result, err := logic.IncreaseFulfillmentOpenedCount(ctx, req)

		assert.NoError(t, err) // 错误应该被优雅处理，不影响用户体验
		assert.NotNil(t, result)
	})
}

func TestLogic_BatchSendFulfillmentReport(t *testing.T) {
	ctx := context.Background()

	t.Run("successful_batch_send_with_not_found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		reportRepo := reportMock.NewMockReadWriter(ctrl)

		logic := &Logic{
			reportRepo: reportRepo,
		}

		req := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			CompanyId:  1,
			BusinessId: 2,
			ReportIds:  []int64{100, 101},
			SendMethod: fulfillmentpb.SendMethod_SMS,
			StaffId:    999,
		}

		// Mock batch get reports - 模拟找不到报告的情况
		reportRepo.EXPECT().FindByID(gomock.Any(), int64(100)).Return(nil, errors.New("not found")).AnyTimes()
		reportRepo.EXPECT().FindByID(gomock.Any(), int64(101)).Return(nil, nil).AnyTimes() // 返回 nil, nil 表示报告不存在

		result, err := logic.BatchSendFulfillmentReport(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.SendResults, 2) // 现在应该返回2个失败结果

		// 验证批量发送状态 - 全部失败
		assert.Equal(t, fulfillmentpb.BatchSendState_ALL_FAILED, result.BatchSendState)

		// 验证失败结果
		for _, sendResult := range result.SendResults {
			assert.Equal(t, fulfillmentpb.SendMethod_SMS, sendResult.SendMethod)
			assert.Contains(t, []int64{100, 101}, sendResult.FulfillmentReportId)
			assert.False(t, sendResult.IsSentSuccess)
			assert.NotEmpty(t, sendResult.ErrorMessage)

			if sendResult.FulfillmentReportId == 100 {
				assert.Contains(t, sendResult.ErrorMessage, "failed to find report")
			} else if sendResult.FulfillmentReportId == 101 {
				assert.Equal(t, "report not found", sendResult.ErrorMessage)
			}
		}
	})

	t.Run("invalid_request", func(t *testing.T) {
		logic := &Logic{}

		req := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			CompanyId:  0, // invalid
			BusinessId: 2,
			ReportIds:  []int64{100},
			SendMethod: fulfillmentpb.SendMethod_SMS,
			StaffId:    999,
		}

		result, err := logic.BatchSendFulfillmentReport(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "company_id must be greater than 0")
	})

	t.Run("empty_report_ids", func(t *testing.T) {
		logic := &Logic{}

		req := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			CompanyId:  1,
			BusinessId: 2,
			ReportIds:  []int64{}, // empty
			SendMethod: fulfillmentpb.SendMethod_SMS,
			StaffId:    999,
		}

		result, err := logic.BatchSendFulfillmentReport(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "report_ids cannot be empty")
	})

	t.Run("unsupported_send_method", func(t *testing.T) {
		logic := &Logic{}

		req := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			CompanyId:  1,
			BusinessId: 2,
			ReportIds:  []int64{100},
			SendMethod: fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED,
			StaffId:    999,
		}

		result, err := logic.BatchSendFulfillmentReport(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "send_method is required")
	})
}

func TestLogic_buildBatchSendState(t *testing.T) {
	logic := &Logic{}

	t.Run("all_success", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{
			{FulfillmentReportId: 1, IsSentSuccess: true},
			{FulfillmentReportId: 2, IsSentSuccess: true},
			{FulfillmentReportId: 3, IsSentSuccess: true},
		}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_ALL_SUCCESS, status)
	})

	t.Run("all_failed", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{
			{FulfillmentReportId: 1, IsSentSuccess: false, ErrorMessage: "error 1"},
			{FulfillmentReportId: 2, IsSentSuccess: false, ErrorMessage: "error 2"},
			{FulfillmentReportId: 3, IsSentSuccess: false, ErrorMessage: "error 3"},
		}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_ALL_FAILED, status)
	})

	t.Run("partial_failed", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{
			{FulfillmentReportId: 1, IsSentSuccess: true},
			{FulfillmentReportId: 2, IsSentSuccess: false, ErrorMessage: "error 2"},
			{FulfillmentReportId: 3, IsSentSuccess: true},
		}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_PARTIAL_FAILED, status)
	})

	t.Run("empty_results", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_BATCH_SEND_STATE_UNSPECIFIED, status)
	})

	t.Run("single_success", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{
			{FulfillmentReportId: 1, IsSentSuccess: true},
		}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_ALL_SUCCESS, status)
	})

	t.Run("single_failed", func(t *testing.T) {
		sendResults := []*fulfillmentpb.FulfillmentReportSendResult{
			{FulfillmentReportId: 1, IsSentSuccess: false, ErrorMessage: "error"},
		}

		status := logic.buildBatchSendState(sendResults)
		assert.Equal(t, fulfillmentpb.BatchSendState_ALL_FAILED, status)
	})
}

func TestLogic_sendSingleReport(t *testing.T) {
	ctx := context.Background()

	// 测试数据
	report := &reportrepo.Report{
		ID:            123,
		CompanyID:     1,
		BusinessID:    2,
		AppointmentID: 100,
		PetID:         200,
		CareType:      int32(offeringpb.CareCategory_GROOMING),
		ServiceDate:   "2024-01-15",
		UUID:          "test-uuid-123",
	}

	req := &fulfillmentpb.BatchSendFulfillmentReportRequest{
		SendMethod: fulfillmentpb.SendMethod_EMAIL,
		StaffId:    999,
	}

	t.Run("successful_email_send", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 创建 mock logic 来测试 sendSingleReport 方法
		logic := &Logic{}

		// 模拟 GetFulfillmentReport 成功
		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		// 模拟 GetReportSummaryInfo 成功
		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		// 模拟 SendEmailMessage 成功
		mockSendResponse := &fulfillmentpb.SendFulfillmentReportResponse{
			SendResult: &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: 123,
				SendMethod:          fulfillmentpb.SendMethod_EMAIL,
				IsSentSuccess:       true,
				ErrorMessage:        "",
			},
		}

		// 创建一个包装器来测试私有方法
		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendEmailMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return mockSendResponse, nil
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.True(t, result.IsSentSuccess)
		assert.Empty(t, result.ErrorMessage)
	})

	t.Run("successful_sms_send", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		smsReq := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			SendMethod: fulfillmentpb.SendMethod_SMS,
			StaffId:    999,
		}

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		mockSendResponse := &fulfillmentpb.SendFulfillmentReportResponse{
			SendResult: &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: 123,
				SendMethod:          fulfillmentpb.SendMethod_SMS,
				IsSentSuccess:       true,
				ErrorMessage:        "",
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendSmsMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return mockSendResponse, nil
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, smsReq)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_SMS, result.SendMethod)
		assert.True(t, result.IsSentSuccess)
		assert.Empty(t, result.ErrorMessage)
	})

	t.Run("get_fulfillment_report_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return nil, errors.New("failed to get report")
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Contains(t, result.ErrorMessage, "failed to get report")
	})

	t.Run("get_report_summary_info_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return nil, errors.New("failed to get summary info")
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Contains(t, result.ErrorMessage, "failed to get report summary info")
	})

	t.Run("send_email_message_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendEmailMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return nil, errors.New("failed to send email")
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Contains(t, result.ErrorMessage, "failed to send email")
	})

	t.Run("send_sms_message_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		smsReq := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			SendMethod: fulfillmentpb.SendMethod_SMS,
			StaffId:    999,
		}

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendSmsMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return nil, errors.New("failed to send sms")
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, smsReq)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_SMS, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Contains(t, result.ErrorMessage, "failed to send sms")
	})

	t.Run("unsupported_send_method", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		unsupportedReq := &fulfillmentpb.BatchSendFulfillmentReportRequest{
			SendMethod: fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED,
			StaffId:    999,
		}

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, unsupportedReq)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Contains(t, result.ErrorMessage, "unsupported send method")
	})

	t.Run("successful_send_with_response_success_false", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		// 模拟发送失败的响应
		mockSendResponse := &fulfillmentpb.SendFulfillmentReportResponse{
			SendResult: &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: 123,
				SendMethod:          fulfillmentpb.SendMethod_EMAIL,
				IsSentSuccess:       false,
				ErrorMessage:        "send failed",
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendEmailMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return mockSendResponse, nil
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess)
		assert.Equal(t, "send failed", result.ErrorMessage)
	})

	t.Run("nil_send_response", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendEmailMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return nil, nil // 返回 nil 响应
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess) // 默认为 false
		assert.Empty(t, result.ErrorMessage)  // 默认为空
	})

	t.Run("response_with_nil_send_result", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		logic := &Logic{}

		mockReport := &Report{
			ID:            123,
			CompanyID:     1,
			BusinessID:    2,
			AppointmentID: 100,
			PetID:         200,
			CareType:      offeringpb.CareCategory_GROOMING,
			ServiceDate:   "2024-01-15",
			UUID:          "test-uuid-123",
		}

		mockSummaryInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo{
			BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
				BusinessName: "Test Business",
			},
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
				PetName: "Test Pet",
			},
			FulfillmentReport: &fulfillmentpb.FulfillmentReport{
				Id:       lo.ToPtr(int64(123)),
				CareType: offeringpb.CareCategory_GROOMING,
			},
		}

		// 模拟响应中 SendResult 为 nil
		mockSendResponse := &fulfillmentpb.SendFulfillmentReportResponse{
			SendResult: nil,
		}

		testLogic := &testLogicWrapper{
			Logic: logic,
			mockGetFulfillmentReport: func(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {
				return mockReport, nil
			},
			mockGetReportSummaryInfo: func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
				return mockSummaryInfo, nil
			},
			mockSendEmailMessage: func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
				return mockSendResponse, nil
			},
		}

		result := testLogic.sendSingleReportWrapper(ctx, report, req)

		assert.NotNil(t, result)
		assert.Equal(t, int64(123), result.FulfillmentReportId)
		assert.Equal(t, fulfillmentpb.SendMethod_EMAIL, result.SendMethod)
		assert.False(t, result.IsSentSuccess) // 默认为 false
		assert.Empty(t, result.ErrorMessage)  // 默认为空
	})
}

// testLogicWrapper 用于测试私有方法的包装器
type testLogicWrapper struct {
	*Logic
	mockGetFulfillmentReport func(ctx context.Context, req *GetFulfillmentReport) (*Report, error)
	mockGetReportSummaryInfo func(ctx context.Context, report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error)
	mockSendEmailMessage     func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error)
	mockSendSmsMessage       func(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo, req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error)
}

// sendSingleReportWrapper 包装私有方法以便测试
func (w *testLogicWrapper) sendSingleReportWrapper(ctx context.Context, report *reportrepo.Report, req *fulfillmentpb.BatchSendFulfillmentReportRequest) *fulfillmentpb.FulfillmentReportSendResult {
	// 构建发送结果，默认为失败
	sendResult := &fulfillmentpb.FulfillmentReportSendResult{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		IsSentSuccess:       false,
		ErrorMessage:        "",
	}

	// 获取完整的报告信息
	logicReport, err := w.mockGetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID:            report.ID,
		CompanyID:     report.CompanyID,
		BusinessID:    report.BusinessID,
		AppointmentID: report.AppointmentID,
		PetID:         report.PetID,
		CareType:      offeringpb.CareCategory(report.CareType),
		ServiceDate:   report.ServiceDate,
		UUID:          report.UUID,
	})
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report: %v", err)
		return sendResult
	}

	// 获取报告摘要信息
	reportSummaryInfo, err := w.mockGetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report summary info: %v", err)
		return sendResult
	}

	// 构建单个发送请求
	sendReq := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		StaffId:             req.GetStaffId(),
	}

	// 根据发送方法发送报告
	var sendResponse *fulfillmentpb.SendFulfillmentReportResponse
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
		if w.mockSendEmailMessage != nil {
			sendResponse, err = w.mockSendEmailMessage(ctx, reportSummaryInfo, sendReq)
		}
	case fulfillmentpb.SendMethod_SMS:
		if w.mockSendSmsMessage != nil {
			sendResponse, err = w.mockSendSmsMessage(ctx, reportSummaryInfo, sendReq)
		}
	default:
		sendResult.ErrorMessage = "unsupported send method"
		return sendResult
	}

	if err != nil {
		sendResult.ErrorMessage = err.Error()
		return sendResult
	}

	if sendResponse != nil && sendResponse.GetSendResult() != nil {
		sendResult.IsSentSuccess = sendResponse.GetSendResult().GetIsSentSuccess()
		sendResult.ErrorMessage = sendResponse.GetSendResult().GetErrorMessage()
	}

	return sendResult
}
