package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	serviceinstance "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// MockServiceInstanceReadWriter 模拟 ServiceInstanceReadWriter 接口
type MockServiceInstanceReadWriter struct {
	mock.Mock
}

func (m *MockServiceInstanceReadWriter) List(ctx context.Context, baseParam *serviceinstance.BaseParam, filter *serviceinstance.Filter) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, baseParam, filter)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) GetByIDs(ctx context.Context, ids []int64) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) BatchCreate(ctx context.Context, serviceInstances []*serviceinstance.ServiceInstance) error {
	args := m.Called(ctx, serviceInstances)
	return args.Error(0)
}

func (m *MockServiceInstanceReadWriter) Create(ctx context.Context, si *serviceinstance.ServiceInstance) (int, error) {
	args := m.Called(ctx, si)
	return args.Int(0), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) Update(ctx context.Context, si *serviceinstance.ServiceInstance) error {
	args := m.Called(ctx, si)
	return args.Error(0)
}

func (m *MockServiceInstanceReadWriter) GetByID(ctx context.Context, id int) (*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) Delete(ctx context.Context, id int) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockServiceInstanceReadWriter) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, appointmentID)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, appointmentIDs)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) GetLatestByPetID(ctx context.Context, businessID, companyID int32, petID int64) (*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, businessID, companyID, petID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*serviceinstance.ServiceInstance), args.Error(1)
}

func TestNew(t *testing.T) {
	// 测试构造函数结构，不测试真实的数据库连接
	// 使用mock来避免数据库依赖
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	assert.NotNil(t, logic)
	assert.NotNil(t, logic.serviceInstanceCli)
}

func TestListServiceInstance_Success(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		Filter: &pb.ServiceInstanceFilter{
			PetIds:      []int64{1, 2},
			CustomerIds: []int64{3, 4},
			CareTypes:   []offeringpb.CareCategory{offeringpb.CareCategory_BOARDING},
			DateTypes:   []pb.DateType{pb.DateType_DATE_TYPE_SPECIFIC_DATE},
			RootIds:     []int64{5, 6},
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			BusinessID:       2,
			CustomerID:       3,
			CompanyID:        1,
			AppointmentID:    10,
			PetID:            1,
			CareType:         2,
			DateType:         2,
			ServiceFactoryID: 100,
			ParentID:         0,
			RootParentID:     5,
			StartDate:        serviceinstance.TimeToPgDate(time.Now()),
			EndDate:          serviceinstance.TimeToPgDate(time.Now().Add(2 * time.Hour)),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.ServiceInstances, 1)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].Id)
	assert.Equal(t, int64(2), resp.ServiceInstances[0].BusinessId)
	assert.True(t, resp.IsEnd)
	assert.Equal(t, int32(1), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_WithDefaultLimit(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  0, // 使用默认限制
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd)
	assert.Equal(t, int32(0), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_WithNilPagination(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		// Pagination is nil
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_WithoutTimeRange(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		// No time range
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_ValidationError_BusinessId(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 0,
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "business_id must be greater than 0")
}

func TestListServiceInstance_ValidationError_CompanyId(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  0,
		BusinessId: 1,
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "company_id must be greater than 0")
}

func TestListServiceInstance_ValidationError_TimeRange(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "start_time must be before end_time")
}

func TestListServiceInstance_DatabaseError(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return([]*serviceinstance.ServiceInstance{}, assert.AnError)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, assert.AnError, err)

	mockCli.AssertExpectations(t)
}

func TestGetServiceInstanceByIds_Success(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	mockInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			BusinessID:       2,
			CustomerID:       3,
			CompanyID:        1,
			AppointmentID:    10,
			PetID:            1,
			CareType:         1,
			DateType:         1,
			ServiceFactoryID: 100,
			ParentID:         0,
			RootParentID:     5,
			StartDate:        serviceinstance.TimeToPgDate(time.Now()),
			EndDate:          serviceinstance.TimeToPgDate(time.Now().Add(2 * time.Hour)),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
		{
			ID:               2,
			BusinessID:       2,
			CustomerID:       4,
			CompanyID:        1,
			AppointmentID:    11,
			PetID:            2,
			CareType:         3,
			DateType:         1,
			ServiceFactoryID: 101,
			ParentID:         1,
			RootParentID:     5,
			StartDate:        serviceinstance.TimeToPgDate(time.Now().Add(1 * time.Hour)),
			EndDate:          serviceinstance.TimeToPgDate(time.Now().Add(3 * time.Hour)),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	}

	mockCli.On("GetByIDs", mock.Anything, []int64{1, 2, 3}).Return(mockInstances, nil)

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.ServiceInstances, 2)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].Id)
	assert.Equal(t, int64(2), resp.ServiceInstances[1].Id)

	mockCli.AssertExpectations(t)
}

func TestGetServiceInstanceByIds_ValidationError_EmptyIds(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{},
	}

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "service_instance_ids cannot be empty")
}

func TestGetServiceInstanceByIds_DatabaseError(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	mockCli.On("GetByIDs", mock.Anything, []int64{1, 2, 3}).Return([]*serviceinstance.ServiceInstance{}, assert.AnError)

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, assert.AnError, err)

	mockCli.AssertExpectations(t)
}

func TestVerifyListServiceInstanceRequest_ValidRequest(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
	}

	err := verifyListServiceInstanceRequest(req)
	assert.NoError(t, err)
}

func TestVerifyListServiceInstanceRequest_ValidRequest_WithoutTime(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
	}

	err := verifyListServiceInstanceRequest(req)
	assert.NoError(t, err)
}

func TestVerifyListServiceInstanceRequest_ValidRequest_OnlyStartTime(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
	}

	err := verifyListServiceInstanceRequest(req)
	assert.NoError(t, err)
}

func TestVerifyGetServiceInstanceByIdsRequest_ValidRequest(t *testing.T) {
	req := &pb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	err := verifyGetServiceInstanceByIDsRequest(req)
	assert.NoError(t, err)
}

func TestBuildServiceInstanceFilter_WithAllFilters(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		Filter: &pb.ServiceInstanceFilter{
			PetIds:      []int64{1, 2, 3},
			CustomerIds: []int64{4, 5, 6},
			CareTypes:   []offeringpb.CareCategory{offeringpb.CareCategory_BOARDING, offeringpb.CareCategory_DAYCARE},
			DateTypes:   []pb.DateType{pb.DateType_DATE_TYPE_SPECIFIC_DATE, pb.DateType_DATE_TYPE_DATE_EVERYDAY},
			RootIds:     []int64{7, 8, 9},
		},
	}

	filter := buildServiceInstanceFilter(req)

	assert.NotNil(t, filter)
	assert.Equal(t, []int64{1, 2, 3}, filter.PetIDs)
	assert.Equal(t, []int64{4, 5, 6}, filter.CustomerIDs)
	assert.Equal(t, []int32{2, 3}, filter.CareTypes)
	assert.Equal(t, []int32{2, 1}, filter.DateTypes)
	assert.Equal(t, []int32{7, 8, 9}, filter.RootIDs)
}

func TestBuildServiceInstanceFilter_WithNilFilter(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{}

	filter := buildServiceInstanceFilter(req)

	assert.Nil(t, filter)
}

func TestBuildServiceInstanceFilter_WithEmptyFilter(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		Filter: &pb.ServiceInstanceFilter{},
	}

	filter := buildServiceInstanceFilter(req)

	assert.NotNil(t, filter)
	assert.Nil(t, filter.PetIDs)
	assert.Nil(t, filter.CustomerIDs)
	assert.Nil(t, filter.CareTypes)
	assert.Nil(t, filter.DateTypes)
	assert.Nil(t, filter.RootIDs)
}

func TestBuildServiceInstanceFilter_WithPartialFilters(t *testing.T) {
	req := &pb.ListServiceInstanceRequest{
		Filter: &pb.ServiceInstanceFilter{
			PetIds:    []int64{1, 2},
			CareTypes: []offeringpb.CareCategory{offeringpb.CareCategory_GROOMING},
		},
	}

	filter := buildServiceInstanceFilter(req)

	assert.NotNil(t, filter)
	assert.Equal(t, []int64{1, 2}, filter.PetIDs)
	assert.Nil(t, filter.CustomerIDs)
	assert.Equal(t, []int32{1}, filter.CareTypes)
	assert.Nil(t, filter.DateTypes)
	assert.Nil(t, filter.RootIDs)
}

func TestConvertToProtoServiceInstance(t *testing.T) {
	now := time.Now()
	instance := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       2,
		CustomerID:       3,
		CompanyID:        4,
		AppointmentID:    5,
		PetID:            6,
		CareType:         2,
		DateType:         1,
		ServiceFactoryID: 7,
		ParentID:         8,
		RootParentID:     9,
		StartDate:        serviceinstance.TimeToPgDate(now),
		EndDate:          serviceinstance.TimeToPgDate(now.Add(2 * time.Hour)),
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	protoInstance := convertToProtoServiceInstance(instance)

	assert.Equal(t, int64(1), protoInstance.Id)
	assert.Equal(t, int64(2), protoInstance.BusinessId)
	assert.Equal(t, int64(3), protoInstance.CustomerId)
	assert.Equal(t, int64(4), protoInstance.CompanyId)
	assert.Equal(t, int64(5), protoInstance.AppointmentId)
	assert.Equal(t, int64(6), protoInstance.PetId)
	assert.Equal(t, offeringpb.CareCategory_BOARDING, protoInstance.CareType)
	assert.Equal(t, pb.DateType_DATE_TYPE_DATE_EVERYDAY, protoInstance.DateType)
	assert.Equal(t, int64(7), protoInstance.ServiceFactoryId)
	assert.Equal(t, int64(8), protoInstance.ParentId)
	assert.Equal(t, int64(9), protoInstance.RootId)
	assert.NotNil(t, protoInstance.StartDate)
	assert.NotNil(t, protoInstance.EndDate)
	assert.NotNil(t, protoInstance.CreatedAt)
	assert.NotNil(t, protoInstance.UpdatedAt)
}

func TestListServiceInstance_IsEndCalculation_NotLastPage(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  5,
		},
	}

	// 返回5个实例，等于limit，应该不是最后一页
	mockInstances := make([]*serviceinstance.ServiceInstance, 5)
	for i := range mockInstances {
		mockInstances[i] = &serviceinstance.ServiceInstance{
			ID:        i + 1,
			StartDate: serviceinstance.TimeToPgDate(time.Now()),
			EndDate:   serviceinstance.TimeToPgDate(time.Now()),
		}
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.False(t, resp.IsEnd) // 返回数量等于limit，不是最后一页
	assert.Equal(t, int32(5), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_IsEndCalculation_LastPage(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &pb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 返回3个实例，小于limit，应该是最后一页
	mockInstances := make([]*serviceinstance.ServiceInstance, 3)
	for i := range mockInstances {
		mockInstances[i] = &serviceinstance.ServiceInstance{
			ID:        i + 1,
			StartDate: serviceinstance.TimeToPgDate(time.Now()),
			EndDate:   serviceinstance.TimeToPgDate(time.Now()),
		}
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd) // 返回数量小于limit，是最后一页
	assert.Equal(t, int32(3), resp.Total)

	mockCli.AssertExpectations(t)
}
