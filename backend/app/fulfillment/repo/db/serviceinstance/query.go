package serviceinstance

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// List 基于BaseParam和Filter查询ServiceInstance记录
func (i *impl) List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance

	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&ServiceInstance{})

	if baseParam == nil {
		return []*ServiceInstance{}, nil
	}

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, baseParam)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	// 应用分页
	query = i.applyPagination(query, baseParam.PaginationInfo)

	// 按创建时间倒序排列
	query = query.Clauses(clause.OrderBy{Columns: []clause.OrderByColumn{
		{Column: clause.Column{Name: ColumnCreatedAt}, Desc: true},
	}})

	err := query.Find(&instances).Error
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, baseParam *BaseParam) *gorm.DB {
	clauses := []clause.Expression{}
	if baseParam.BusinessID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnBusinessID, Value: baseParam.BusinessID})
	}
	if baseParam.CompanyID != 0 {
		clauses = append(clauses, clause.Eq{Column: ColumnCompanyID, Value: baseParam.CompanyID})
	}
	if !baseParam.StartTime.IsZero() {
		clauses = append(clauses, clause.Gte{Column: ColumnCreatedAt, Value: baseParam.StartTime})
	}
	if !baseParam.EndTime.IsZero() {
		clauses = append(clauses, clause.Lte{Column: ColumnCreatedAt, Value: baseParam.EndTime})
	}

	return query.Clauses(clauses...)
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	clauses := []clause.Expression{}

	if len(filter.PetIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnPetID, Values: convertToInterface(filter.PetIDs)})
	}
	if len(filter.CustomerIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnCustomerID, Values: convertToInterface(filter.CustomerIDs)})
	}
	if len(filter.CareTypes) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnCareType, Values: convertToInterface(filter.CareTypes)})
	}
	if len(filter.DateTypes) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnDateType, Values: convertToInterface(filter.DateTypes)})
	}
	if len(filter.RootIDs) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnRootID, Values: convertToInterface(filter.RootIDs)})
	}

	if len(clauses) > 0 {
		query = query.Clauses(clauses...)
	}

	return query
}

// applyPagination 应用分页条件
func (i *impl) applyPagination(query *gorm.DB, paginationInfo *PaginationInfo) *gorm.DB {
	if paginationInfo != nil {
		if paginationInfo.Offset > 0 {
			query = query.Offset(int(paginationInfo.Offset))
		}
		if paginationInfo.Limit > 0 {
			query = query.Limit(int(paginationInfo.Limit))
		}
	}

	return query
}

func (i *impl) GetByIDs(ctx context.Context, ids []int64) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := i.db.WithContext(ctx).Clauses(clause.IN{
		Column: ColumnID,
		Values: convertToInterface(ids),
	}).Find(&instances).Error; err != nil {
		return nil, err
	}

	return instances, nil
}

func (i *impl) Delete(ctx context.Context, id int) error {
	return i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnID,
		Value:  id,
	}).Delete(&ServiceInstance{}).Error
}

func (i *impl) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnAppointmentID,
		Value:  appointmentID,
	}).Find(&instances).Error; err != nil {
		return nil, err
	}

	return instances, nil
}

func (i *impl) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := i.db.WithContext(ctx).Clauses(clause.IN{
		Column: ColumnAppointmentID,
		Values: convertToInterface(appointmentIDs),
	}).Find(&instances).Error; err != nil {
		return nil, err
	}

	return instances, nil
}

func (i *impl) GetByID(ctx context.Context, id int) (*ServiceInstance, error) {
	var instance ServiceInstance
	if err := i.db.WithContext(ctx).Clauses(clause.Eq{
		Column: ColumnID,
		Value:  id,
	}).First(&instance).Error; err != nil {
		return nil, err
	}

	return &instance, nil
}

// GetLatestByPetID 获取指定宠物的最新service instance
func (i *impl) GetLatestByPetID(ctx context.Context, businessID,
	companyID int32, petID int64) (*ServiceInstance, error) {
	var instance ServiceInstance
	err := i.db.WithContext(ctx).
		Clauses(
			clause.Eq{Column: ColumnBusinessID, Value: businessID},
			clause.Eq{Column: ColumnCompanyID, Value: companyID},
			clause.Eq{Column: ColumnPetID, Value: petID},
			clause.OrderBy{Columns: []clause.OrderByColumn{
				{Column: clause.Column{Name: ColumnCreatedAt}, Desc: true},
			}},
		).
		Limit(1).
		First(&instance).Error
	if err != nil {
		return nil, err
	}

	return &instance, nil
}

// convertToInterface 将各种类型的切片转换为[]interface{}
func convertToInterface[T any](slice []T) []interface{} {
	if len(slice) == 0 {
		return nil
	}
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}

	return result
}
