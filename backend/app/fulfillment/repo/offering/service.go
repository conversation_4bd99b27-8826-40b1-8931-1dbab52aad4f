package offering

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type ReadWriter interface {
	GetService(ctx context.Context, serviceTemplateID int64) (*offeringpb.Service, error)
}

type impl struct {
	serviceCli offeringpb.ServiceServiceClient
}

func New() ReadWriter {
	return &impl{
		serviceCli: grpc.NewClient("moego-offering", offeringpb.NewServiceServiceClient),
	}
}

func (i *impl) GetService(ctx context.Context, serviceTemplateID int64) (*offeringpb.Service, error) {
	serviceTemplateResp, err := i.serviceCli.GetService(ctx, &offeringpb.GetServiceRequest{
		ServiceId: serviceTemplateID,
	})
	if err != nil {
		return nil, err
	}

	return serviceTemplateResp.GetService(), nil
}
