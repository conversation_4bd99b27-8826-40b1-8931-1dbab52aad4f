package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/appointment"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type AppointmentService struct {
	// TODO(aiden): 后面需要删掉
	pb.UnimplementedAppointmentServiceServer
	appointment *appointment.Logic
}

func NewAppointmentService() *AppointmentService {
	return &AppointmentService{
		appointment: appointment.New(),
	}
}

func (a *AppointmentService) CreateAppointment(ctx context.Context,
	req *pb.CreateAppointmentRequest) (*pb.CreateAppointmentResponse, error) {
	return a.appointment.CreateAppointment(ctx, req)
}

func (a *AppointmentService) ListAppointment(ctx context.Context,
	req *pb.ListAppointmentRequest) (*pb.ListAppointmentResponse, error) {
	return a.appointment.ListAppointment(ctx, req)
}

func (a *AppointmentService) UpdateAppointment(ctx context.Context,
	req *pb.UpdateAppointmentRequest) (*pb.UpdateAppointmentResponse, error) {
	return a.appointment.UpdateAppointment(ctx, req)
}

func (a *AppointmentService) GetAppointmentByIDs(ctx context.Context,
	req *pb.GetAppointmentByIDsRequest) (*pb.GetAppointmentByIDsResponse, error) {
	return a.appointment.GetAppointmentByIDs(ctx, req)
}

func (a *AppointmentService) GetLastAppointmentByPetID(ctx context.Context,
	req *pb.GetLastAppointmentByPetIDRequest) (*pb.GetLastAppointmentByPetIDResponse, error) {
	return a.appointment.GetLastAppointmentByPetID(ctx, req)
}

func (a *AppointmentService) ListCustomerPackages(ctx context.Context,
	req *pb.ListCustomerPackagesRequest) (*pb.ListCustomerPackagesResponse, error) {
	return a.appointment.ListCustomerPackages(ctx, req)
}
