package fulfillmentutils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsNormal(t *testing.T) {
	t.Run("test_is_normal_int_types", func(t *testing.T) {
		// int
		assert.True(t, IsNormal(1))
		assert.<PERSON>als<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>(0))
		assert.Fals<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>(-1))

		// int64
		assert.True(t, <PERSON><PERSON>orm<PERSON>(int64(1)))
		assert.Fals<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>(int64(0)))
		assert.False(t, <PERSON>Normal(int64(-1)))

		// uint64
		assert.True(t, IsNormal(uint64(1)))
		assert.False(t, <PERSON>Normal(uint64(0)))
	})

	t.Run("test_is_normal_float_types", func(t *testing.T) {
		// float32
		assert.True(t, IsNormal(float32(1.0)))
		assert.True(t, <PERSON><PERSON>orm<PERSON>(float32(0.1)))
		assert.False(t, <PERSON><PERSON><PERSON><PERSON>(float32(0.0)))
		assert.False(t, <PERSON><PERSON><PERSON><PERSON>(float32(-1.0)))

		// float64
		assert.True(t, <PERSON><PERSON><PERSON><PERSON>(1.0))
		assert.True(t, <PERSON>Norm<PERSON>(0.1))
		assert.False(t, IsNormal(0.0))
		assert.False(t, IsNormal(-1.0))
	})
}
