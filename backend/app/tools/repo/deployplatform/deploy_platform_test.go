package deployplatform

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	toolsutils "github.com/MoeGolibrary/moego/backend/app/tools/utils"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

const (
	name      = "112233"
	title     = "testProject"
	createdBy = "Zihao"
	phaseType = PhaseCI
	phaseName = "1234567890"
)

type CreateDeployTaskTestSuite struct {
	suite.Suite
}

func TestCreateDeployTask(t *testing.T) {
	// t.Skip("skip db test in online env")
	suite.Run(t, new(CreateDeployTaskTestSuite))
}

var defaultConfigPath = "/config/local/config.yaml"

var setupTestSvc sync.Once

func onceSetupTestSvc() {
	setupTestSvc.Do(func() {
		rpc.ServerConfigPath = toolsutils.GetProjectToolsDir() + defaultConfigPath
		_ = rpc.NewServer()
	})
}

func (t *CreateDeployTaskTestSuite) SetupSuite() {
	onceSetupTestSvc()
}

func (t *CreateDeployTaskTestSuite) testDelete(manager Deployer, taskID int64) {
	// 删除测试数据
	err := manager.DeleteDeployTask(context.TODO(), taskID)
	t.Nil(err)
}

func (t *CreateDeployTaskTestSuite) TestGetPhasesAndLogs() {
	manager := NewDeployer()
	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID

	defer func() {
		t.testDelete(manager, taskID)
	}()

	phases, logs, err := manager.GetPhasesAndLogs(context.TODO(), &entity.DeployTask{ID: taskID})
	t.Condition(func() (success bool) {
		return len(phases) == 1
	})
	t.Condition(func() (success bool) {
		return len(logs) > 0
	})
	t.Nil(err)

	t.Equal(phases[0].DeployTaskID, taskID)
	t.Equal(phases[0].Type, PhaseCI)
	t.Equal(phases[0].State, Init)
}

func (t *CreateDeployTaskTestSuite) TestGetTaskByID() {
	manager := NewDeployer()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer func() {
		t.testDelete(manager, taskID)
	}()

	task, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	t.Nil(err)
	t.Equal(task.ID, taskID)
	t.Equal(task.Name, name)
	t.Equal(task.Title, title)
	t.Equal(task.CreatedBy, createdBy)
	t.Equal(task.State, Init)
	t.Equal(task.CurrentPhase, PhaseCI)
}

func (t *CreateDeployTaskTestSuite) TestGetDeployTasksNotEnded() {
	manager := NewDeployer()
	afterTime := time.Now()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer t.testDelete(manager, taskID)

	tasks, err := manager.GetDeployTasksNotEnded(context.TODO(), afterTime)
	t.Nil(err)
	t.T().Logf("tasks: %v", tasks)

	targetTask, has := lo.Find(tasks, func(task *entity.DeployTask) bool {
		return task.ID == taskID
	})
	t.T().Logf("targetTask: %v", targetTask)
	t.Equal(true, has)
	t.Equal(targetTask.ID, taskID)
	t.Equal(task.Name, name)
	t.Equal(task.Title, title)
	t.Equal(targetTask.CreatedBy, createdBy)
	t.Equal(targetTask.State, Init)
	t.Equal(targetTask.CurrentPhase, PhaseCI)
	t.Equal(task.Description, targetTask.Description) // 添加: 验证 Description 字段

	err = manager.EndDeployTask(context.TODO(), taskID, `{}`)
	t.Nil(err)

	tasks, err = manager.GetDeployTasksNotEnded(context.TODO(), afterTime)
	t.Nil(err)
	targetTask, has = lo.Find(tasks, func(task *entity.DeployTask) bool {
		return task.ID == taskID
	})
	t.T().Logf("targetTask: %v", targetTask)
	t.Equal(false, has)
}

func (t *CreateDeployTaskTestSuite) TestDeleteDeployTask() {
	manager := NewDeployer()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID

	err = manager.DeleteDeployTask(context.TODO(), taskID)
	t.Nil(err)

	_, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	t.NotNil(err)
	t.True(errors.Is(err, gorm.ErrRecordNotFound))
}

func (t *CreateDeployTaskTestSuite) TestGetDeployTaskByName() {
	manager := NewDeployer()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	defer t.testDelete(manager, task.ID)

	retrievedTask, err := manager.GetDeployTaskByName(context.TODO(), name)
	t.Nil(err)
	t.Equal(task.ID, retrievedTask.ID)
	t.Equal(task.Name, retrievedTask.Name)
	t.Equal(task.Title, retrievedTask.Title)
	t.Equal(task.CreatedBy, retrievedTask.CreatedBy)
	t.Equal(task.State, retrievedTask.State)
	t.Equal(task.CurrentPhase, retrievedTask.CurrentPhase)
	t.Equal(task.Description, retrievedTask.Description) // 添加: 验证 Description 字段
}

func (t *CreateDeployTaskTestSuite) TestCreateDeployTask() {
	manager := NewDeployer()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	defer t.testDelete(manager, task.ID) // 删除: defer func() { t.testDelete(manager, task.ID) }()

	t.NotNil(task)
	t.Equal(name, task.Name)
	t.Equal(title, task.Title)
	t.Equal(createdBy, task.CreatedBy)
	t.Equal(Init, task.State)
	t.Equal(PhaseCI, task.CurrentPhase)
	t.Equal("", task.Description) // 添加: 验证 Description 字段
}

func (t *CreateDeployTaskTestSuite) TestUpdatePhaseAndTaskState() {
	manager := NewDeployer()

	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer t.testDelete(manager, taskID)
	task, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	phases, _, err := manager.GetPhasesAndLogs(context.TODO(), &entity.DeployTask{ID: taskID})
	t.Nil(err)
	t.Condition(func() (success bool) {
		return len(phases) == 1
	})
	t.Equal(Init, phases[0].State)
	t.Equal(Init, task.State)
	t.Equal(PhaseCI, phases[0].Type)
	t.Equal(PhaseCI, task.CurrentPhase)

	err = manager.UpdatePhaseAndTaskState(context.TODO(), phases[0], Running, `{}`)
	t.Nil(err)

	task, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	phases, _, err = manager.GetPhasesAndLogs(context.TODO(), &entity.DeployTask{ID: taskID})
	t.Nil(err)
	t.Equal(Running, phases[0].State)
	t.Equal(Running, task.State)
	t.Equal(PhaseCI, task.CurrentPhase) // 添加: 验证 CurrentPhase 字段
}

// UpdatePhase
func (t *CreateDeployTaskTestSuite) TestUpdatePhase() {
	manager := NewDeployer()
	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer t.testDelete(manager, taskID)

	phase, err := manager.GetDeployPhase(context.TODO(), taskID, PhaseCI)
	t.Nil(err)
	t.Equal(Init, phase.State)
	t.Equal(PhaseCI, phase.Type)
	t.Equal(task.ID, phase.DeployTaskID)

	phase.Parameters = entity.JSONB(`{"foo":"bar"}`)
	err = manager.UpdatePhase(context.TODO(), phase, `{}`)
	t.Nil(err)

	phase, err = manager.GetDeployPhase(context.TODO(), taskID, PhaseCI)
	t.Nil(err)
	t.Equal(entity.JSONB(`{"foo": "bar"}`), phase.Parameters)

}

func (t *CreateDeployTaskTestSuite) TestCreatePhaseAndUpdateTask() {
	manager := NewDeployer()
	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer t.testDelete(manager, taskID)

	err = manager.CreatePhaseAndUpdateTask(context.TODO(),
		task, PhaseCanary, "0987654321", Init, `{}`)
	t.Nil(err)

	task, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	t.Nil(err)
	phases, logs, err := manager.GetPhasesAndLogs(context.TODO(),
		task)
	t.Nil(err)
	t.Condition(func() (success bool) {
		// 有CI和Canary两个phase
		return len(phases) == 2 && len(logs) == 3
	})
	ci, has := lo.Find(phases, func(phase *entity.DeployPhase) bool {
		return phase.Type == PhaseCI
	})
	t.Equal(true, has)
	canary, has := lo.Find(phases, func(phase *entity.DeployPhase) bool {
		return phase.Type == PhaseCanary
	})
	t.Equal(true, has)

	t.Equal(Init, ci.State)
	t.Equal(Init, task.State)
	t.Equal(PhaseCanary, task.CurrentPhase) // 添加: 验证 CurrentPhase 字段

	t.Equal(PhaseCanary, canary.Type)

	// 验证Logs
	ciInitLog, has := lo.Find(logs, func(log *entity.DeployLog) bool {
		return log.Phase == PhaseCI && log.State == Init && log.Type == "InitPhase"
	})
	t.Equal(true, has)
	t.Equal(taskID, ciInitLog.DeployTaskID)
	t.Equal(taskID, task.ID)

	canaryInitLog, has := lo.Find(logs, func(log *entity.DeployLog) bool {
		return log.Phase == PhaseCanary && log.State == Init
	})
	t.Equal(true, has)
	t.Equal(taskID, canaryInitLog.DeployTaskID)
	t.Equal(taskID, task.ID)
	t.Equal("CreatePhase", canaryInitLog.Type)
}

// EndDeployTask
func (t *CreateDeployTaskTestSuite) TestEndDeployTask() {
	manager := NewDeployer()
	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	t.Equal(false, task.EndedAt.Valid)
	taskID := task.ID
	defer t.testDelete(manager, taskID)

	err = manager.EndDeployTask(context.TODO(), taskID, `{}`)
	t.Nil(err)
	task, err = manager.GetDeployTaskByID(context.TODO(), taskID)
	t.Nil(err)
	t.Equal(true, task.EndedAt.Valid)
}

// EndDeployPhase
func (t *CreateDeployTaskTestSuite) TestEndDeployPhase() {
	manager := NewDeployer()
	task, err := manager.GetOrCreateDeployTask(context.TODO(), name, title, "", createdBy, phaseType, phaseName, `{}`)
	t.Nil(err)
	taskID := task.ID
	defer t.testDelete(manager, taskID)

	phase, err := manager.GetDeployPhase(context.TODO(), taskID, phaseType)
	t.Nil(err)
	t.Equal(Init, phase.State)
	t.Equal(phaseType, phase.Type)
	t.Equal(task.ID, phase.DeployTaskID)
	t.Equal(false, phase.EndedAt.Valid)

	err = manager.EndDeployPhase(context.TODO(), phase, `{}`)
	t.Nil(err)
	phase, err = manager.GetDeployPhase(context.TODO(), taskID, PhaseCI)
	t.Nil(err)
	t.Equal(true, phase.EndedAt.Valid)
}
