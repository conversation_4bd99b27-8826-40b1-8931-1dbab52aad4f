package datadog

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/DataDog/datadog-api-client-go/v2/api/datadog"
	"github.com/DataDog/datadog-api-client-go/v2/api/datadogV2"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// DDClient represents a Datadog client wrapper
type DDClient struct {
	client *datadogV2.LogsApi
	ctx    context.Context
}

// LogEntry represents a simplified log entry with request body and account ID
type LogEntry struct {
	ID           string      `json:"id"`
	Timestamp    *time.Time  `json:"timestamp,omitempty"`
	Message      string      `json:"message"`
	RequestBody  interface{} `json:"request_body,omitempty"`
	MoeAccountID interface{} `json:"moe_account_id,omitempty"`
}

// NewDDClient creates a new Datadog client instance
func NewDDClient(config *configinit.MessageConfig) (*DDClient, error) {
	// Initialize Datadog configuration
	configuration := datadog.NewConfiguration()

	// Create context with API keys
	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: config.MessageAlert.DatadogAPIToken,
			},
			"appKeyAuth": {
				Key: config.MessageAlert.DatadogAPPToken,
			},
		},
	)

	// Set the site context
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})

	// Create API client
	client := datadog.NewAPIClient(configuration)
	api := datadogV2.NewLogsApi(client)

	return &DDClient{
		client: api,
		ctx:    ctx,
	}, nil
}

// MessageLogs queries Datadog for message logs in the last 5 minutes
func (dd *DDClient) MessageLogs() ([]map[string]interface{}, error) {
	endTime := time.Now().UTC()
	startTime := endTime.Add(-1 * time.Second * 75)

	startTimeStr := startTime.Format(time.RFC3339)
	endTimeStr := endTime.Format(time.RFC3339)

	searchFilter := "@path:/message/send/toCustomer/one OR @path:/message/send/toCustomer/batch/asyn"
	body := datadogV2.ListLogsOptionalParameters{
		Body: &datadogV2.LogsListRequest{
			Filter: &datadogV2.LogsQueryFilter{
				Query: &searchFilter,
				From:  &startTimeStr,
				To:    &endTimeStr,
			},
			Page: &datadogV2.LogsListRequestPage{
				Limit: datadog.PtrInt32(100),
			},
		},
	}
	resp, _, err := dd.client.ListLogs(dd.ctx, body)
	if err != nil {
		return nil, fmt.Errorf("error querying Datadog logs: %w", err)
	}
	logs := resp.GetData()
	result := make([]map[string]interface{}, 0, len(logs))
	for _, logEntry := range logs {
		// Marshal the log entry to JSON and then unmarshal to a map for easier access
		logData, err := json.Marshal(logEntry)
		if err != nil {
			log.Errorf("Failed to marshal log entry: %v", err)

			continue
		}

		// log.Infof("log entry: %s", string(logData))

		var logMap map[string]interface{}
		if err := json.Unmarshal(logData, &logMap); err != nil {
			log.Errorf("Failed to unmarshal log entry: %v", err)

			continue
		}

		// Extract messageBody and moe_account_id from the unmarshaled data
		var messageBody interface{}
		var moeAccountID interface{}
		var receiverName interface{}

		// First get the outer attributes
		if outerAttributes, ok := logMap["attributes"].(map[string]interface{}); ok {
			// Then get the inner attributes
			if innerAttributes, ok := outerAttributes["attributes"].(map[string]interface{}); ok {
				// Extract moe_account_id directly
				if accountID, ok := innerAttributes["moe_account_id"]; ok {
					moeAccountID = accountID
				}

				// Extract receiver name from res_body_details
				if resBody, ok := innerAttributes["res_body_details"]; ok {
					if resBodyMap, ok := resBody.(map[string]interface{}); ok {
						if data, ok := resBodyMap["data"].(map[string]interface{}); ok {
							if messageReceiverDTO, ok := data["messageReceiverDTO"].(map[string]interface{}); ok {
								if name, ok := messageReceiverDTO["name"]; ok {
									receiverName = name
								}
							}
						}
					}
				}
				// Extract messageBody from req_body_details (already parsed)
				if reqBodyDetails, ok := innerAttributes["req_body_details"]; ok {
					if reqBodyMap, ok := reqBodyDetails.(map[string]interface{}); ok {
						if mb, ok := reqBodyMap["messageBody"]; ok {
							messageBody = mb
						}
					}
				} else if message, ok := innerAttributes["message"]; ok {
					messageBody = message
				}
			}
		}

		result = append(result, map[string]interface{}{
			"messageBody":    messageBody,
			"moe_account_id": moeAccountID,
			"receiverName":   receiverName,
		})
	}

	return result, nil
}

// PullLogsAndAudit pulls logs from Datadog and audits them using OpenAI
func (dd *DDClient) PullLogsAndAudit(ctx context.Context, openaiClient *OpenAIClient) (string, error) {
	// Pull logs from Datadog
	logs, err := dd.MessageLogs()
	if err != nil {
		return "", fmt.Errorf("failed to pull logs from Datadog: %w", err)
	}

	// If no logs found, return empty result
	if len(logs) == 0 {
		return "", nil
	}

	// Audit logs using OpenAI
	result, err := openaiClient.AuditLogs(ctx, logs)
	if err != nil {
		return "", fmt.Errorf("failed to audit logs with OpenAI: %w", err)
	}

	return result, nil
}
