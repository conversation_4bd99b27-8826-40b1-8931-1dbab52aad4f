load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "datadog",
    srcs = [
        "datadog.go",
        "log_audit.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/datadog",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/configinit",
        "//backend/app/tools/utils",
        "//backend/common/rpc/framework/log",
        "@com_github_datadog_datadog_api_client_go_v2//api/datadog",
        "@com_github_datadog_datadog_api_client_go_v2//api/datadogV2",
    ],
)

go_test(
    name = "datadog_test",
    srcs = [
        "datadog_test.go",
        "log_audit_recheck_test.go",
        "log_audit_test.go",
    ],
    embed = [":datadog"],
    deps = [
        "//backend/app/tools/configinit",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@in_gopkg_yaml_v2//:yaml_v2",
        "@tools_gotest_v3//assert",
    ],
)
