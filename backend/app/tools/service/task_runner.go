package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

type TaskRunnerService struct {
	toolspb.UnimplementedTaskRunnerServer
	messageAuditService *MessageAuditService
}

func NewTaskRunnerService(config *configinit.MessageConfig) (*TaskRunnerService, error) {
	// Create message audit service
	messageAuditService, err := NewMessageAuditService(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create message audit service: %w", err)
	}

	return &TaskRunnerService{
		messageAuditService: messageAuditService,
	}, nil
}

// RunTask executes a specific task by name
func (s *TaskRunnerService) RunTask(
	ctx context.Context, req *toolspb.RunTaskRequest) (*toolspb.DefaultResponse, error) {
	log.Infof("RunTask called with task: %s, task_type: %s, params: %v", req.Task, req.TaskType, req.Params)

	switch req.Task {
	case "message_audit":
		return s.runMessageAudit(ctx)
	default:
		return &toolspb.DefaultResponse{
			Message:   fmt.Sprintf("Unknown task: %s", req.Task),
			ErrorCode: 1,
		}, nil
	}
}

// runMessageAudit executes the message audit task
func (s *TaskRunnerService) runMessageAudit(ctx context.Context) (*toolspb.DefaultResponse, error) {
	log.InfoContextf(ctx, "Starting message audit task")

	// Execute the audit logic (moved from StartPeriodicAudit)
	if err := s.messageAuditService.PullLogsAuditAndSend(ctx); err != nil {
		log.ErrorContextf(ctx, "Error in message audit: %v", err)

		return &toolspb.DefaultResponse{
			Message:   fmt.Sprintf("Message audit failed: %v", err),
			ErrorCode: 1,
		}, nil
	}

	log.InfoContextf(ctx, "Message audit task completed successfully")

	return &toolspb.DefaultResponse{
		Message:   "Message audit completed successfully",
		ErrorCode: 0,
	}, nil
}
