package deployplatform

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/tools/logic/deployplatform/state"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

type CanaryHandler struct {
	repo deployplatform.Deployer
}

func NewCanaryHandler(repo deployplatform.Deployer) *CanaryHandler {
	return &CanaryHandler{
		repo: repo,
	}
}

// HandleCanaryEventWebhookEvent handles canary event webhook event
func (c *CanaryH<PERSON>ler) HandleCanaryEventWebhookEvent(ctx context.Context,
	req *toolspb.HandleCanaryEventWebhookEventRequest,
) (
	*toolspb.HandleCanaryEventWebhookEventResponse, error,
) {
	log.Debugf("HandleCanaryEventWebhookEvent: %+v", req)

	taskName := req.Checksum
	taskTitle := fmt.Sprintf("%s | %s", req.Namespace, req.Name)
	phaseName := fmt.Sprintf("%s-%s", req.Namespace, req.Name)
	messageBytes, err := protojson.Marshal(req)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to marshal proto message: %v", err)
	}
	message := string(messageBytes)

	deployTask, err := c.getOrCreateDeployTask(ctx, taskName, taskTitle, phaseName, message)
	if err != nil {
		return nil, err
	}

	if deployTask.State != deployplatform.Running &&
		deployTask.State != deployplatform.Waiting &&
		deployTask.State != deployplatform.Init {
		log.Debugf("deployTask is not running, taskID: %d", deployTask.ID)

		return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
	}

	phase, err := c.getOrCreateCanaryDeployPhase(ctx, deployTask, phaseName)
	if err != nil {
		return nil, err
	}

	var canaryParams state.CanaryParameters
	if err := json.Unmarshal(phase.Parameters, &canaryParams); err != nil {
		log.Errorf("failed to unmarshal Canary parameters, taskID: %d", deployTask.ID)

		return nil, err
	}

	if canaryParams.IsCompleted() {
		return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
	}

	go func() {
		_ = c.repo.CreateDeployLog(context.Background(),
			deployTask.ID, "flagger", phase.State, deployplatform.PhaseCanary, string(message))
	}()

	log.Debugf("phase: %s, state: %s", phase.Type, phase.State)

	canaryParams.Phase = req.Phase
	canaryParams.Metadata = req.Metadata
	if req.CanaryWeight != nil {
		canaryParams.CanaryWeight = *req.CanaryWeight
	}
	if req.FailedChecks != nil {
		canaryParams.FailedChecks = *req.FailedChecks
	}
	if req.Iterations != nil {
		canaryParams.Iterations = *req.Iterations
	}
	if req.Remaining != nil {
		canaryParams.RemainingTime = time.Duration(*req.Remaining)
	}

	// 将 req 直接序列化为 JSON 存储到 Parameters
	paramsBytes, err := json.Marshal(canaryParams)
	if err != nil {
		log.Errorf("failed to marshal proto to JSON: %v", err)

		return nil, status.Errorf(codes.Internal, "failed to process parameters")
	}
	phase.Parameters = paramsBytes

	messageBytes, err = protojson.Marshal(req)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to marshal proto message: %v", err)
	}
	message = string(messageBytes)

	if err = c.repo.UpdatePhase(ctx, phase, message); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update phase: %v", err)
	}

	return &toolspb.HandleCanaryEventWebhookEventResponse{}, nil
}

// HandleCanaryWebhookEvent handles canary webhook event
func (c *CanaryHandler) HandleCanaryWebhookEvent(
	ctx context.Context, req *toolspb.HandleCanaryWebhookEventRequest,
) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	taskName := req.Checksum
	taskTitle := fmt.Sprintf("%s | %s", req.Namespace, req.Name)
	phaseName := fmt.Sprintf("%s-%s", req.Namespace, req.Name)
	message, err := marshalProtoJSON(req)
	if err != nil {
		return nil, err
	}

	deployTask, err := c.getOrCreateDeployTask(ctx, taskName, taskTitle, phaseName, message)
	if err != nil {
		return nil, err
	}

	phase, err := c.getOrCreateCanaryDeployPhase(ctx, deployTask, phaseName)
	if err != nil {
		return nil, err
	}

	log.Debugf("phase: %s, state: %s", phase.Type, phase.State)
	resp, err := c.handleCanaryStatus(req, deployTask, phase)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// HandleCanaryStatus handles the status update for canary deployment.
func (c *CanaryHandler) handleCanaryStatus(
	req *toolspb.HandleCanaryWebhookEventRequest, task *entity.DeployTask, phase *entity.DeployPhase,
) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	var params state.CanaryParameters
	if err := json.Unmarshal(phase.Parameters, &params); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(task.Parameters, &params); err != nil {
		return nil, err
	}

	resp, err := c.handleHookType(req.Type, params)
	if err != nil {
		log.Errorf("failed to handle hook type: %v", err)

		return nil, status.Errorf(codes.InvalidArgument, "failed to handle hook type: %s", err)
	}

	return resp, nil
}

// handleHookType handles different types of canary hooks.
func (c *CanaryHandler) handleHookType(
	hookType string, parameters state.CanaryParameters,
) (
	*toolspb.HandleCanaryWebhookEventResponse, error,
) {
	if handler, ok := state.CanaryHooks[hookType]; ok {
		if handler(parameters) {
			return &toolspb.HandleCanaryWebhookEventResponse{
				Rollback: parameters.Rollback,
				Skip:     parameters.Skip,
				ManualTrafficControl: &toolspb.HandleCanaryWebhookEventResponse_ManualTrafficControl{
					Weight:    parameters.Weight,
					Paused:    parameters.Paused,
					Timestamp: parameters.ManualTimestamp,
				},
			}, nil
		}
	} else {
		return nil, status.Errorf(codes.InvalidArgument, "unknown hook type: %s", hookType)
	}

	return nil, status.Errorf(codes.InvalidArgument, "hook not enabled for type: %s", hookType)
}

// getOrCreateDeployTask retrieves the deploy task by task name.
func (c *CanaryHandler) getOrCreateDeployTask(
	ctx context.Context, name, title, phaseName, message string,
) (*entity.DeployTask, error) {
	deployTask, err := c.repo.GetOrCreateDeployTask(ctx, name, title, title, "flagger",
		deployplatform.PhaseCanary, phaseName, message)
	if err != nil {
		log.Errorf("failed to create DeployTask: %v", err)

		return nil, status.Errorf(codes.Internal, "failed to create DeployTask")
	}

	return deployTask, nil
}

// getOrCreateCanaryDeployPhase
func (c *CanaryHandler) getOrCreateCanaryDeployPhase(
	ctx context.Context, task *entity.DeployTask, phaseName string,
) (
	*entity.DeployPhase, error,
) {
	phase, err := c.repo.GetDeployPhase(ctx, task.ID, deployplatform.PhaseCanary)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Errorf("failed to get current phase: %v", err)

			return nil, status.Errorf(codes.Internal, "failed to get phase")
		}

		err = c.repo.CreatePhaseAndUpdateTask(
			ctx, task, deployplatform.PhaseCanary, phaseName, deployplatform.Init, "{}")
		if err != nil {
			log.Errorf("failed to create phase and update task, taskID: %d", task.ID)

			return nil, err
		}

		return c.repo.GetDeployPhase(ctx, task.ID, deployplatform.PhaseCanary)
	}

	return phase, nil
}

// MarshalJSONB converts CanaryParameters to JSONB.
func MarshalJSONB(target interface{}) (entity.JSONB, error) {
	data, err := json.Marshal(target)
	if err != nil {
		return nil, err
	}

	return entity.JSONB(data), nil
}

// UnmarshalJSONB converts JSONB to CanaryParameters.
func UnmarshalJSONB(data entity.JSONB, target interface{}) error {
	return json.Unmarshal([]byte(data), target)
}
