package state

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// CanaryParameters 是Canary webhook的参数。
type CanaryParameters struct {
	TaskParameters
	Name                   string            `json:"name"`
	Namespace              string            `json:"namespace"`
	Checksum               string            `json:"checksum"`
	BuildID                string            `json:"build_id"`
	Phase                  string            `json:"phase"`
	FailedChecks           int32             `json:"failed_checks"`
	CanaryWeight           int32             `json:"canary_weight"`
	Iterations             int32             `json:"iterations"`
	RemainingTime          time.Duration     `json:"remaining_time"`
	Metadata               map[string]string `json:"metadata"`
	Rollout                bool              `json:"rollout"`
	PreRollOut             bool              `json:"pre-rollout"`
	PostRollOut            bool              `json:"post-rollout"`
	ConfirmRollOut         bool              `json:"confirm-rollout"`
	ConfirmPromotion       bool              `json:"confirm-promotion"`
	ConfirmTrafficIncrease bool              `json:"confirm-traffic-increase"`
}

// IsCompleted 判断CanaryPhase是否已经完成。
func (p *CanaryParameters) IsCompleted() bool {
	return p.Phase == CanaryPhaseFailed || p.Phase == CanaryPhaseSucceeded || p.Phase == CanaryPhaseTerminated
}

// NewCanaryPhase 创建一个新的CanaryPhase实例，并初始化其状态。
// dbManager 是用于与数据库交互的管理器。
// task 是与Canary部署相关的任务。
// 返回值 ret 是新创建的CanaryPhase实例，err 是可能发生的错误。
func NewCanaryPhase(dbManager deployplatform.Deployer, task *entity.DeployTask) (
	canaryPhase *CanaryPhase, err error,
) {
	if dbManager == nil || task == nil {
		return nil, errors.New("dbManager or task is nil")
	}
	canaryPhase = &CanaryPhase{
		Phase: Phase{
			Repo: dbManager,
			Task: task,
		},
	}

	return
}

// CanaryPhase 代表Canary阶段的状态管理。
type CanaryPhase struct {
	Phase
}

// InitState 初始化Canary阶段的状态。
// 通过从数据库中获取阶段信息并更新当前任务的状态。
func (p *CanaryPhase) InitState() (err error) {
	err = p.SyncStateFromDB()
	if err != nil {
		log.Errorf("failed to sync state from DB, taskID: %d", p.Task.ID)
	}

	if !lo.ContainsBy(p.Phases, func(phase *entity.DeployPhase) bool {
		return phase.Type == deployplatform.PhaseCanary
	}) {
		err = p.Repo.CreatePhaseAndUpdateTask(
			context.Background(), p.Task, deployplatform.PhaseCanary, p.Task.Name, deployplatform.Init, "{}")
		if err != nil {
			log.Errorf("failed to create phase and update task, taskID: %d", p.Task.ID)

			return err
		}
	}

	return
}

// Change 根据当前阶段状态改变任务状态或阶段。
// env 是包含环境变量的映射，用于可能的环境变量解析。
// 返回值 ret 是可能的下一个任务，err 是可能发生的错误。
func (p *CanaryPhase) Change(_ map[string]string) (ret Task, err error) {
	// 从数据库中同步当前任务的状态
	if err := p.SyncStateFromDB(); err != nil {
		return nil, err
	}
	// 如果任务已经1小时没有更新，则结束任务。
	if !p.Task.IsEnd() && p.Task.UpdatedAt.Add(time.Hour).Before(time.Now()) {
		p.End(true)

		return p, nil
	}

	currentPhase, err := p.GetCurrentPhase()
	log.Debugf("current phase state: %s, taskID: %d", currentPhase.State, p.Task.ID)
	if err != nil {
		return nil, err
	}

	if currentPhase.IsEnd() {
		return p, nil
	}

	canaryParams, err := p.GetParams()
	if err != nil {
		return nil, err
	}

	log.Debugf("canary phase params: %s, taskID: %d", currentPhase.State, p.Task.ID)

	switch currentPhase.State {
	case deployplatform.Init:
		return p.handleInitState(canaryParams)
	case deployplatform.Running, deployplatform.Waiting:
		return p.handleRunningState(*canaryParams, currentPhase)
	case deployplatform.Rollback:
		p.End(true)
	case deployplatform.Skipped, deployplatform.Succeeded:
		return p.handleEndState()
	default:
		p.End(true)
	}

	return p, nil
}

// handleInitState 处理 Init 状态。
func (p *CanaryPhase) handleInitState(canaryParams *CanaryParameters) (ret Task, err error) {
	if !canaryParams.Skip {
		log.Debugf("canary Init->Running, taskID: %d", p.Task.ID)
		if err = p.SetState(deployplatform.Running); err != nil {
			return nil, err
		}
	} else {
		log.Debugf("canary Init->Skip, taskID: %d", p.Task.ID)
		if err = p.SetState(deployplatform.Skipped); err != nil {
			return nil, err
		}
	}

	return p, nil
}

// handleRunningState 处理 Running 状态。
func (p *CanaryPhase) handleRunningState(
	canaryParams CanaryParameters, currentPhase *entity.DeployPhase,
) (
	ret Task, err error,
) {
	nextState := getNewStateFromCanaryParameters(canaryParams)
	if nextState == currentPhase.State {
		return p, nil
	}
	log.Debugf("canary Running->%s, taskID: %d", nextState, p.Task.ID)
	if err := p.SetState(nextState); err != nil {
		return nil, err
	}

	return p, nil
}

// handleEndState 处理 Skipped 和 Succeeded 状态。
func (p *CanaryPhase) handleEndState() (ret Task, err error) {
	p.End(false)
	log.Debugf("canary ->FullDeployment Init, taskID: %d", p.Task.ID)
	phase, err := NewFullDeploymentPhase(p.Repo, p.Task)
	if err != nil {
		return nil, err
	}
	err = phase.InitState()
	if err != nil {
		return nil, err
	}

	return phase, nil
}

// End Phase, Maybe End Task
// endTask 指示是否结束整个任务。
func (p *CanaryPhase) End(endTask bool) {

	for _, phase := range p.Phases {
		if phase.Type == deployplatform.PhaseCanary {
			p.endPhaseOrTask(phase, endTask)

			break
		}
	}
}

// GetParams 获取CanaryParameters
func (p *CanaryPhase) GetParams() (*CanaryParameters, error) {
	currentPhase, err := p.GetCurrentPhase()
	if err != nil {
		return nil, err
	}
	var canaryParams CanaryParameters
	if err := json.Unmarshal(currentPhase.Parameters, &canaryParams); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(p.Task.Parameters, &canaryParams); err != nil {
		return nil, err
	}

	return &canaryParams, nil
}

// CheckStatus 检查CI状态
func (p *CanaryPhase) CheckStatus() (err error) {
	return
}

// getNewStateFromCanaryParameters 根据Canary参数确定新的状态。
// params 是Canary webhook的参数。
// 返回新的状态字符串。
func getNewStateFromCanaryParameters(params CanaryParameters) string {
	if params.Rollback {
		return deployplatform.Rollback
	} else if params.Skip {
		return deployplatform.Skipped
	}
	switch params.Phase {
	case CanaryPhaseSucceeded:
		return deployplatform.Succeeded
	case CanaryPhaseFailed:
		return deployplatform.Failed
	case CanaryPhaseTerminating, CanaryPhaseTerminated:
		return deployplatform.Rollback
	case CanaryPhaseWaiting, CanaryPhaseWaitingPromotion:
		return deployplatform.Waiting
	case CanaryPhaseProgressing, CanaryPhasePromoting, CanaryPhaseFinalising:
		return deployplatform.Running
	default:
		return deployplatform.Running
	}
}

var CanaryHooks = map[string]func(CanaryParameters) bool{
	RolloutHook:                func(p CanaryParameters) bool { return p.Rollout },
	PreRolloutHook:             func(p CanaryParameters) bool { return p.PreRollOut },
	PostRolloutHook:            func(p CanaryParameters) bool { return p.PostRollOut },
	ConfirmRolloutHook:         func(p CanaryParameters) bool { return p.ConfirmRollOut },
	ConfirmPromotionHook:       func(p CanaryParameters) bool { return p.ConfirmPromotion },
	RollbackHook:               func(p CanaryParameters) bool { return p.Rollback },
	SkipHook:                   func(p CanaryParameters) bool { return p.Skip },
	ConfirmTrafficIncreaseHook: func(p CanaryParameters) bool { return p.ConfirmTrafficIncrease },
}

// 常量定义
const (
	// CanaryPhaseWaiting means the canary rollout is paused (waiting for confirmation to proceed)
	CanaryPhaseWaiting string = "Waiting"
	// CanaryPhaseProgressing means the canary analysis is underway
	CanaryPhaseProgressing string = "Progressing"
	// CanaryPhaseWaitingPromotion means the canary promotion is paused (waiting for confirmation to proceed)
	CanaryPhaseWaitingPromotion string = "WaitingPromotion"
	// CanaryPhasePromoting means the canary analysis is finished and the primary spec has been updated
	CanaryPhasePromoting string = "Promoting"
	// CanaryPhaseFinalising means the canary promotion is finished and traffic has been routed back to primary
	CanaryPhaseFinalising string = "Finalising"
	// CanaryPhaseSucceeded means the canary analysis has been successful
	// and the canary deployment has been promoted
	CanaryPhaseSucceeded string = "Succeeded"
	// CanaryPhaseFailed means the canary analysis failed
	// and the canary deployment has been scaled to zero
	CanaryPhaseFailed string = "Failed"
	// CanaryPhaseTerminating means the canary has been marked
	// for deletion and in the finalizing state
	CanaryPhaseTerminating string = "Terminating"
	// CanaryPhaseTerminated means the canary has been finalized
	// and successfully deleted
	CanaryPhaseTerminated string = "Terminated"

	// RolloutHook execute webhook during the canary analysis
	RolloutHook string = "rollout"
	// PreRolloutHook execute webhook before routing traffic to canary
	PreRolloutHook string = "pre-rollout"
	// PostRolloutHook execute webhook after the canary analysis
	PostRolloutHook string = "post-rollout"
	// ConfirmRolloutHook halt canary analysis until webhook returns HTTP 200
	ConfirmRolloutHook string = "confirm-rollout"
	// ConfirmPromotionHook halt canary promotion until webhook returns HTTP 200
	ConfirmPromotionHook string = "confirm-promotion"
	// EventHook dispatches Flagger events to the specified endpoint
	EventHook string = "event"
	// RollbackHook rollback canary analysis if webhook returns HTTP 200
	RollbackHook string = "rollback"
	// SkipHook skips canary analysis if webhook returns HTTP 200
	SkipHook string = "skip"
	// ConfirmTrafficIncreaseHook increases traffic weight if webhook returns HTTP 200
	ConfirmTrafficIncreaseHook = "confirm-traffic-increase"
)
