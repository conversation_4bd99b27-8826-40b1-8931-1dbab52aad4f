package additional

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestAdditionalServiceToModels_Success(t *testing.T) {
	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1, 2},
		AdditionalServiceIds:  []int64{10, 20},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Len(t, models, 4)

	// 验证 careType 规则（前两个）
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, models[0].OrganizationType)
	assert.Equal(t, int64(123), models[0].OrganizationID)
	assert.Equal(t, int64(1), models[0].ServiceID)
	assert.Equal(t, int64(1), models[0].AdditionalCareTypeID)
	assert.Equal(t, int64(0), models[0].AdditionalServiceID)

	assert.Equal(t, int64(2), models[1].AdditionalCareTypeID)
	assert.Equal(t, int64(0), models[1].AdditionalServiceID)

	// 验证 service 规则（后两个）
	assert.Equal(t, int64(10), models[2].AdditionalServiceID)
	assert.Equal(t, int64(0), models[2].AdditionalCareTypeID)

	assert.Equal(t, int64(20), models[3].AdditionalServiceID)
	assert.Equal(t, int64(0), models[3].AdditionalCareTypeID)
}

func TestAdditionalServiceToModels_WithNilAdditionalService(t *testing.T) {
	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.Len(t, models, 0)
}

func TestAdditionalServiceToModels_WithEmptyLists(t *testing.T) {
	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{},
		AdditionalServiceIds:  []int64{},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Len(t, models, 0)
}

func TestAdditionalServiceToModels_WithOnlyCareTypeIds(t *testing.T) {
	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1, 2, 3},
		AdditionalServiceIds:  []int64{},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Len(t, models, 3)

	for i, careTypeID := range []int64{1, 2, 3} {
		assert.Equal(t, careTypeID, models[i].AdditionalCareTypeID)
		assert.Equal(t, int64(0), models[i].AdditionalServiceID)
	}
}

func TestAdditionalServiceToModels_WithOnlyServiceIds(t *testing.T) {
	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{},
		AdditionalServiceIds:  []int64{10, 20, 30},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Len(t, models, 3)

	for i, serviceID := range []int64{10, 20, 30} {
		assert.Equal(t, serviceID, models[i].AdditionalServiceID)
		assert.Equal(t, int64(0), models[i].AdditionalCareTypeID)
	}
}

func TestModelsToAdditionalService_Success(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 2,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  20,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.AdditionalCareTypeIds, 2)
	assert.Contains(t, result.AdditionalCareTypeIds, int64(1))
	assert.Contains(t, result.AdditionalCareTypeIds, int64(2))
	assert.Len(t, result.AdditionalServiceIds, 2)
	assert.Contains(t, result.AdditionalServiceIds, int64(10))
	assert.Contains(t, result.AdditionalServiceIds, int64(20))
}

func TestModelsToAdditionalService_WithEmptyModels(t *testing.T) {
	// Execute
	result := ModelsToProto([]*model.AdditionalService{})

	// Assertions
	assert.Nil(t, result)
}

func TestModelsToAdditionalService_WithOnlyCareTypeIDs(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 2,
			AdditionalServiceID:  0,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.AdditionalCareTypeIds, 2)
	assert.Empty(t, result.AdditionalServiceIds)
}

func TestModelsToAdditionalService_WithOnlyServiceIDs(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  20,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Empty(t, result.AdditionalCareTypeIds)
	assert.Len(t, result.AdditionalServiceIds, 2)
	assert.Contains(t, result.AdditionalServiceIds, int64(10))
	assert.Contains(t, result.AdditionalServiceIds, int64(20))
}

func TestModelsToAdditionalService_WithZeroValues(t *testing.T) {
	// Test data - 包含零值的模型
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  0,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.Nil(t, result) // 所有值都是零值，返回 nil
}

func TestModelsToAdditionalService_WithMixOfZeroAndNonZero(t *testing.T) {
	// Test data - 混合零值和非零值
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  0, // 这个应该被过滤
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.AdditionalCareTypeIds, 1)
	assert.Contains(t, result.AdditionalCareTypeIds, int64(1))
	assert.Len(t, result.AdditionalServiceIds, 1)
	assert.Contains(t, result.AdditionalServiceIds, int64(10))
}

func TestModelsToAdditionalService_WithDuplicates(t *testing.T) {
	// Test data - 包含重复值
	models := []*model.AdditionalService{
		{
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
		{
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
	}

	// Execute
	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.AdditionalCareTypeIds, 2) // 应该有 2 个 1
	assert.Len(t, result.AdditionalServiceIds, 2)  // 应该有 2 个 10
}

func TestAdditionalServiceToModels_And_ModelsToAdditionalService_RoundTrip(t *testing.T) {
	// Original data
	original := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1, 2, 3},
		AdditionalServiceIds:  []int64{10, 20},
	}

	// Execute round trip
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		original,
	)

	result := ModelsToProto(models)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, len(original.AdditionalCareTypeIds), len(result.AdditionalCareTypeIds))
	for _, careTypeID := range original.AdditionalCareTypeIds {
		assert.Contains(t, result.AdditionalCareTypeIds, careTypeID)
	}
	assert.Equal(t, len(original.AdditionalServiceIds), len(result.AdditionalServiceIds))
	for _, serviceID := range original.AdditionalServiceIds {
		assert.Contains(t, result.AdditionalServiceIds, serviceID)
	}
}
