package additional

import (
	"context"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

type Logic struct {
	repo        additional.Repository
	serviceRepo service.Repository
}

func NewLogic() *Logic {
	return &Logic{
		repo:        additional.NewRepository(),
		serviceRepo: service.NewRepository(),
	}
}

func NewLogicWithRepository(
	repo additional.Repository, serviceRepo service.Repository) *Logic {
	return &Logic{
		repo:        repo,
		serviceRepo: serviceRepo,
	}
}

// Create 创建 additional service 配置
func (l *Logic) Create(
	ctx context.Context, query *query.Query,
	orgType organizationpb.OrganizationType, orgID, serviceID int64,
	additionalService *offeringpb.AdditionalService) error {
	if additionalService == nil {
		return nil
	}

	models := ProtoToModels(orgType, orgID, serviceID, additionalService)
	if len(models) == 0 {
		return nil
	}

	return l.repo.WithTX(query).BatchCreate(ctx, models)
}

// Upsert 批量写入 additional service 配置
func (l *Logic) Upsert(
	ctx context.Context, query *query.Query,
	orgType organizationpb.OrganizationType, orgID, serviceID int64,
	additionalService *offeringpb.AdditionalService) error {
	if additionalService == nil {
		return nil
	}

	err := l.DeleteByServiceID(ctx, query, serviceID)
	if err != nil {
		return err
	}

	return l.Create(ctx, query, orgType, orgID, serviceID, additionalService)
}

// GetByServiceID 获取服务的 additional service 配置
func (l *Logic) GetByServiceID(ctx context.Context, serviceID int64) (*offeringpb.AdditionalService, error) {
	serviceIDToAdditionalService, err := l.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	return serviceIDToAdditionalService[serviceID], nil
}

// ListByServiceIDs 批量获取多个服务的 additional service 配置
// 返回值：serviceID -> AdditionalService
func (l *Logic) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) (map[int64]*offeringpb.AdditionalService, error) {
	if len(serviceIDs) == 0 {
		return make(map[int64]*offeringpb.AdditionalService), nil
	}

	models, err := l.repo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 按 serviceID 分组
	modelsByServiceID := lo.GroupBy(models, func(model *model.AdditionalService) int64 {
		return model.ServiceID
	})

	// 转换为 proto 格式
	result := make(map[int64]*offeringpb.AdditionalService, len(serviceIDs))
	for _, serviceID := range serviceIDs {
		if models, exists := modelsByServiceID[serviceID]; exists {
			result[serviceID] = ModelsToProto(models)
		} else {
			result[serviceID] = nil
		}
	}

	return result, nil
}

// DeleteByServiceID 删除服务的所有 additional service 配置
func (l *Logic) DeleteByServiceID(ctx context.Context, query *query.Query, serviceID int64) error {
	return l.repo.WithTX(query).DeleteByServiceID(ctx, serviceID)
}

// ListAdditionalServiceIDs 批量获取多个 service 共同的 additional service IDs
func (l *Logic) ListAdditionalServiceIDs(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64, serviceIDs []int64) ([]int64, error) {
	if len(serviceIDs) == 0 {
		return []int64{}, nil
	}

	// 1. 获取所有 service 的 additional service 配置
	models, err := l.repo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	if len(models) == 0 {
		return []int64{}, nil
	}

	// 2. 按 serviceID 分组
	modelsByServiceID := lo.GroupBy(models, func(model *model.AdditionalService) int64 {
		return model.ServiceID
	})

	// 3. 提取 careTypeID 列表和 serviceID 列表
	extractIDs := func(serviceID int64) ([]int64, []int64) {
		serviceModels, exists := modelsByServiceID[serviceID]
		if !exists {
			return nil, nil
		}

		careTypeIDs := lo.FilterMap(serviceModels, func(m *model.AdditionalService, _ int) (int64, bool) {
			return m.AdditionalCareTypeID, m.AdditionalCareTypeID > 0
		})

		serviceIDs := lo.FilterMap(serviceModels, func(m *model.AdditionalService, _ int) (int64, bool) {
			return m.AdditionalServiceID, m.AdditionalServiceID > 0
		})

		return careTypeIDs, serviceIDs
	}

	// 为每个 service 提取 IDs
	allCareTypeIDLists := lo.Map(serviceIDs, func(serviceID int64, _ int) []int64 {
		careTypeIDs, _ := extractIDs(serviceID)

		return careTypeIDs
	})

	allServiceIDLists := lo.Map(serviceIDs, func(serviceID int64, _ int) []int64 {
		_, serviceIDs := extractIDs(serviceID)

		return serviceIDs
	})

	// 4. 取交集
	commonCareTypeIDs := lo.Reduce(allCareTypeIDLists[1:], func(acc []int64, list []int64, _ int) []int64 {
		return lo.Intersect(acc, list)
	}, allCareTypeIDLists[0])

	commonServiceIDs := lo.Reduce(allServiceIDLists[1:], func(acc []int64, list []int64, _ int) []int64 {
		return lo.Intersect(acc, list)
	}, allServiceIDLists[0])

	// 5. 根据 careTypeID 查询所有对应的 service
	allServiceIDsFromCareType := lo.Flatten(lo.Map(commonCareTypeIDs, func(careTypeID int64, _ int) []int64 {
		services, _, err := l.serviceRepo.List(ctx, &service.ListServiceFilter{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			CareTypeIDs:      []int64{careTypeID},
			Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
		}, nil)
		if err != nil {
			return []int64{}
		}

		return lo.Map(services, func(s *model.Service, _ int) int64 {
			return s.ID
		})
	}))

	// 6. 批量获取 service 信息，过滤出 type=SERVICE 的（只处理直接配置的 service IDs）
	var filteredServiceIDs []int64
	if len(commonServiceIDs) > 0 {
		services, err := l.serviceRepo.BatchGet(ctx, commonServiceIDs)
		if err != nil {
			return nil, err
		}
		filteredServiceIDs = lo.Map(lo.Filter(services, func(s *model.Service, _ int) bool {
			return s.Type == offeringpb.Service_SERVICE
		}), func(s *model.Service, _ int) int64 {
			return s.ID
		})
	}

	// 7. 合并 careTypeID 对应的 service IDs 和直接配置的 service IDs
	// careTypeID 查询出来的已经是 SERVICE 类型了，不需要再过滤
	resultServiceIDs := lo.Union(filteredServiceIDs, allServiceIDsFromCareType)

	return resultServiceIDs, nil
}
