package additional

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction/mocks"
	mockadditional "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	mockservice "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl        *gomock.Controller
	mockQuery   *mocks.MockQuery
	mockRepo    *mockadditional.MockRepository
	mockService *mockservice.MockRepository
	logic       *Logic
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)
	mockQuery := mocks.NewMockQuery()
	mockRepo := mockadditional.NewMockRepository(ctrl)
	mockService := mockservice.NewMockRepository(ctrl)

	logic := NewLogicWithRepository(mockRepo, mockService)

	return &testHelper{
		ctrl:        ctrl,
		mockQuery:   mockQuery,
		mockRepo:    mockRepo,
		mockService: mockService,
		logic:       logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

func TestLogic_Create_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1, 2},
		AdditionalServiceIds:  []int64{10, 20},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_WithNilAdditionalService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_WithEmptyConfigurations(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data with empty configurations
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{},
		AdditionalServiceIds:  []int64{},
	}

	// Execute (should not call BatchCreate because len(models) == 0)
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1},
	}

	// Mock expectations - 模拟 repository 错误
	expectedErr := errors.New("repository error")
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(expectedErr)

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_Upsert_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1, 2},
		AdditionalServiceIds:  []int64{10},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo).Times(2)
	helper.mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Upsert_DeleteError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	additionalService := &offeringpb.AdditionalService{
		AdditionalCareTypeIds: []int64{1},
	}

	// Mock expectations - 模拟 delete 错误
	expectedErr := errors.New("delete error")
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(expectedErr)

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		additionalService,
	)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_Upsert_WithNilAdditionalService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_GetByServiceID_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(1)
	models := []*model.AdditionalService{
		{
			ID:                   1,
			OrganizationType:     organizationpb.OrganizationType_COMPANY,
			OrganizationID:       123,
			ServiceID:            serviceID,
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			ID:                   2,
			OrganizationType:     organizationpb.OrganizationType_COMPANY,
			OrganizationID:       123,
			ServiceID:            serviceID,
			AdditionalCareTypeID: 2,
			AdditionalServiceID:  0,
		},
		{
			ID:                   3,
			OrganizationType:     organizationpb.OrganizationType_COMPANY,
			OrganizationID:       123,
			ServiceID:            serviceID,
			AdditionalCareTypeID: 0,
			AdditionalServiceID:  10,
		},
	}

	// Mock expectations - GetByServiceID 调用 ListByServiceIDs，需要 mock ListByServiceIDs
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).Return(models, nil)

	// Execute
	result, err := helper.logic.GetByServiceID(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.AdditionalCareTypeIds, 2)
	assert.Contains(t, result.AdditionalCareTypeIds, int64(1))
	assert.Contains(t, result.AdditionalCareTypeIds, int64(2))
	assert.Len(t, result.AdditionalServiceIds, 1)
	assert.Contains(t, result.AdditionalServiceIds, int64(10))
}

func TestLogic_GetByServiceID_EmptyModels(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(1)

	// Mock expectations - GetByServiceID 调用 ListByServiceIDs，需要 mock ListByServiceIDs
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).Return([]*model.AdditionalService{}, nil)

	// Execute
	result, err := helper.logic.GetByServiceID(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.Nil(t, result) // 空模型返回 nil
}

func TestLogic_GetByServiceID_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(1)
	expectedErr := errors.New("repository error")

	// Mock expectations - GetByServiceID 调用 ListByServiceIDs，需要 mock ListByServiceIDs
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.GetByServiceID(context.Background(), serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListByServiceIDs_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceIDs := []int64{1, 2}
	models := []*model.AdditionalService{
		// Service 1 的配置
		{
			ID:                   1,
			ServiceID:            1,
			AdditionalCareTypeID: 1,
			AdditionalServiceID:  0,
		},
		{
			ID:                   2,
			ServiceID:            1,
			AdditionalServiceID:  10,
			AdditionalCareTypeID: 0,
		},
		// Service 2 的配置
		{
			ID:                   3,
			ServiceID:            2,
			AdditionalCareTypeID: 2,
			AdditionalServiceID:  0,
		},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(models, nil)

	// Execute
	result, err := helper.logic.ListByServiceIDs(context.Background(), serviceIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 2)

	// 验证 service 1
	assert.NotNil(t, result[1])
	assert.Len(t, result[1].AdditionalCareTypeIds, 1)
	assert.Contains(t, result[1].AdditionalCareTypeIds, int64(1))
	assert.Len(t, result[1].AdditionalServiceIds, 1)
	assert.Contains(t, result[1].AdditionalServiceIds, int64(10))

	// 验证 service 2
	assert.NotNil(t, result[2])
	assert.Len(t, result[2].AdditionalCareTypeIds, 1)
	assert.Contains(t, result[2].AdditionalCareTypeIds, int64(2))
	assert.Empty(t, result[2].AdditionalServiceIds)
}

func TestLogic_ListByServiceIDs_EmptyServiceIDs(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	result, err := helper.logic.ListByServiceIDs(context.Background(), []int64{})

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Empty(t, result)
}

func TestLogic_ListByServiceIDs_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceIDs := []int64{1, 2}
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.ListByServiceIDs(context.Background(), serviceIDs)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListByServiceIDs_WithNoConfig(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceIDs := []int64{1, 2}

	// Mock expectations - 返回空模型
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return([]*model.AdditionalService{}, nil)

	// Execute
	result, err := helper.logic.ListByServiceIDs(context.Background(), serviceIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 2)
	assert.Nil(t, result[1])
	assert.Nil(t, result[2])
}

func TestLogic_DeleteByServiceID_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(1)

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)

	// Execute
	err := helper.logic.DeleteByServiceID(context.Background(), nil, serviceID)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_DeleteByServiceID_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(1)
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(expectedErr)

	// Execute
	err := helper.logic.DeleteByServiceID(context.Background(), nil, serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_ListAdditionalServiceIDs_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceIDs := []int64{1, 2}
	models := []*model.AdditionalService{
		// Service 1 的配置
		{ServiceID: 1, AdditionalServiceID: 10, AdditionalCareTypeID: 0},
		{ServiceID: 1, AdditionalServiceID: 0, AdditionalCareTypeID: 1},
		// Service 2 的配置
		{ServiceID: 2, AdditionalServiceID: 10, AdditionalCareTypeID: 0},
		{ServiceID: 2, AdditionalServiceID: 0, AdditionalCareTypeID: 1},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(models, nil)

	// Mock service queries for care type
	helper.mockService.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Service{
		{ID: 100, Type: offeringpb.Service_SERVICE},
		{ID: 101, Type: offeringpb.Service_SERVICE},
	}, int64(2), nil)

	// Mock batch get for common service IDs
	helper.mockService.EXPECT().BatchGet(gomock.Any(), gomock.Any()).Return([]*model.Service{
		{ID: 10, Type: offeringpb.Service_SERVICE},
	}, nil)

	// Execute
	result, err := helper.logic.ListAdditionalServiceIDs(
		context.Background(),
		organizationpb.OrganizationType_COMPANY,
		123,
		serviceIDs,
	)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, result, int64(10))  // 共同的 service ID
	assert.Contains(t, result, int64(100)) // care type 下的 service
	assert.Contains(t, result, int64(101)) // care type 下的 service
}

func TestLogic_ListAdditionalServiceIDs_EmptyServiceIDs(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	result, err := helper.logic.ListAdditionalServiceIDs(
		context.Background(),
		organizationpb.OrganizationType_COMPANY,
		123,
		[]int64{},
	)

	// Assertions
	assert.NoError(t, err)
	assert.Empty(t, result)
}

func TestLogic_ListAdditionalServiceIDs_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	serviceIDs := []int64{1, 2}
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.ListAdditionalServiceIDs(
		context.Background(),
		organizationpb.OrganizationType_COMPANY,
		123,
		serviceIDs,
	)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}
