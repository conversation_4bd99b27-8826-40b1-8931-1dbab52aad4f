package additional

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ProtoToModels 将 AdditionalService proto 转换为数据库模型
func ProtoToModels(
	organizationType organizationpb.OrganizationType,
	organizationID, serviceID int64,
	additionalService *offeringpb.AdditionalService) []*model.AdditionalService {
	if additionalService == nil {
		return []*model.AdditionalService{}
	}

	var models []*model.AdditionalService

	// 转换 additional_care_type_ids
	for _, careTypeID := range additionalService.AdditionalCareTypeIds {
		models = append(models, &model.AdditionalService{
			OrganizationType:     organizationType,
			OrganizationID:       organizationID,
			ServiceID:            serviceID,
			AdditionalCareTypeID: careTypeID,
		})
	}

	// 转换 additional_service_ids
	for _, additionalServiceID := range additionalService.AdditionalServiceIds {
		models = append(models, &model.AdditionalService{
			OrganizationType:    organizationType,
			OrganizationID:      organizationID,
			ServiceID:           serviceID,
			AdditionalServiceID: additionalServiceID,
		})
	}

	return models
}

// ModelsToProto 将数据库模型转换为 AdditionalService proto
func ModelsToProto(models []*model.AdditionalService) *offeringpb.AdditionalService {
	if len(models) == 0 {
		return nil
	}

	var careTypeIDs []int64
	var serviceIDs []int64

	for _, model := range models {
		if model.AdditionalCareTypeID > 0 {
			careTypeIDs = append(careTypeIDs, model.AdditionalCareTypeID)
		}
		if model.AdditionalServiceID > 0 {
			serviceIDs = append(serviceIDs, model.AdditionalServiceID)
		}
	}

	// 如果没有配置，返回 nil
	if len(careTypeIDs) == 0 && len(serviceIDs) == 0 {
		return nil
	}

	return &offeringpb.AdditionalService{
		AdditionalCareTypeIds: careTypeIDs,
		AdditionalServiceIds:  serviceIDs,
	}
}
