package availability

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestPetSizeLogic_ProtoToModel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	logic := NewPetSizeLogicWithRepository(mockRepo)
	serviceID := int64(123)

	tests := []struct {
		name     string
		config   *offeringpb.AvailablePetSize
		expected []*model.ServicePetAvailabilityScope
	}{
		{
			name:   "nil config should return ALL_SIZES scope",
			config: nil,
			expected: []*model.ServicePetAvailabilityScope{
				{
					ServiceID: serviceID,
					ScopeType: offeringpb.PetAvailabilityScopeType_ALL_SIZES,
					TargetID:  0,
				},
			},
		},
		{
			name: "IsAll true should return ALL_SIZES scope",
			config: &offeringpb.AvailablePetSize{
				IsAll: true,
			},
			expected: []*model.ServicePetAvailabilityScope{
				{
					ServiceID: serviceID,
					ScopeType: offeringpb.PetAvailabilityScopeType_ALL_SIZES,
					TargetID:  0,
				},
			},
		},
		{
			name: "specific pet sizes should return SPECIFIC_SIZE scopes",
			config: &offeringpb.AvailablePetSize{
				IsAll:      false,
				PetSizeIds: []int64{1, 2, 3},
			},
			expected: []*model.ServicePetAvailabilityScope{
				{
					ServiceID: serviceID,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  1,
				},
				{
					ServiceID: serviceID,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  2,
				},
				{
					ServiceID: serviceID,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  3,
				},
			},
		},
		{
			name: "empty pet size IDs should return empty scopes",
			config: &offeringpb.AvailablePetSize{
				IsAll:      false,
				PetSizeIds: []int64{},
			},
			expected: []*model.ServicePetAvailabilityScope{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, serviceID, tt.config)
			assert.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				assert.Equal(t, expected.ServiceID, result[i].ServiceID)
				assert.Equal(t, expected.ScopeType, result[i].ScopeType)
				assert.Equal(t, expected.TargetID, result[i].TargetID)
			}
		})
	}
}

func TestPetSizeLogic_ModelToProto(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	logic := NewPetSizeLogicWithRepository(mockRepo)

	tests := []struct {
		name     string
		scopes   []*model.ServicePetAvailabilityScope
		expected *offeringpb.AvailablePetSize
	}{
		{
			name:     "empty scopes should return IsAll true",
			scopes:   []*model.ServicePetAvailabilityScope{},
			expected: &offeringpb.AvailablePetSize{IsAll: true, PetSizeIds: []int64{}},
		},
		{
			name: "ALL_SIZES scope should return IsAll true",
			scopes: []*model.ServicePetAvailabilityScope{
				{
					ServiceID: 123,
					ScopeType: offeringpb.PetAvailabilityScopeType_ALL_SIZES,
					TargetID:  0,
				},
			},
			expected: &offeringpb.AvailablePetSize{
				IsAll:      true,
				PetSizeIds: []int64{},
			},
		},
		{
			name: "SPECIFIC_SIZE scopes should return correct pet size IDs",
			scopes: []*model.ServicePetAvailabilityScope{
				{
					ServiceID: 123,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  1,
				},
				{
					ServiceID: 123,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  2,
				},
				{
					ServiceID: 123,
					ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
					TargetID:  3,
				},
			},
			expected: &offeringpb.AvailablePetSize{
				IsAll:      false,
				PetSizeIds: []int64{1, 2, 3},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.ModelToProto(tt.scopes)

			assert.Equal(t, tt.expected.IsAll, result.IsAll)
			assert.ElementsMatch(t, tt.expected.PetSizeIds, result.PetSizeIds)
		})
	}
}

func TestPetSizeLogic_ListAvailableServiceIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	logic := NewPetSizeLogicWithRepository(mockRepo)
	ctx := context.Background()

	tests := []struct {
		name           string
		orgType        organizationpb.OrganizationType
		orgID          int64
		petIdentities  []service.PetIdentity
		mockSetup      func()
		expectedResult *FilterResult
		expectedError  bool
	}{
		{
			name:          "empty petIdentities should return empty list",
			orgType:       organizationpb.OrganizationType_COMPANY,
			orgID:         100888,
			petIdentities: []service.PetIdentity{},
			mockSetup: func() {
				// No mock calls expected
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{}, IsFiltered: true},
			expectedError:  false,
		},
		{
			name:    "single petIdentity should return service IDs",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{PetSizeID: int64Ptr(1)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDsByPetSize(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							PetSizeID: int64Ptr(1),
						},
					},
				).Return([]int64{1, 2, 3}, nil)
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{1, 2, 3}, IsFiltered: true},
			expectedError:  false,
		},
		{
			name:    "multiple petIdentities should return intersection",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{PetSizeID: int64Ptr(1)},
				{PetSizeID: int64Ptr(2)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDsByPetSize(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							PetSizeID: int64Ptr(1),
						},
					},
				).Return([]int64{1, 2, 3}, nil)

				mockRepo.EXPECT().ListAvailableServiceIDsByPetSize(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							PetSizeID: int64Ptr(2),
						},
					},
				).Return([]int64{2, 3, 4}, nil)
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{2, 3}, IsFiltered: true}, // intersection of [1,2,3] and [2,3,4]
			expectedError:  false,
		},
		{
			name:    "repository error should be returned",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{PetSizeID: int64Ptr(1)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDsByPetSize(
					gomock.Any(),
					gomock.Any(),
				).Return(nil, assert.AnError)
			},
			expectedResult: nil,
			expectedError:  true,
		},
		{
			name:    "no pet size info should return no filter result",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{PetSizeID: nil},
				{PetSizeID: nil},
			},
			mockSetup: func() {
				// No mock calls expected
			},
			expectedResult: &FilterResult{ServiceIDs: nil, IsFiltered: false},
			expectedError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			result, err := logic.ListAvailableServiceIDs(ctx, tt.orgType, tt.orgID, tt.petIdentities)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}
