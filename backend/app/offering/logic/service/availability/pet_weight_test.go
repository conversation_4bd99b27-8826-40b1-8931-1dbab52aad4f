package availability

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// Helper function to create float64 pointer
func float64Ptr(v float64) *float64 {
	return &v
}

func TestPetWeightLogic_ListAvailableServiceIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetWeightRepository(ctrl)
	logic := NewPetWeightLogicWithRepository(mockRepo)
	ctx := context.Background()

	tests := []struct {
		name           string
		orgType        organizationpb.OrganizationType
		orgID          int64
		petIdentities  []service.PetIdentity
		mockSetup      func()
		expectedResult *FilterResult
		expectedError  bool
	}{
		{
			name:          "empty petIdentities should return empty list",
			orgType:       organizationpb.OrganizationType_COMPANY,
			orgID:         100888,
			petIdentities: []service.PetIdentity{},
			mockSetup: func() {
				// No mock calls expected
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{}, IsFiltered: true},
			expectedError:  false,
		},
		{
			name:    "single petIdentity should return service IDs",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{Weight: float64Ptr(15.0)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDs(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							Weight: float64Ptr(15.0),
						},
					},
				).Return([]int64{1, 2, 3}, nil)
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{1, 2, 3}, IsFiltered: true},
			expectedError:  false,
		},
		{
			name:    "multiple petIdentities should return intersection",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{Weight: float64Ptr(15.0)},
				{Weight: float64Ptr(20.0)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDs(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							Weight: float64Ptr(15.0),
						},
					},
				).Return([]int64{1, 2, 3}, nil)

				mockRepo.EXPECT().ListAvailableServiceIDs(
					gomock.Any(),
					service.ListAvailableServiceIDsFilter{
						OrganizationType: organizationpb.OrganizationType_COMPANY,
						OrganizationID:   100888,
						PetIdentity: service.PetIdentity{
							Weight: float64Ptr(20.0),
						},
					},
				).Return([]int64{2, 3, 4}, nil)
			},
			expectedResult: &FilterResult{ServiceIDs: []int64{2, 3}, IsFiltered: true}, // intersection of [1,2,3] and [2,3,4]
			expectedError:  false,
		},
		{
			name:    "repository error should be returned",
			orgType: organizationpb.OrganizationType_COMPANY,
			orgID:   100888,
			petIdentities: []service.PetIdentity{
				{Weight: float64Ptr(15.0)},
			},
			mockSetup: func() {
				mockRepo.EXPECT().ListAvailableServiceIDs(
					gomock.Any(),
					gomock.Any(),
				).Return(nil, assert.AnError)
			},
			expectedResult: nil,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			result, err := logic.ListAvailableServiceIDs(ctx, tt.orgType, tt.orgID, tt.petIdentities)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}
