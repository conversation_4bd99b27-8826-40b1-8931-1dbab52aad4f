package availability

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	servicemock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	mock2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/pet/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestManager_NewManager(t *testing.T) {
	// 由于 NewManager 会初始化真实的 repository，这里我们跳过这个测试
	// 在实际环境中，应该使用依赖注入或者 mock 来测试
	t.Skip("Skipping test that requires database connection")
}

func TestManager_NewManagerWithRepo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPetScopeRepo := mock.NewMockPetScopeRepository(ctrl)
	mockPetRepo := mock2.NewMockRepository(ctrl)
	mockWeightRepo := mock.NewMockPetWeightRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	manager := NewManagerWithRepository(mockPetRepo, mockPetScopeRepo, mockWeightRepo, mockServiceRepo)
	assert.NotNil(t, manager)
	assert.NotNil(t, manager.typeBreedLogic)
	assert.NotNil(t, manager.petSizeLogic)
	assert.NotNil(t, manager.coatTypeLogic)
	assert.NotNil(t, manager.petCodeLogic)
	assert.Equal(t, mockPetScopeRepo, manager.petScopeRepo)
}

func TestManager_Save(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPetRepo := mock2.NewMockRepository(ctrl)
	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	mockWeightRepo := mock.NewMockPetWeightRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	manager := NewManagerWithRepository(mockPetRepo, mockRepo, mockWeightRepo, mockServiceRepo)
	ctx := context.Background()
	mockQuery := &query.Query{}

	// 设置 mock 的期望行为
	mockRepo.EXPECT().WithQuery(mockQuery).Return(mockRepo).AnyTimes()
	mockRepo.EXPECT().DeleteByServiceID(ctx, gomock.Any()).Return(nil).AnyTimes()
	mockRepo.EXPECT().BatchCreate(ctx, gomock.Any()).Return(nil).AnyTimes()
	// Mock expectations for pet weight
	mockWeightRepo.EXPECT().WithQuery(mockQuery).Return(mockWeightRepo).AnyTimes()
	mockWeightRepo.EXPECT().DeleteByServiceID(ctx, gomock.Any()).Return(nil).AnyTimes()
	mockWeightRepo.EXPECT().Create(ctx, gomock.Any()).Return(nil).AnyTimes() // 创建默认的 pet weight 记录

	tests := []struct {
		name         string
		availability *PetAvailability
		expectError  bool
	}{
		{
			name: "save with all pet availability types",
			availability: &PetAvailability{
				ServiceID: 123,
				PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
					IsAll: true,
				},
				PetSize: &offeringpb.AvailablePetSize{
					IsAll: true,
				},
				CoatType: &offeringpb.AvailableCoatType{
					IsAll: true,
				},
				PetCode: &offeringpb.AvailablePetCode{
					RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION,
				},
			},
			expectError: false,
		},
		{
			name: "save with partial pet availability types",
			availability: &PetAvailability{
				ServiceID: 456,
				PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
					IsAll: false,
					AvailablePetTypes: []*offeringpb.AvailablePetType{
						{
							PetTypeId: 1,
							IsAll:     true,
							BreedIds:  []int64{},
						},
					},
				},
				PetSize:  nil,
				CoatType: nil,
				PetCode:  nil,
			},
			expectError: false,
		},
		{
			name: "save with nil availability should not error",
			availability: &PetAvailability{
				ServiceID:    789,
				PetTypeBreed: nil,
				PetSize:      nil,
				CoatType:     nil,
				PetCode:      nil,
			},
			expectError: false,
		},
		{
			name: "save with all nil availability should create default records",
			availability: &PetAvailability{
				ServiceID:        999,
				OrganizationType: organizationpb.OrganizationType_COMPANY,
				OrganizationID:   123,
				PetTypeBreed:     nil,
				PetSize:          nil,
				CoatType:         nil,
				PetCode:          nil,
				PetWeight:        nil,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.Save(ctx, mockQuery, tt.availability)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestManager_DeleteByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	mockPetRepo := mock2.NewMockRepository(ctrl)
	mockWeightRepo := mock.NewMockPetWeightRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	manager := NewManagerWithRepository(mockPetRepo, mockRepo, mockWeightRepo, mockServiceRepo)
	ctx := context.Background()
	mockQuery := &query.Query{}
	serviceID := int64(123)

	// 设置 mock 的期望行为
	mockRepo.EXPECT().WithQuery(mockQuery).Return(mockRepo)
	mockRepo.EXPECT().DeleteByServiceID(ctx, serviceID).Return(nil)

	mockWeightRepo.EXPECT().WithQuery(mockQuery).Return(mockWeightRepo)
	mockWeightRepo.EXPECT().DeleteByServiceID(ctx, serviceID).Return(nil)

	err := manager.DeleteByServiceID(ctx, mockQuery, serviceID)
	assert.NoError(t, err)
}

func TestManager_GetByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	mockPetRepo := mock2.NewMockRepository(ctrl)
	mockWeightRepo := mock.NewMockPetWeightRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	manager := NewManagerWithRepository(mockPetRepo, mockRepo, mockWeightRepo, mockServiceRepo)
	ctx := context.Background()
	serviceID := int64(123)

	// 设置 mock 的期望行为
	mockRepo.EXPECT().List(ctx, gomock.Any()).Return([]*model.ServicePetAvailabilityScope{}, nil)
	mockWeightRepo.EXPECT().GetByServiceID(ctx, serviceID).Return([]*model.ServicePetWeightRange{}, nil)

	result, err := manager.GetByServiceID(ctx, serviceID)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.ServiceID)
}

func TestManager_ProtoToModel_Integration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockPetScopeRepository(ctrl)
	mockPetRepo := mock2.NewMockRepository(ctrl)
	mockWeightRepo := mock.NewMockPetWeightRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	manager := NewManagerWithRepository(mockPetRepo, mockRepo, mockWeightRepo, mockServiceRepo)
	serviceID := int64(123)

	// 测试完整的转换流程
	availability := &PetAvailability{
		ServiceID: serviceID,
		PetTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		PetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2},
		},
		CoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1},
		},
		PetCode: &offeringpb.AvailablePetCode{
			RuleType:   offeringpb.AvailabilityRuleType_INCLUDE,
			PetCodeIds: []int64{1, 2, 3},
		},
	}

	// 测试各个 logic 的 ProtoToModel 方法
	typeBreedScopes := manager.typeBreedLogic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, serviceID, availability.PetTypeBreed)
	assert.Len(t, typeBreedScopes, 1)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_TYPE, typeBreedScopes[0].ScopeType)
	assert.Equal(t, int64(1), typeBreedScopes[0].TargetID)

	petSizeScopes := manager.petSizeLogic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, serviceID, availability.PetSize)
	assert.Len(t, petSizeScopes, 2)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE, petSizeScopes[0].ScopeType)
	assert.Equal(t, int64(1), petSizeScopes[0].TargetID)

	coatTypeScopes := manager.coatTypeLogic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, serviceID, availability.CoatType)
	assert.Len(t, coatTypeScopes, 1)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_COAT_TYPE, coatTypeScopes[0].ScopeType)
	assert.Equal(t, int64(1), coatTypeScopes[0].TargetID)

	petCodeScopes := manager.petCodeLogic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, serviceID, availability.PetCode)
	assert.Len(t, petCodeScopes, 3)
	assert.Equal(t, offeringpb.PetAvailabilityScopeType_SPECIFIC_CODE, petCodeScopes[0].ScopeType)
	assert.Equal(t, int64(1), petCodeScopes[0].TargetID)
}

// TestPetWeightLogic_ProtoToModel 测试 PetWeightLogic 的 ProtoToModel 方法
func TestPetWeightLogic_ProtoToModel(t *testing.T) {
	petWeightLogic := &PetWeightLogic{}

	tests := []struct {
		name        string
		config      *offeringpb.AvailablePetWeight
		serviceID   int64
		expectError bool
		expectLen   int
	}{
		{
			name:        "nil config should return all ranges available",
			config:      nil,
			serviceID:   123,
			expectError: false,
			expectLen:   1,
		},
		{
			name: "IsAll true should return all ranges available",
			config: &offeringpb.AvailablePetWeight{
				IsAll: true,
			},
			serviceID:   123,
			expectError: false,
			expectLen:   1,
		},
		{
			name: "valid weight ranges should return correct ranges",
			config: &offeringpb.AvailablePetWeight{
				IsAll: false,
				PetWeightRanges: []*offeringpb.PetWeightRange{
					{MinWeight: 1, MaxWeight: 10},
					{MinWeight: 15, MaxWeight: 25},
				},
			},
			serviceID:   123,
			expectError: false,
			expectLen:   2,
		},
		{
			name: "overlapping weight ranges should return error",
			config: &offeringpb.AvailablePetWeight{
				IsAll: false,
				PetWeightRanges: []*offeringpb.PetWeightRange{
					{MinWeight: 1, MaxWeight: 15},  // 重叠范围
					{MinWeight: 10, MaxWeight: 25}, // 与上面重叠
				},
			},
			serviceID:   123,
			expectError: true,
			expectLen:   0,
		},
		{
			name: "single weight range should not error",
			config: &offeringpb.AvailablePetWeight{
				IsAll: false,
				PetWeightRanges: []*offeringpb.PetWeightRange{
					{MinWeight: 1, MaxWeight: 10},
				},
			},
			serviceID:   123,
			expectError: false,
			expectLen:   1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ranges, err := petWeightLogic.ProtoToModel(organizationpb.OrganizationType_COMPANY, 100888, tt.serviceID, tt.config)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, ranges)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, ranges)
				assert.Len(t, ranges, tt.expectLen)

				// 验证返回的 ranges 结构
				for _, r := range ranges {
					assert.Equal(t, tt.serviceID, r.ServiceID)
					if tt.config != nil && !tt.config.IsAll && len(tt.config.PetWeightRanges) > 0 {
						assert.False(t, r.IsAllRange)
						assert.NotNil(t, r.MinWeight)
						assert.NotNil(t, r.MaxWeight)
					} else {
						assert.True(t, r.IsAllRange)
					}
				}
			}
		})
	}
}

// TestPetWeightLogic_ModelToProto 测试 PetWeightLogic 的 ModelToProto 方法
func TestPetWeightLogic_ModelToProto(t *testing.T) {
	petWeightLogic := &PetWeightLogic{}

	tests := []struct {
		name     string
		ranges   []*model.ServicePetWeightRange
		expected *offeringpb.AvailablePetWeight
	}{
		{
			name:   "empty ranges should return all available",
			ranges: []*model.ServicePetWeightRange{},
			expected: &offeringpb.AvailablePetWeight{
				IsAll:           true,
				PetWeightRanges: []*offeringpb.PetWeightRange{},
			},
		},
		{
			name: "IsAllRange true should return all available",
			ranges: []*model.ServicePetWeightRange{
				{IsAllRange: true},
			},
			expected: &offeringpb.AvailablePetWeight{
				IsAll:           true,
				PetWeightRanges: []*offeringpb.PetWeightRange{},
			},
		},
		{
			name: "specific ranges should return correct ranges",
			ranges: []*model.ServicePetWeightRange{
				{MinWeight: 1.0, MaxWeight: 10.0, IsAllRange: false},
				{MinWeight: 15.0, MaxWeight: 25.0, IsAllRange: false},
			},
			expected: &offeringpb.AvailablePetWeight{
				IsAll: false,
				PetWeightRanges: []*offeringpb.PetWeightRange{
					{MinWeight: 1, MaxWeight: 10},
					{MinWeight: 15, MaxWeight: 25},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := petWeightLogic.ModelToProto(tt.ranges)

			assert.NotNil(t, result)
			assert.Equal(t, tt.expected.IsAll, result.IsAll)
			assert.Len(t, result.PetWeightRanges, len(tt.expected.PetWeightRanges))

			for i, expectedRange := range tt.expected.PetWeightRanges {
				if i < len(result.PetWeightRanges) {
					assert.Equal(t, expectedRange.MinWeight, result.PetWeightRanges[i].MinWeight)
					assert.Equal(t, expectedRange.MaxWeight, result.PetWeightRanges[i].MaxWeight)
				}
			}
		})
	}
}
