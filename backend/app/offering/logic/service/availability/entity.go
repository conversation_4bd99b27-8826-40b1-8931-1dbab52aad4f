package availability

import (
	"github.com/samber/lo"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// PetAvailability 宠物可用性配置结构
type PetAvailability struct {
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64
	ServiceID        int64
	PetTypeBreed     *offeringpb.AvailablePetTypeBreed
	PetSize          *offeringpb.AvailablePetSize
	CoatType         *offeringpb.AvailableCoatType
	PetCode          *offeringpb.AvailablePetCode
	PetWeight        *offeringpb.AvailablePetWeight
}

// FilterResult 可用性过滤结果结构
type FilterResult struct {
	// 可用服务ID列表
	ServiceIDs []int64
	// 是否进行了过滤。true: 进行了过滤, false: 不过滤（需要获取所有服务ID）
	IsFiltered bool
}

// IntersectServiceIDLists 计算多个服务ID列表的交集
func IntersectServiceIDLists(serviceIDLists [][]int64) []int64 {
	if len(serviceIDLists) == 0 {
		return []int64{}
	}

	result := serviceIDLists[0]
	for i := 1; i < len(serviceIDLists); i++ {
		result = lo.Intersect(result, serviceIDLists[i])
	}

	return result
}
