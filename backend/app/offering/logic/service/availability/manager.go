package availability

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"github.com/samber/lo"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/pet"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// Manager 负责管理和协调不同类型的 availability 逻辑
type Manager struct {
	typeBreedLogic *TypeBreedLogic
	petSizeLogic   *PetSizeLogic
	coatTypeLogic  *CoatTypeLogic
	petCodeLogic   *PetCodeLogic
	petWeightLogic *PetWeightLogic
	petRepo        pet.Repository
	petScopeRepo   service.PetScopeRepository
	petWeightRepo  service.PetWeightRepository
	serviceRepo    service.Repository
}

// NewManager 创建 availability 管理器
func NewManager() *Manager {
	return &Manager{
		typeBreedLogic: NewTypeBreedLogic(),
		petSizeLogic:   NewPetSizeLogic(),
		coatTypeLogic:  NewCoatTypeLogic(),
		petCodeLogic:   NewPetCodeLogic(),
		petWeightLogic: NewPetWeightLogic(),
		petRepo:        pet.NewRepository(),
		petScopeRepo:   service.NewPetScopeRepository(),
		petWeightRepo:  service.NewPetWeightRepository(),
		serviceRepo:    service.NewRepository(),
	}
}

// NewManagerWithRepository 创建 availability 管理器，允许传入自定义的 repository
func NewManagerWithRepository(
	petRepo pet.Repository,
	petScopeRepo service.PetScopeRepository,
	petWeightRepo service.PetWeightRepository,
	serviceRepo service.Repository,
) *Manager {
	return &Manager{
		typeBreedLogic: NewTypeBreedLogicWithRepository(petScopeRepo),
		petSizeLogic:   NewPetSizeLogicWithRepository(petScopeRepo),
		coatTypeLogic:  NewCoatTypeLogicWithRepository(petScopeRepo),
		petCodeLogic:   NewPetCodeLogicWithRepository(petScopeRepo),
		petWeightLogic: NewPetWeightLogicWithRepository(petWeightRepo),
		petRepo:        petRepo,
		petScopeRepo:   petScopeRepo,
		petWeightRepo:  petWeightRepo,
		serviceRepo:    serviceRepo,
	}
}

// Save 保存所有类型的可用性配置
func (m *Manager) Save(ctx context.Context, query *query.Query, availability *PetAvailability) error {
	// 1. 转换 Pet availability 配置为通用的 scope 记录，如果某项配置为空，则创建默认的 "适用于所有" 记录确保能被查询到
	var allScopes []*model.ServicePetAvailabilityScope
	var serviceID = availability.ServiceID
	var orgType = availability.OrganizationType
	var orgID = availability.OrganizationID
	if availability.PetTypeBreed != nil {
		scopes := m.typeBreedLogic.ProtoToModel(orgType, orgID, serviceID, availability.PetTypeBreed)
		allScopes = append(allScopes, scopes...)
	} else {
		allScopes = append(allScopes, &model.ServicePetAvailabilityScope{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_TYPES_AND_BREEDS,
		})
	}

	if availability.PetSize != nil {
		scopes := m.petSizeLogic.ProtoToModel(orgType, orgID, serviceID, availability.PetSize)
		allScopes = append(allScopes, scopes...)
	} else {
		allScopes = append(allScopes, &model.ServicePetAvailabilityScope{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_SIZES,
		})
	}

	if availability.CoatType != nil {
		scopes := m.coatTypeLogic.ProtoToModel(orgType, orgID, serviceID, availability.CoatType)
		allScopes = append(allScopes, scopes...)
	} else {
		allScopes = append(allScopes, &model.ServicePetAvailabilityScope{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_COAT_TYPES,
		})
	}

	if availability.PetCode != nil {
		scopes := m.petCodeLogic.ProtoToModel(orgType, orgID, serviceID, availability.PetCode)
		allScopes = append(allScopes, scopes...)
	} else {
		allScopes = append(allScopes, &model.ServicePetAvailabilityScope{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_CODES,
		})
	}

	// 2. 批量保存所有 scope 记录
	if err := m.petScopeRepo.WithQuery(query).BatchCreate(ctx, allScopes); err != nil {
		return errors.New("failed to batch create pet availability scopes: %w")
	}

	// 3. 保存 Pet weight ranges
	if availability.PetWeight != nil {
		weightRanges, err := m.petWeightLogic.ProtoToModel(orgType, orgID, serviceID, availability.PetWeight)
		if err != nil {
			return err
		}
		if len(weightRanges) > 0 {
			if err := m.petWeightRepo.WithQuery(query).BatchCreate(ctx, weightRanges); err != nil {
				return errors.New("failed to batch create pet weight ranges: %w")
			}
		}
	} else {
		// 当 PetWeight 为 nil 时，保存一条 isAll 的记录
		defaultWeightRange := &model.ServicePetWeightRange{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			IsAllRange:       true,
		}
		if err := m.petWeightRepo.WithQuery(query).Create(ctx, defaultWeightRange); err != nil {
			return errors.New("failed to create default pet weight range: %w")
		}
	}

	return nil
}

// Update 更新所有类型的可用性配置
func (m *Manager) Update(ctx context.Context, query *query.Query, availability *PetAvailability) error {
	if err := m.petScopeRepo.WithQuery(query).DeleteByServiceID(ctx, availability.ServiceID); err != nil {
		return errors.New("failed to delete pet availability scopes: %w")
	}
	if err := m.petWeightRepo.WithQuery(query).DeleteByServiceID(ctx, availability.ServiceID); err != nil {
		return errors.New("failed to delete pet weight ranges: %w")
	}

	return m.Save(ctx, query, availability)
}

// DeleteByServiceID 删除所有类型的可用性配置
func (m *Manager) DeleteByServiceID(ctx context.Context, query *query.Query, serviceID int64) error {
	if err := m.petScopeRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet availability scopes: %w")
	}

	if err := m.petWeightRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet weight ranges: %w")
	}

	return nil
}

// GetByServiceID 根据服务ID获取所有类型的可用性配置
func (m *Manager) GetByServiceID(ctx context.Context, serviceID int64) (*PetAvailability, error) {
	allScopes, err := m.petScopeRepo.List(ctx, service.ListScopeFilter{
		ServiceIDs: []int64{serviceID},
	})
	if err != nil {
		return nil, errors.New("failed to get pet availability scopes: %w")
	}

	// 获取 pet weight ranges
	weightRanges, err := m.petWeightRepo.GetByServiceID(ctx, serviceID)
	if err != nil {
		return nil, errors.New("failed to get pet weight ranges: %w")
	}

	// TODO: 如果没有配置，返回适用于所有宠物类型的默认配置（不确定是否需要）
	if allScopes == nil && len(weightRanges) == 0 {
		return &PetAvailability{
			ServiceID:    serviceID,
			PetTypeBreed: &offeringpb.AvailablePetTypeBreed{IsAll: true},
			PetSize:      &offeringpb.AvailablePetSize{IsAll: true},
			CoatType:     &offeringpb.AvailableCoatType{IsAll: true},
			PetCode:      &offeringpb.AvailablePetCode{RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION},
			PetWeight:    &offeringpb.AvailablePetWeight{IsAll: true},
		}, nil
	}

	// 将 scope 记录转换为对应的 pb 配置
	// 从第一个 scope 记录中获取组织信息（如果有的话）
	var orgType organizationpb.OrganizationType
	var orgID int64
	if len(allScopes) > 0 {
		orgType = allScopes[0].OrganizationType
		orgID = allScopes[0].OrganizationID
	} else if len(weightRanges) > 0 {
		orgType = weightRanges[0].OrganizationType
		orgID = weightRanges[0].OrganizationID
	}

	return &PetAvailability{
		OrganizationType: orgType,
		OrganizationID:   orgID,
		ServiceID:        serviceID,
		PetTypeBreed:     m.typeBreedLogic.ModelToProto(allScopes),
		PetSize:          m.petSizeLogic.ModelToProto(allScopes),
		CoatType:         m.coatTypeLogic.ModelToProto(allScopes),
		PetCode:          m.petCodeLogic.ModelToProto(allScopes),
		PetWeight:        m.petWeightLogic.ModelToProto(weightRanges),
	}, nil
}

// getAllServiceIDs 获取指定组织下的所有服务ID
func (m *Manager) getAllServiceIDs(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64) ([]int64, error) {
	filter := &service.ListServiceFilter{
		OrganizationType: orgType,
		OrganizationID:   orgID,
	}

	services, _, err := m.serviceRepo.List(ctx, filter, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get all service IDs: %w", err)
	}

	serviceIDs := make([]int64, len(services))
	for i, s := range services {
		serviceIDs[i] = s.ID
	}

	return serviceIDs, nil
}

// ListAvailableServiceIDs 获取指定宠物 ID 列表可用的服务 ID 列表
func (m *Manager) ListAvailableServiceIDs(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64, petIDs []int64) ([]int64, error) {
	// 1. 根据 pet id 取详情和元信息
	pets, err := m.petRepo.BatchGetPetInfo(ctx, petIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet info: %w", err)
	}
	extraInfos, err := m.petRepo.BatchGetPetExtraInfo(ctx, petIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet extra info: %w", err)
	}
	petIDToExtraInfo := lo.KeyBy(extraInfos, func(extraInfo *svcpb.BatchGetPetExtraInfoResponse_PetExtraInfo) int64 {
		return extraInfo.GetPetId()
	})

	petIdentities := lo.Map(pets, func(pet *customerpb.BusinessCustomerPetInfoModel, _ int) service.PetIdentity {
		weight, err := strconv.ParseFloat(pet.GetWeight(), 32)
		if err != nil {
			log.Errorf("failed to parse pet weight: %w", err)
			weight = 0.0
		}
		extraInfo := petIDToExtraInfo[pet.GetId()]

		return service.PetIdentity{
			PetTypeID:  lo.ToPtr(extraInfo.GetPetTypeId()),
			BreedID:    lo.ToPtr(extraInfo.GetBreedId()),
			PetSizeID:  lo.ToPtr(extraInfo.GetPetSizeId()),
			CoatTypeID: lo.ToPtr(extraInfo.GetCoatTypeId()),
			PetCodeID:  pet.GetPetCodeIds(),
			Weight:     &weight,
		}
	})

	// 2. 用二维数组保存所有查询结果
	var allServiceIDLists [][]int64

	// 2.1 类型和品种过滤
	typeBreedResult, err := m.typeBreedLogic.ListAvailableServiceIDs(ctx, orgType, orgID, petIdentities)
	if err != nil {
		return nil, fmt.Errorf("failed to get type breed service IDs: %w", err)
	}
	if typeBreedResult.IsFiltered {
		allServiceIDLists = append(allServiceIDLists, typeBreedResult.ServiceIDs)
	}

	// 2.2 宠物尺寸过滤
	petSizeResult, err := m.petSizeLogic.ListAvailableServiceIDs(ctx, orgType, orgID, petIdentities)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet size service IDs: %w", err)
	}
	if petSizeResult.IsFiltered {
		allServiceIDLists = append(allServiceIDLists, petSizeResult.ServiceIDs)
	}

	// 2.3 毛色类型过滤
	coatTypeResult, err := m.coatTypeLogic.ListAvailableServiceIDs(ctx, orgType, orgID, petIdentities)
	if err != nil {
		return nil, fmt.Errorf("failed to get coat type service IDs: %w", err)
	}
	if coatTypeResult.IsFiltered {
		allServiceIDLists = append(allServiceIDLists, coatTypeResult.ServiceIDs)
	}

	// 2.4 宠物代码过滤
	petCodeResult, err := m.petCodeLogic.ListAvailableServiceIDs(ctx, orgType, orgID, petIdentities)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet code service IDs: %w", err)
	}
	if petCodeResult.IsFiltered {
		allServiceIDLists = append(allServiceIDLists, petCodeResult.ServiceIDs)
	}

	// 2.5 宠物重量过滤
	petWeightResult, err := m.petWeightLogic.ListAvailableServiceIDs(ctx, orgType, orgID, petIdentities)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet weight service IDs: %w", err)
	}
	if petWeightResult.IsFiltered {
		allServiceIDLists = append(allServiceIDLists, petWeightResult.ServiceIDs)
	}

	// 3. 如果没有启用的过滤条件，获取并返回所有服务ID
	if len(allServiceIDLists) == 0 {
		allServiceIDs, err := m.getAllServiceIDs(ctx, orgType, orgID)
		if err != nil {
			return nil, fmt.Errorf("failed to get all service IDs: %w", err)
		}

		return allServiceIDs, nil
	}

	// 4. 遍历二维数组取交集
	result := allServiceIDLists[0]
	for i := 1; i < len(allServiceIDLists); i++ {
		result = lo.Intersect(result, allServiceIDLists[i])
	}

	return result, nil
}

// ListByServiceIDs 根据多个服务ID批量获取所有类型的可用性配置
func (m *Manager) ListByServiceIDs(ctx context.Context, serviceIDs []int64) (map[int64]*PetAvailability, error) {
	if len(serviceIDs) == 0 {
		return make(map[int64]*PetAvailability), nil
	}

	// 获取所有 scopes
	allScopes, err := m.petScopeRepo.List(ctx, service.ListScopeFilter{
		ServiceIDs: serviceIDs,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get pet availability scopes: %w", err)
	}

	// 按 service ID 分组 scopes
	scopesByServiceID := lo.GroupBy(allScopes, func(scope *model.ServicePetAvailabilityScope) int64 {
		return scope.ServiceID
	})

	// 获取所有 pet weight ranges
	weightRangesByServiceID, err := m.petWeightRepo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get pet weight ranges: %w", err)
	}

	// 为每个 service ID 构建结果
	result := make(map[int64]*PetAvailability)
	for _, serviceID := range serviceIDs {
		scopes := scopesByServiceID[serviceID]
		weightRanges := weightRangesByServiceID[serviceID]

		// 如果没有配置，返回适用于所有宠物类型的默认配置
		if len(scopes) == 0 && len(weightRanges) == 0 {
			result[serviceID] = &PetAvailability{
				ServiceID:    serviceID,
				PetTypeBreed: &offeringpb.AvailablePetTypeBreed{IsAll: true},
				PetSize:      &offeringpb.AvailablePetSize{IsAll: true},
				CoatType:     &offeringpb.AvailableCoatType{IsAll: true},
				PetCode:      &offeringpb.AvailablePetCode{RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION},
				PetWeight:    &offeringpb.AvailablePetWeight{IsAll: true},
			}

			continue
		}

		// 从第一个 scope 记录中获取组织信息（如果有的话）
		var orgType organizationpb.OrganizationType
		var orgID int64
		if len(scopes) > 0 {
			orgType = scopes[0].OrganizationType
			orgID = scopes[0].OrganizationID
		} else if len(weightRanges) > 0 {
			orgType = weightRanges[0].OrganizationType
			orgID = weightRanges[0].OrganizationID
		}

		result[serviceID] = &PetAvailability{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			PetTypeBreed:     m.typeBreedLogic.ModelToProto(scopes),
			PetSize:          m.petSizeLogic.ModelToProto(scopes),
			CoatType:         m.coatTypeLogic.ModelToProto(scopes),
			PetCode:          m.petCodeLogic.ModelToProto(scopes),
			PetWeight:        m.petWeightLogic.ModelToProto(weightRanges),
		}
	}

	return result, nil
}
