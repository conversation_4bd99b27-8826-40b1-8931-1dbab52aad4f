package availability

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// CoatTypeLogic 处理宠物毛类型的可用性逻辑
type CoatTypeLogic struct {
	repo service.PetScopeRepository
}

// NewCoatTypeLogic 创建新的 CoatTypeLogic 实例
func NewCoatTypeLogic() *CoatTypeLogic {
	return &CoatTypeLogic{
		repo: service.NewPetScopeRepository(),
	}
}

// NewCoatTypeLogicWithRepository 创建新的 CoatTypeLogic 实例，允许传入自定义的 repository
func NewCoatTypeLogicWithRepository(repo service.PetScopeRepository) *CoatTypeLogic {
	return &CoatTypeLogic{
		repo: repo,
	}
}

// ProtoToModel 将 pb 配置转换为 model 结构，供 manager 批量保存
func (l *CoatTypeLogic) ProtoToModel(
	orgType organizationpb.OrganizationType, orgID, serviceID int64,
	config *offeringpb.AvailableCoatType) []*model.ServicePetAvailabilityScope {
	// Case 1: 如果 config 为 nil，表示全部可用
	if config == nil {
		return []*model.ServicePetAvailabilityScope{
			{
				OrganizationType: orgType,
				OrganizationID:   orgID,
				ServiceID:        serviceID,
				ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_COAT_TYPES,
				TargetID:         0,
			},
		}
	}

	// Case 2: 如果 IsAll 为 true，则插入 ALL_COAT_TYPES 的 scope 记录
	if config.IsAll {
		return []*model.ServicePetAvailabilityScope{
			{
				OrganizationType: orgType,
				OrganizationID:   orgID,
				ServiceID:        serviceID,
				ScopeType:        offeringpb.PetAvailabilityScopeType_ALL_COAT_TYPES,
				TargetID:         0,
			},
		}
	}

	// Case 3: 部分可用，为每个宠物毛类型创建 scope 记录
	scopes := []*model.ServicePetAvailabilityScope{}
	for _, coatTypeID := range config.CoatTypeIds {
		scopes = append(scopes, &model.ServicePetAvailabilityScope{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			ScopeType:        offeringpb.PetAvailabilityScopeType_SPECIFIC_COAT_TYPE,
			TargetID:         coatTypeID,
		})
	}

	return scopes
}

// ModelToProto 将 model 结构转换为 pb 配置
func (l *CoatTypeLogic) ModelToProto(
	scopes []*model.ServicePetAvailabilityScope) *offeringpb.AvailableCoatType {
	// Case 1: 如果没有 scopes，表示全部可用
	if len(scopes) == 0 {
		return &offeringpb.AvailableCoatType{
			IsAll:       true,
			CoatTypeIds: []int64{},
		}
	}

	// Case 2: 检查是否有 ALL_COAT_TYPES 记录
	for _, scope := range scopes {
		if scope.ScopeType == offeringpb.PetAvailabilityScopeType_ALL_COAT_TYPES {
			return &offeringpb.AvailableCoatType{
				IsAll:       true,
				CoatTypeIds: []int64{},
			}
		}
	}

	// Case 3: 部分可用
	var coatTypeIDs []int64
	for _, scope := range scopes {
		if scope.ScopeType == offeringpb.PetAvailabilityScopeType_SPECIFIC_COAT_TYPE {
			coatTypeIDs = append(coatTypeIDs, scope.TargetID)
		}
	}

	return &offeringpb.AvailableCoatType{
		IsAll:       false,
		CoatTypeIds: coatTypeIDs,
	}
}

// ListAvailableServiceIDs 获取指定宠物身份信息列表可用的服务 ID 列表
// 处理逻辑：
// 1. 有毛色类型信息的宠物：进行正常的过滤查询
// 2. 没有毛色类型信息的宠物：相当于"不过滤"，IsFiltered = false
// 3. 最终结果：取所有宠物的交集
func (l *CoatTypeLogic) ListAvailableServiceIDs(
	ctx context.Context, orgType organizationpb.OrganizationType,
	orgID int64, petIdentities []service.PetIdentity,
) (*FilterResult, error) {

	// 边界情况处理
	if len(petIdentities) == 0 {
		return &FilterResult{ServiceIDs: []int64{}, IsFiltered: true}, nil
	}

	// 分离有毛色类型信息的宠物
	petsWithCoatType := l.filterPetsWithCoatType(petIdentities)
	if len(petsWithCoatType) == 0 {
		return &FilterResult{ServiceIDs: nil, IsFiltered: false}, nil
	}

	// 对有毛色类型信息的宠物进行过滤
	filteredServiceIDs, err := l.filterServiceIDsByCoatType(ctx, orgType, orgID, petsWithCoatType)
	if err != nil {
		return nil, err
	}

	return &FilterResult{ServiceIDs: filteredServiceIDs, IsFiltered: true}, nil
}

// filterPetsWithCoatType 筛选出有毛色类型信息的宠物
func (l *CoatTypeLogic) filterPetsWithCoatType(petIdentities []service.PetIdentity) []service.PetIdentity {
	var result []service.PetIdentity
	for _, petIdentity := range petIdentities {
		if petIdentity.CoatTypeID != nil {
			result = append(result, petIdentity)
		}
	}

	return result
}

// filterServiceIDsByCoatType 根据毛色类型信息过滤服务ID
func (l *CoatTypeLogic) filterServiceIDsByCoatType(
	ctx context.Context, orgType organizationpb.OrganizationType,
	orgID int64, petIdentities []service.PetIdentity,
) ([]int64, error) {

	// 收集所有宠物的服务ID列表
	var allServiceIDLists [][]int64

	for _, petIdentity := range petIdentities {
		filter := service.ListAvailableServiceIDsFilter{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			PetIdentity:      petIdentity,
		}
		serviceIDs, err := l.repo.ListAvailableServiceIDsByCoatType(ctx, filter)
		if err != nil {
			return nil, err
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 计算所有列表的交集
	return IntersectServiceIDLists(allServiceIDLists), nil
}
