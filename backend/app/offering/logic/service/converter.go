package service

import (
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	"github.com/MoeGolibrary/moego/backend/common/utils/money"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// CreateDefToModel converts protobuf ServiceCreateDef to model.Service
func CreateDefToModel(pb *offeringpb.ServiceCreateDef) *model.Service {
	if pb == nil {
		return nil
	}

	service := &model.Service{
		OrganizationType: pb.GetOrganizationType(),
		OrganizationID:   pb.GetOrganizationId(),
		CareTypeID:       pb.GetCareTypeId(),
		CategoryID:       pb.GetCategoryId(),
		Name:             pb.GetName(),
		Description:      pb.Description,
		ColorCode:        pb.GetColorCode(),
		Images:           pb.GetImages(),
		Source:           pb.GetSource(),
		Status:           pb.GetStatus(),
		Type:             offeringpb.Service_SERVICE,
		TaxID:            pb.GetTaxId(),
	}

	if pb.Price != nil {
		service.PriceAmount = money.ToDecimal(pb.GetPrice())
		service.PriceCurrency = pb.GetPrice().GetCurrencyCode()
	}

	return service
}

// UpdateDefToModel converts protobuf ServiceUpdateDef to model.Service
func UpdateDefToModel(pb *offeringpb.ServiceUpdateDef) *model.Service {
	if pb == nil {
		return nil
	}

	m := &model.Service{
		ID: pb.GetId(),
	}

	if pb.CategoryId != nil {
		m.CategoryID = pb.GetCategoryId()
	}
	if pb.Name != nil {
		m.Name = pb.GetName()
	}
	if pb.Description != nil {
		m.Description = pb.Description
	}
	if pb.ColorCode != nil {
		m.ColorCode = pb.GetColorCode()
	}
	if pb.Sort != nil {
		m.Sort = pb.GetSort()
	}
	if len(pb.Images) > 0 {
		m.Images = pb.Images
	}
	if pb.Status != nil {
		m.Status = pb.GetStatus()
	}
	if pb.Price != nil {
		m.PriceAmount = money.ToDecimal(pb.GetPrice())
		m.PriceCurrency = pb.GetPrice().GetCurrencyCode()
	}
	if pb.TaxId != nil {
		m.TaxID = pb.GetTaxId()
	}

	return m
}

// RequestToFilter 构造服务查询的过滤条件
func RequestToFilter(req *offeringpb.ListAvailableServicesRequest) *service.ListServiceFilter {
	filter := &service.ListServiceFilter{
		Types: []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	// 有过滤条件
	if req.Filter != nil {
		filter.CareTypeIDs = req.GetFilter().GetCareTypeIds()
		filter.CategoriesIDs = req.GetFilter().GetCategoryIds()
		filter.Statuses = req.GetFilter().GetStatuses()
		filter.Sources = req.GetFilter().GetSources()
		filter.Keyword = req.GetFilter().Keyword
	}

	return filter
}

// ModelToProto 转换 model.Service 为 proto.Service
func ModelToProto(m *model.Service) *offeringpb.Service {
	if m == nil {
		return nil
	}

	pb := &offeringpb.Service{
		Id:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationId:   m.OrganizationID,
		CareTypeId:       m.CareTypeID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           m.Status,
		Type:             m.Type,
		Price:            money.FromDecimal(m.PriceAmount, m.PriceCurrency),
		TaxId:            m.TaxID,
	}

	if m.CategoryID != 0 {
		pb.CategoryId = &m.CategoryID
	}

	if m.CreateTime != nil {
		pb.CreateTime = timestamppb.New(*m.CreateTime)
	}
	if m.UpdateTime != nil {
		pb.UpdateTime = timestamppb.New(*m.UpdateTime)
	}
	if m.DeleteTime != nil {
		pb.DeleteTime = timestamppb.New(*m.DeleteTime)
	}

	return pb
}

// ModelToAvailableProto 转换 model.Service 为 proto.AvailableService
func ModelToAvailableProto(m *model.Service) *offeringpb.AvailableService {
	if m == nil {
		return nil
	}

	pb := &offeringpb.AvailableService{
		Id:          m.ID,
		CareTypeId:  m.CareTypeID,
		CategoryId:  m.CategoryID,
		Name:        m.Name,
		Description: lo.FromPtr(m.Description),
		ColorCode:   m.ColorCode,
		Sort:        m.Sort,
		Images:      m.Images,
		Source:      m.Source,
		Status:      m.Status,
		Price:       money.FromDecimal(m.PriceAmount, m.PriceCurrency),
		TaxId:       m.TaxID,
	}

	return pb
}

func (b *BusinessScope) PBToModel(scope *AvailableBusinessScope) *model.ServiceBusinessScope {
	if scope == nil {
		return nil
	}

	m := &model.ServiceBusinessScope{
		OrganizationType: scope.OrganizationType,
		OrganizationID:   scope.OrganizationID,
		ServiceID:        scope.ServiceID,
	}
	if scope.AvailableBusiness == nil {
		m.IsAllBusiness = true
		m.AvailableBusinessIds = []int64{}
	} else {
		m.IsAllBusiness = scope.AvailableBusiness.GetIsAll()
		m.AvailableBusinessIds = scope.AvailableBusiness.GetBusinessIds()
	}

	return m
}

// AddOnRequestToFilter 构造 addon 查询的过滤条件
func AddOnRequestToFilter(req *offeringpb.ListAvailableAddOnsRequest) *service.ListServiceFilter {
	filter := &service.ListServiceFilter{
		Types: []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	// 有过滤条件
	if req.Filter != nil {
		filter.CategoriesIDs = req.GetFilter().GetCategoryIds()
		filter.Statuses = lo.Map(req.GetFilter().GetStatuses(),
			func(s offeringpb.AddOn_Status, _ int) offeringpb.Service_Status {
				return offeringpb.Service_Status(s)
			})
		filter.Sources = req.GetFilter().GetSources()
		filter.Keyword = req.GetFilter().Keyword
	}

	return filter
}

// ModelToAvailableAddOnProto 转换 model.Service 为 proto.AvailableAddOn
func ModelToAvailableAddOnProto(m *model.Service) *offeringpb.AvailableAddOn {
	if m == nil {
		return nil
	}

	pb := &offeringpb.AvailableAddOn{
		Id:          m.ID,
		CategoryId:  m.CategoryID,
		Name:        m.Name,
		Description: lo.FromPtr(m.Description),
		ColorCode:   m.ColorCode,
		Sort:        m.Sort,
		Images:      m.Images,
		Source:      m.Source,
		Status:      offeringpb.AddOn_Status(m.Status),
		Price:       money.FromDecimal(m.PriceAmount, m.PriceCurrency),
		TaxId:       m.TaxID,
	}

	return pb
}

// ToAttributes 将 proto.ServiceAttributes 转换为 proto.AvailableServiceAttributes
func ToAttributes(attributes *offeringpb.ServiceAttributes) *offeringpb.AvailableServiceAttributes {
	if attributes == nil {
		return nil
	}

	return &offeringpb.AvailableServiceAttributes{
		Duration:                  attributes.Duration,
		MaxDuration:               attributes.MaxDuration,
		IsRequiredStaff:           attributes.IsRequiredStaff,
		ObAlias:                   attributes.ObAlias,
		PriceUnit:                 attributes.PriceUnit,
		DefaultService:            attributes.DefaultService,
		ConditionalDefaultService: attributes.ConditionalDefaultService,
	}
}
