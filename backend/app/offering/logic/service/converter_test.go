package service

import (
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestCreateDefToModel(t *testing.T) {
	t.Run("正常转换", func(t *testing.T) {
		// 准备测试数据
		pb := &offeringpb.ServiceCreateDef{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			CareTypeId:       1,
			CategoryId:       lo.ToPtr(int64(2)),
			Name:             "Test Service",
			Description:      lo.ToPtr("Test Description"),
			ColorCode:        "#FF0000",
			Images:           []string{"image1.jpg", "image2.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			TaxId:            int64(123),
			Price: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
				Nanos:        *********, // 0.5 USD
			},
		}

		// 执行转换
		result := CreateDefToModel(pb)

		// 验证结果
		assert.NotNil(t, result)
		assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
		assert.Equal(t, int64(123), result.OrganizationID)
		assert.Equal(t, int64(1), result.CareTypeID)
		assert.Equal(t, int64(2), result.CategoryID)
		assert.Equal(t, "Test Service", result.Name)
		assert.Equal(t, "Test Description", *result.Description)
		assert.Equal(t, "#FF0000", result.ColorCode)
		assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
		assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
		assert.Equal(t, offeringpb.Service_ACTIVE, result.Status)
		assert.Equal(t, offeringpb.Service_SERVICE, result.Type)
		assert.Equal(t, int64(123), result.TaxID)
		assert.Equal(t, decimal.New(100*********, -9), result.PriceAmount)
		assert.Equal(t, "USD", result.PriceCurrency)
	})

	t.Run("空指针输入", func(t *testing.T) {
		result := CreateDefToModel(nil)
		assert.Nil(t, result)
	})

	t.Run("最小字段", func(t *testing.T) {
		pb := &offeringpb.ServiceCreateDef{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			CareTypeId:       1,
			Name:             "Minimal Service",
		}

		result := CreateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
		assert.Equal(t, int64(123), result.OrganizationID)
		assert.Equal(t, int64(1), result.CareTypeID)
		assert.Equal(t, int64(0), result.CategoryID)
		assert.Equal(t, "Minimal Service", result.Name)
		assert.Nil(t, result.Description)
		assert.Empty(t, result.ColorCode)
		assert.Empty(t, result.Images)
		assert.Equal(t, offeringpb.OfferingSource_OFFERING_SOURCE_UNSPECIFIED, result.Source)
		assert.Equal(t, offeringpb.Service_STATUS_UNSPECIFIED, result.Status)
		assert.Equal(t, offeringpb.Service_SERVICE, result.Type)
		assert.Equal(t, int64(0), result.TaxID)
		assert.True(t, decimal.NewFromFloat(0.0).Equal(result.PriceAmount))
		assert.Empty(t, result.PriceCurrency)
	})

	t.Run("价格转换精度测试", func(t *testing.T) {
		pb := &offeringpb.ServiceCreateDef{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			CareTypeId:       1,
			Name:             "Price Test Service",
			Price: &money.Money{
				CurrencyCode: "USD",
				Units:        0,
				Nanos:        *********, // 0.********* USD
			},
		}

		result := CreateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, decimal.New(*********, -9), result.PriceAmount)
		assert.Equal(t, "USD", result.PriceCurrency)
	})
}

func TestUpdateDefToModel(t *testing.T) {
	t.Run("完整字段更新", func(t *testing.T) {
		pb := &offeringpb.ServiceUpdateDef{
			Id:          1,
			CategoryId:  lo.ToPtr(int64(3)),
			Name:        lo.ToPtr("Updated Service"),
			Description: lo.ToPtr("Updated Description"),
			ColorCode:   lo.ToPtr("#00FF00"),
			Sort:        lo.ToPtr(int64(10)),
			Images:      []string{"updated1.jpg", "updated2.jpg"},
			Status:      lo.ToPtr(offeringpb.Service_INACTIVE),
			TaxId:       lo.ToPtr(int64(456)),
			Price: &money.Money{
				CurrencyCode: "EUR",
				Units:        200,
				Nanos:        *********, // 0.75 EUR
			},
		}

		result := UpdateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.ID)
		assert.Equal(t, int64(3), result.CategoryID)
		assert.Equal(t, "Updated Service", result.Name)
		assert.Equal(t, "Updated Description", *result.Description)
		assert.Equal(t, "#00FF00", result.ColorCode)
		assert.Equal(t, int64(10), result.Sort)
		assert.Equal(t, []string{"updated1.jpg", "updated2.jpg"}, result.Images)
		assert.Equal(t, offeringpb.Service_INACTIVE, result.Status)
		assert.Equal(t, int64(456), result.TaxID)
		assert.Equal(t, decimal.New(200*********, -9), result.PriceAmount)
		assert.Equal(t, "EUR", result.PriceCurrency)
	})

	t.Run("空指针输入", func(t *testing.T) {
		result := UpdateDefToModel(nil)
		assert.Nil(t, result)
	})

	t.Run("只更新ID", func(t *testing.T) {
		pb := &offeringpb.ServiceUpdateDef{
			Id: 999,
		}

		result := UpdateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, int64(999), result.ID)
		assert.Equal(t, int64(0), result.CategoryID)
		assert.Empty(t, result.Name)
		assert.Nil(t, result.Description)
		assert.Empty(t, result.ColorCode)
		assert.Equal(t, int64(0), result.Sort)
		assert.Empty(t, result.Images)
		assert.Equal(t, offeringpb.Service_STATUS_UNSPECIFIED, result.Status)
		assert.Equal(t, int64(0), result.TaxID)
		assert.True(t, decimal.NewFromFloat(0.0).Equal(result.PriceAmount))
		assert.Empty(t, result.PriceCurrency)
	})

	t.Run("部分字段更新", func(t *testing.T) {
		pb := &offeringpb.ServiceUpdateDef{
			Id:     2,
			Name:   lo.ToPtr("Partial Update"),
			Status: lo.ToPtr(offeringpb.Service_ACTIVE),
			TaxId:  lo.ToPtr(int64(789)),
		}

		result := UpdateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, int64(2), result.ID)
		assert.Equal(t, "Partial Update", result.Name)
		assert.Equal(t, offeringpb.Service_ACTIVE, result.Status)
		assert.Equal(t, int64(789), result.TaxID)
		// 其他字段应该保持零值
		assert.Equal(t, int64(0), result.CategoryID)
		assert.Nil(t, result.Description)
		assert.Empty(t, result.ColorCode)
		assert.Equal(t, int64(0), result.Sort)
		assert.Empty(t, result.Images)
		assert.True(t, decimal.NewFromFloat(0.0).Equal(result.PriceAmount))
		assert.Empty(t, result.PriceCurrency)
	})

	t.Run("空Images数组", func(t *testing.T) {
		pb := &offeringpb.ServiceUpdateDef{
			Id:     3,
			Images: []string{},
		}

		result := UpdateDefToModel(pb)

		assert.NotNil(t, result)
		assert.Equal(t, int64(3), result.ID)
		assert.Empty(t, result.Images)
	})
}

func TestRequestToFilter(t *testing.T) {
	t.Run("基本过滤条件", func(t *testing.T) {
		req := &offeringpb.ListAvailableServicesRequest{
			OrganizationType: organizationpb.OrganizationType_BUSINESS,
			OrganizationId:   456,
		}

		result := RequestToFilter(req)

		assert.NotNil(t, result)
		assert.Equal(t, []offeringpb.Service_Type{offeringpb.Service_SERVICE}, result.Types)
		assert.Nil(t, result.CareTypeIDs)
		assert.Nil(t, result.Statuses)
		assert.Nil(t, result.Keyword)
	})

	t.Run("带过滤条件", func(t *testing.T) {
		keyword := "spa"
		req := &offeringpb.ListAvailableServicesRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   789,
			Filter: &offeringpb.ListAvailableServicesRequest_Filter{
				CareTypeIds: []int64{1, 2, 3},
				Statuses:    []offeringpb.Service_Status{offeringpb.Service_ACTIVE},
				Keyword:     &keyword,
			},
		}

		result := RequestToFilter(req)

		assert.NotNil(t, result)
		assert.Equal(t, []offeringpb.Service_Type{offeringpb.Service_SERVICE}, result.Types)
		assert.Equal(t, []int64{1, 2, 3}, result.CareTypeIDs)
		assert.Equal(t, []offeringpb.Service_Status{offeringpb.Service_ACTIVE}, result.Statuses)
		assert.Equal(t, &keyword, result.Keyword)
	})

	t.Run("空过滤条件", func(t *testing.T) {
		req := &offeringpb.ListAvailableServicesRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Filter:           &offeringpb.ListAvailableServicesRequest_Filter{},
		}

		result := RequestToFilter(req)

		assert.NotNil(t, result)
		assert.Equal(t, []offeringpb.Service_Type{offeringpb.Service_SERVICE}, result.Types)
		assert.Nil(t, result.CareTypeIDs)
		assert.Nil(t, result.Statuses)
		assert.Nil(t, result.Keyword)
	})
}

func TestModelToProto(t *testing.T) {
	t.Run("完整模型转换", func(t *testing.T) {
		now := time.Now()
		model := &model.Service{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Test Service",
			Description:      lo.ToPtr("Test Description"),
			ColorCode:        "#FF0000",
			Sort:             10,
			Images:           []string{"image1.jpg", "image2.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			Type:             offeringpb.Service_SERVICE,
			TaxID:            int64(123),
			PriceAmount:      decimal.New(100*********, -9),
			PriceCurrency:    "USD",
			CreateTime:       &now,
			UpdateTime:       &now,
		}

		result := ModelToProto(model)

		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.Id)
		assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
		assert.Equal(t, int64(123), result.OrganizationId)
		assert.Equal(t, int64(1), result.CareTypeId)
		assert.Equal(t, int64(2), *result.CategoryId)
		assert.Equal(t, "Test Service", result.Name)
		assert.Equal(t, "Test Description", *result.Description)
		assert.Equal(t, "#FF0000", result.ColorCode)
		assert.Equal(t, int64(10), *result.Sort)
		assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
		assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
		assert.Equal(t, offeringpb.Service_ACTIVE, result.Status)
		assert.Equal(t, offeringpb.Service_SERVICE, result.Type)
		assert.Equal(t, int64(123), result.TaxId)
		assert.NotNil(t, result.Price)
		assert.Equal(t, "USD", result.Price.CurrencyCode)
		assert.Equal(t, int64(100), result.Price.Units)
		assert.Equal(t, int32(*********), result.Price.Nanos)
		assert.NotNil(t, result.CreateTime)
		assert.NotNil(t, result.UpdateTime)
	})

	t.Run("空指针输入", func(t *testing.T) {
		result := ModelToProto(nil)
		assert.Nil(t, result)
	})

	t.Run("最小字段模型", func(t *testing.T) {
		model := &model.Service{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_BUSINESS,
			OrganizationID:   456,
			CareTypeID:       1,
			Name:             "Minimal Service",
		}

		result := ModelToProto(model)

		assert.NotNil(t, result)
		assert.Equal(t, int64(2), result.Id)
		assert.Equal(t, organizationpb.OrganizationType_BUSINESS, result.OrganizationType)
		assert.Equal(t, int64(456), result.OrganizationId)
		assert.Equal(t, int64(1), result.CareTypeId)
		assert.Nil(t, result.CategoryId)
		assert.Equal(t, "Minimal Service", result.Name)
		assert.Nil(t, result.Description)
		assert.Empty(t, result.ColorCode)
		assert.Equal(t, int64(0), *result.Sort)
		assert.Empty(t, result.Images)
		assert.Equal(t, offeringpb.OfferingSource_OFFERING_SOURCE_UNSPECIFIED, result.Source)
		assert.Equal(t, offeringpb.Service_STATUS_UNSPECIFIED, result.Status)
		assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, result.Type)
		assert.Equal(t, int64(0), result.TaxId)
		assert.Equal(t, &money.Money{}, result.Price)
		assert.Nil(t, result.CreateTime)
		assert.Nil(t, result.UpdateTime)
		assert.Nil(t, result.DeleteTime)
	})

	t.Run("价格转换精度测试", func(t *testing.T) {
		model := &model.Service{
			ID:               3,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   789,
			CareTypeID:       1,
			Name:             "Price Test Service",
			PriceAmount:      decimal.New(*********, -9),
			PriceCurrency:    "EUR",
		}

		result := ModelToProto(model)

		assert.NotNil(t, result)
		assert.NotNil(t, result.Price)
		assert.Equal(t, "EUR", result.Price.CurrencyCode)
		assert.Equal(t, int64(0), result.Price.Units)
		assert.Equal(t, int32(*********), result.Price.Nanos)
	})

	t.Run("零价格也转换", func(t *testing.T) {
		model := &model.Service{
			ID:               4,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			Name:             "Zero Price Service",
			PriceAmount:      decimal.Zero,
			PriceCurrency:    "USD",
		}

		result := ModelToProto(model)

		assert.NotNil(t, result)
		assert.NotNil(t, result.Price)
		assert.Equal(t, &money.Money{
			CurrencyCode: "USD",
			Units:        0,
			Nanos:        0,
		}, result.Price)
	})

	t.Run("时间字段转换", func(t *testing.T) {
		now := time.Now()
		deleteTime := now.Add(24 * time.Hour)
		model := &model.Service{
			ID:               5,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			Name:             "Time Test Service",
			CreateTime:       &now,
			UpdateTime:       &now,
			DeleteTime:       &deleteTime,
		}

		result := ModelToProto(model)

		assert.NotNil(t, result)
		assert.NotNil(t, result.CreateTime)
		assert.NotNil(t, result.UpdateTime)
		assert.NotNil(t, result.DeleteTime)
	})
}
