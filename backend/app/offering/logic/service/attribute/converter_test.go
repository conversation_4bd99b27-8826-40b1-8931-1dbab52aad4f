package attribute

import (
	"testing"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	model2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	utils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestEntityToModel(t *testing.T) {
	// 测试正常转换
	entity := &ServiceAttribute{
		ID:             1,
		ServiceID:      100,
		FieldName:      "duration",
		AttributeKey:   offeringpb.AttributeKey_DURATION,
		AttributeValue: utils.StringToPBValue("60"),
	}

	model := EntityToModel(entity)

	assert.NotNil(t, model)
	assert.Equal(t, int64(1), model.ID)
	assert.Equal(t, int64(100), model.ServiceID)
	assert.Equal(t, "duration", model.FieldName)
	assert.Equal(t, offeringpb.AttributeKey_DURATION, model.AttributeKey)
	assert.Equal(t, "60", model.AttributeValue)

	// 测试 nil 输入
	model = EntityToModel(nil)
	assert.Nil(t, model)
}

func TestBasicProcessor_ToModel(t *testing.T) {
	processor := &BasicProcessor{}

	// 测试 Duration 属性
	duration := int32(60)
	attributes := &offeringpb.ServiceAttributes{
		Duration: &duration,
	}

	result, err := processor.ToModel(1, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), result[0].ServiceID)
	assert.Equal(t, "duration", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_DURATION, result[0].AttributeKey)
	assert.Equal(t, "60", result[0].AttributeValue)

	// 测试 MaxDuration 属性
	maxDuration := int32(120)
	attributes = &offeringpb.ServiceAttributes{
		MaxDuration: &maxDuration,
	}

	result, err = processor.ToModel(2, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(2), result[0].ServiceID)
	assert.Equal(t, "max_duration", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_MAX_DURATION, result[0].AttributeKey)
	assert.Equal(t, "120", result[0].AttributeValue)

	// 测试 IsRequiredStaff 属性
	isRequiredStaff := true
	attributes = &offeringpb.ServiceAttributes{
		IsRequiredStaff: &isRequiredStaff,
	}

	result, err = processor.ToModel(3, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(3), result[0].ServiceID)
	assert.Equal(t, "is_required_staff", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_IS_REQUIRED_STAFF, result[0].AttributeKey)
	assert.Equal(t, "true", result[0].AttributeValue)

	// 测试 AutoAssignStaff 属性
	autoAssignStaff := false
	attributes = &offeringpb.ServiceAttributes{
		AutoAssignStaff: &autoAssignStaff,
	}

	result, err = processor.ToModel(4, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(4), result[0].ServiceID)
	assert.Equal(t, "auto_assign_staff", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_STAFF_AUTO_ASSIGN, result[0].AttributeKey)
	assert.Equal(t, "false", result[0].AttributeValue)

	// 测试 ObAlias 属性
	obAlias := "test-alias"
	attributes = &offeringpb.ServiceAttributes{
		ObAlias: &obAlias,
	}

	result, err = processor.ToModel(5, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(5), result[0].ServiceID)
	assert.Equal(t, "ob_alias", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_ONLINE_BOOKING_ALIAS, result[0].AttributeKey)
	assert.Equal(t, "test-alias", result[0].AttributeValue)

	// 测试 PriceUnit 属性
	priceUnit := offeringpb.PriceUnit_PER_SESSION
	attributes = &offeringpb.ServiceAttributes{
		PriceUnit: &priceUnit,
	}

	result, err = processor.ToModel(6, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(6), result[0].ServiceID)
	assert.Equal(t, "price_unit", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_PRICE_UNIT, result[0].AttributeKey)
	assert.Equal(t, "PER_SESSION", result[0].AttributeValue)

	// 测试同时包含多个属性
	attributes = &offeringpb.ServiceAttributes{
		Duration:        &duration,
		MaxDuration:     &maxDuration,
		IsRequiredStaff: &isRequiredStaff,
		AutoAssignStaff: &autoAssignStaff,
		ObAlias:         &obAlias,
		PriceUnit:       &priceUnit,
	}

	result, err = processor.ToModel(7, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 6)

	// 测试 nil 输入
	result, err = processor.ToModel(8, nil)
	assert.NoError(t, err)
	assert.Nil(t, result)

	// 测试空属性
	result, err = processor.ToModel(9, &offeringpb.ServiceAttributes{})
	assert.NoError(t, err)
	assert.Len(t, result, 0)
}

func TestBasicProcessor_ToModel_JSONFields(t *testing.T) {
	processor := &BasicProcessor{}

	// 测试 ResultValidityPeriod 属性
	resultValidityPeriod := &offeringpb.ResultValidityPeriod{
		PeriodType: offeringpb.ResultValidityPeriod_ALWAYS_VALID,
	}
	attributes := &offeringpb.ServiceAttributes{
		ResultValidityPeriod: resultValidityPeriod,
	}

	result, err := processor.ToModel(1, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), result[0].ServiceID)
	assert.Equal(t, "result_validity_period", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_RESULT_RESETTABLE, result[0].AttributeKey)
	assert.Contains(t, result[0].AttributeValue, "period_type")

	// 测试 DefaultService 属性
	defaultService := &offeringpb.DefaultService{
		Enabled:    true,
		ServiceIds: []int64{1, 2, 3},
	}
	attributes = &offeringpb.ServiceAttributes{
		DefaultService: defaultService,
	}

	result, err = processor.ToModel(2, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(2), result[0].ServiceID)
	assert.Equal(t, "default_service", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_DEFAULT_SERVICE, result[0].AttributeKey)
	assert.Contains(t, result[0].AttributeValue, "enabled")
	assert.Contains(t, result[0].AttributeValue, "service_ids")

	// 测试 ConditionalDefaultService 属性
	conditionalDefaultService := &offeringpb.ConditionalDefaultService{
		Enabled: true,
		Condition: &offeringpb.ConditionalDefaultService_Condition{
			MinStayLength: 2,
			Unit:          offeringpb.ConditionalDefaultService_DAYS,
		},
		ApplyRules: []*offeringpb.ConditionalDefaultService_ApplyRule{
			{
				ServiceId: 1,
				DateType:  offeringpb.ConditionalDefaultService_EVERYDAY,
			},
		},
	}
	attributes = &offeringpb.ServiceAttributes{
		ConditionalDefaultService: conditionalDefaultService,
	}

	result, err = processor.ToModel(3, attributes)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(3), result[0].ServiceID)
	assert.Equal(t, "conditional_default_service", result[0].FieldName)
	assert.Equal(t, offeringpb.AttributeKey_CONDITIONAL_DEFAULT_SERVICE, result[0].AttributeKey)
	assert.Contains(t, result[0].AttributeValue, "enabled")
	assert.Contains(t, result[0].AttributeValue, "condition")
}

func TestBasicProcessor_FromModel(t *testing.T) {
	processor := &BasicProcessor{}

	// 测试 Duration 属性
	attributes := []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
	}

	result, err := processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(60), *result.Duration)
	assert.Nil(t, result.MaxDuration)

	// 测试 MaxDuration 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "max_duration",
			AttributeKey:   offeringpb.AttributeKey_MAX_DURATION,
			AttributeValue: "120",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Nil(t, result.Duration)
	assert.Equal(t, int32(120), *result.MaxDuration)

	// 测试 IsRequiredStaff 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "is_required_staff",
			AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
			AttributeValue: "true",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.True(t, *result.IsRequiredStaff)

	// 测试 AutoAssignStaff 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "auto_assign_staff",
			AttributeKey:   offeringpb.AttributeKey_STAFF_AUTO_ASSIGN,
			AttributeValue: "false",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.False(t, *result.AutoAssignStaff)

	// 测试 ObAlias 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "ob_alias",
			AttributeKey:   offeringpb.AttributeKey_ONLINE_BOOKING_ALIAS,
			AttributeValue: "test-alias",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-alias", *result.ObAlias)

	// 测试 PriceUnit 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "price_unit",
			AttributeKey:   offeringpb.AttributeKey_PRICE_UNIT,
			AttributeValue: "PER_SESSION",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, offeringpb.PriceUnit_PER_SESSION, *result.PriceUnit)

	// 测试同时包含多个属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
		{
			ServiceID:      1,
			FieldName:      "max_duration",
			AttributeKey:   offeringpb.AttributeKey_MAX_DURATION,
			AttributeValue: "120",
		},
		{
			ServiceID:      1,
			FieldName:      "is_required_staff",
			AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
			AttributeValue: "true",
		},
		{
			ServiceID:      1,
			FieldName:      "auto_assign_staff",
			AttributeKey:   offeringpb.AttributeKey_STAFF_AUTO_ASSIGN,
			AttributeValue: "false",
		},
		{
			ServiceID:      1,
			FieldName:      "ob_alias",
			AttributeKey:   offeringpb.AttributeKey_ONLINE_BOOKING_ALIAS,
			AttributeValue: "test-alias",
		},
		{
			ServiceID:      1,
			FieldName:      "price_unit",
			AttributeKey:   offeringpb.AttributeKey_PRICE_UNIT,
			AttributeValue: "PER_SESSION",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(60), *result.Duration)
	assert.Equal(t, int32(120), *result.MaxDuration)
	assert.True(t, *result.IsRequiredStaff)
	assert.False(t, *result.AutoAssignStaff)
	assert.Equal(t, "test-alias", *result.ObAlias)
	assert.Equal(t, offeringpb.PriceUnit_PER_SESSION, *result.PriceUnit)

	// 测试空列表
	result, err = processor.FromModel([]*model2.ServiceAttribute{})
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Nil(t, result.Duration)
	assert.Nil(t, result.MaxDuration)

	// 测试无效值 - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "invalid",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to parse duration")

	// 测试空值
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Nil(t, result.Duration)
}

func TestBasicProcessor_FromModel_JSONFields(t *testing.T) {
	processor := &BasicProcessor{}

	// 测试 ResultValidityPeriod 属性
	attributes := []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "result_validity_period",
			AttributeKey:   offeringpb.AttributeKey_RESULT_RESETTABLE,
			AttributeValue: `{"period_type":1}`,
		},
	}

	result, err := processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.ResultValidityPeriod)
	assert.Equal(t, offeringpb.ResultValidityPeriod_ALWAYS_VALID, result.ResultValidityPeriod.PeriodType)

	// 测试 DefaultService 属性
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "default_service",
			AttributeKey:   offeringpb.AttributeKey_DEFAULT_SERVICE,
			AttributeValue: `{"enabled":true,"service_ids":[1,2,3]}`,
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.DefaultService)
	assert.True(t, result.DefaultService.Enabled)
	assert.Equal(t, []int64{1, 2, 3}, result.DefaultService.ServiceIds)

	// 测试 ConditionalDefaultService 属性 - 简化版本
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "conditional_default_service",
			AttributeKey:   offeringpb.AttributeKey_CONDITIONAL_DEFAULT_SERVICE,
			AttributeValue: `{"enabled":true}`,
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.ConditionalDefaultService)
	assert.True(t, result.ConditionalDefaultService.Enabled)
}

func TestBasicProcessor_FromModel_ErrorHandling(t *testing.T) {
	processor := &BasicProcessor{}

	// 测试无效的布尔值 - 应该返回错误
	attributes := []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "is_required_staff",
			AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
			AttributeValue: "invalid_bool",
		},
	}

	result, err := processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to parse is required staff")

	// 测试无效的整数 - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "not_a_number",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to parse duration")

	// 测试无效的 PriceUnit - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "price_unit",
			AttributeKey:   offeringpb.AttributeKey_PRICE_UNIT,
			AttributeValue: "INVALID_UNIT",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to parse price unit")

	// 测试无效的 JSON - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "result_validity_period",
			AttributeKey:   offeringpb.AttributeKey_RESULT_RESETTABLE,
			AttributeValue: "invalid_json",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to unmarshal result validity period")

	// 测试无效的 DefaultService JSON - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "default_service",
			AttributeKey:   offeringpb.AttributeKey_DEFAULT_SERVICE,
			AttributeValue: "invalid_json",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to unmarshal default service")

	// 测试无效的 ConditionalDefaultService JSON - 应该返回错误
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "conditional_default_service",
			AttributeKey:   offeringpb.AttributeKey_CONDITIONAL_DEFAULT_SERVICE,
			AttributeValue: "invalid_json",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to unmarshal conditional default service")

	// 测试空字符串跳过逻辑
	attributes = []*model2.ServiceAttribute{
		{
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "",
		},
		{
			ServiceID:      1,
			FieldName:      "max_duration",
			AttributeKey:   offeringpb.AttributeKey_MAX_DURATION,
			AttributeValue: "120",
		},
	}

	result, err = processor.FromModel(attributes)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Nil(t, result.Duration) // 空字符串应该被跳过
	assert.Equal(t, int32(120), *result.MaxDuration)
}

func TestStaffProcessor_ToModel(t *testing.T) {
	processor := &StaffProcessor{}

	// 测试适用于所有员工
	attributes := &offeringpb.ServiceAttributes{
		AvailableStaff: &offeringpb.AvailableStaff{
			IsAll:    true,
			StaffIds: []int64{},
		},
	}

	result := processor.ToModel(model2.Service{ID: 1, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, attributes)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ServiceID)
	assert.True(t, result.IsAllStaff)
	assert.Empty(t, result.AvailableStaffIds)

	// 测试指定员工
	attributes = &offeringpb.ServiceAttributes{
		AvailableStaff: &offeringpb.AvailableStaff{
			IsAll:    false,
			StaffIds: []int64{1, 2, 3},
		},
	}

	result = processor.ToModel(model2.Service{ID: 2, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, attributes)
	assert.NotNil(t, result)
	assert.Equal(t, int64(2), result.ServiceID)
	assert.False(t, result.IsAllStaff)
	assert.Equal(t, []int64{1, 2, 3}, []int64(result.AvailableStaffIds))

	// 测试 nil 输入
	result = processor.ToModel(model2.Service{ID: 3, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, nil)
	assert.Nil(t, result)

	// 测试空属性
	result = processor.ToModel(model2.Service{ID: 4, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, &offeringpb.ServiceAttributes{})
	assert.Nil(t, result)
}

func TestStaffProcessor_FromModel(t *testing.T) {
	processor := &StaffProcessor{}

	// 测试适用于所有员工
	scope := &model2.ServiceStaffScope{
		ServiceID:         1,
		IsAllStaff:        true,
		AvailableStaffIds: []int64{},
	}

	result := processor.FromModel(scope)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableStaff)
	assert.True(t, result.AvailableStaff.IsAll)
	assert.Empty(t, result.AvailableStaff.StaffIds)

	// 测试指定员工
	scope = &model2.ServiceStaffScope{
		ServiceID:         2,
		IsAllStaff:        false,
		AvailableStaffIds: []int64{1, 2, 3},
	}

	result = processor.FromModel(scope)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableStaff)
	assert.False(t, result.AvailableStaff.IsAll)
	assert.Equal(t, []int64{1, 2, 3}, result.AvailableStaff.StaffIds)

	// 测试 nil 输入
	result = processor.FromModel(nil)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableStaff)
	assert.True(t, result.AvailableStaff.IsAll)
	assert.Empty(t, result.AvailableStaff.StaffIds)
}

func TestAutoRolloverProcessor_ToModel(t *testing.T) {
	processor := &AutoRolloverProcessor{}

	// 测试正常属性
	attributes := &offeringpb.ServiceAttributes{
		AutoRollover: &offeringpb.AutoRollover{
			TargetServiceId: lo.ToPtr(int64(100)),
			AfterMinute:     lo.ToPtr(int32(30)),
		},
	}

	result := processor.ToModel(1, attributes)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ServiceID)
	assert.Equal(t, int64(100), result.TargetServiceID)
	assert.Equal(t, int32(30), result.AfterMinute)

	// 测试 nil 输入
	result = processor.ToModel(2, nil)
	assert.Nil(t, result)

	// 测试空属性
	result = processor.ToModel(3, &offeringpb.ServiceAttributes{})
	assert.Nil(t, result)
}

func TestAutoRolloverProcessor_FromModel(t *testing.T) {
	processor := &AutoRolloverProcessor{}

	// 测试正常属性
	rollover := &model2.ServiceAutoRollover{
		ServiceID:       1,
		TargetServiceID: 100,
		AfterMinute:     30,
	}

	result := processor.FromModel(rollover)
	assert.NotNil(t, result)
	assert.NotNil(t, result.GetAutoRollover())
	assert.Equal(t, int64(100), result.GetAutoRollover().GetTargetServiceId())
	assert.Equal(t, int32(30), result.GetAutoRollover().GetAfterMinute())

	// 测试 nil 输入
	result = processor.FromModel(nil)
	assert.NotNil(t, result)
	assert.Nil(t, result.AutoRollover)
}

func TestLodgingProcessor_ToModel(t *testing.T) {
	processor := &LodgingProcessor{}

	// 测试适用于所有住宿类型
	attributes := &offeringpb.ServiceAttributes{
		AvailableLodgingType: &offeringpb.AvailableLodgingType{
			IsAll:          true,
			LodgingTypeIds: []int64{},
		},
	}

	result := processor.ToModel(model2.Service{ID: 1, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, attributes)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ServiceID)
	assert.True(t, result.IsAllLodging)
	assert.Empty(t, []int64(result.AvailableLodgingTypeIds))

	// 测试指定住宿类型
	attributes = &offeringpb.ServiceAttributes{
		AvailableLodgingType: &offeringpb.AvailableLodgingType{
			IsAll:          false,
			LodgingTypeIds: []int64{1, 2, 3},
		},
	}

	result = processor.ToModel(model2.Service{ID: 2, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, attributes)
	assert.NotNil(t, result)
	assert.Equal(t, int64(2), result.ServiceID)
	assert.False(t, result.IsAllLodging)
	assert.Equal(t, []int64{1, 2, 3}, []int64(result.AvailableLodgingTypeIds))

	// 测试 nil 输入
	result = processor.ToModel(model2.Service{ID: 3, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, nil)
	assert.Nil(t, result)

	// 测试空属性
	result = processor.ToModel(model2.Service{ID: 4, OrganizationType: organizationpb.OrganizationType_COMPANY, OrganizationID: 10}, &offeringpb.ServiceAttributes{})
	assert.Nil(t, result)
}

func TestLodgingProcessor_FromModel(t *testing.T) {
	processor := &LodgingProcessor{}

	// 测试适用于所有住宿类型
	scope := &model2.ServiceLodgingScope{
		ServiceID:               1,
		IsAllLodging:            true,
		AvailableLodgingTypeIds: pq.Int64Array{},
	}

	result := processor.FromModel(scope)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableLodgingType)
	assert.True(t, result.AvailableLodgingType.IsAll)
	assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)

	// 测试指定住宿类型
	scope = &model2.ServiceLodgingScope{
		ServiceID:               2,
		IsAllLodging:            false,
		AvailableLodgingTypeIds: pq.Int64Array{1, 2, 3},
	}

	result = processor.FromModel(scope)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableLodgingType)
	assert.False(t, result.AvailableLodgingType.IsAll)
	assert.Equal(t, []int64{1, 2, 3}, result.AvailableLodgingType.LodgingTypeIds)

	// 测试 nil 输入
	result = processor.FromModel(nil)
	assert.NotNil(t, result)
	assert.NotNil(t, result.AvailableLodgingType)
	assert.True(t, result.AvailableLodgingType.IsAll)
	assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)
}
