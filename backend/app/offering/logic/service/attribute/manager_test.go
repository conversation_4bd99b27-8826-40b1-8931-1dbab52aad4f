package attribute

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	model2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	servicemock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl             *gomock.Controller
	mockServiceRepo  *servicemock.MockRepository
	mockBasicRepo    *mock.MockRepository
	mockRolloverRepo *mock.MockAutoRollOverRepository
	mockLodgingRepo  *mock.MockLodgingScopeRepository
	mockStaffRepo    *mock.MockStaffScopeRepository
	manager          *Manager
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)

	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockBasicRepo := mock.NewMockRepository(ctrl)
	mockRolloverRepo := mock.NewMockAutoRollOverRepository(ctrl)
	mockLodgingRepo := mock.NewMockLodgingScopeRepository(ctrl)
	mockStaffRepo := mock.NewMockStaffScopeRepository(ctrl)

	manager := NewManagerWithRepositories(
		mockServiceRepo, mockBasicRepo, mockRolloverRepo, mockLodgingRepo, mockStaffRepo)

	return &testHelper{
		ctrl:             ctrl,
		mockServiceRepo:  mockServiceRepo,
		mockBasicRepo:    mockBasicRepo,
		mockRolloverRepo: mockRolloverRepo,
		mockLodgingRepo:  mockLodgingRepo,
		mockStaffRepo:    mockStaffRepo,
		manager:          manager,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	if h.ctrl != nil {
		h.ctrl.Finish()
	}
}

// createServiceAttributes 创建测试用的 ServiceAttributes
func createServiceAttributes(duration int32, maxDuration int32) *offeringpb.ServiceAttributes {
	return &offeringpb.ServiceAttributes{
		Duration:    &duration,
		MaxDuration: &maxDuration,
	}
}

// createServiceAttributesWithAutoRollover 创建包含 auto rollover 的 ServiceAttributes
func createServiceAttributesWithAutoRollover(duration int32, targetServiceID int64, afterMinute int32) *offeringpb.ServiceAttributes {
	return &offeringpb.ServiceAttributes{
		Duration: &duration,
		AutoRollover: &offeringpb.AutoRollover{
			TargetServiceId: lo.ToPtr(targetServiceID),
			AfterMinute:     lo.ToPtr(afterMinute),
		},
	}
}

func TestNewManager(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	assert.NotNil(t, helper.manager)
	assert.NotEmpty(t, helper.manager.processors)
}

func TestNewManagerWithRepositories(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	assert.NotNil(t, helper.manager)
	assert.NotEmpty(t, helper.manager.processors)
}

func TestManager_Save(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	mockQuery := &query.Query{}
	// 测试保存服务属性配置
	duration := int32(60)
	attributes := &offeringpb.ServiceAttributes{
		Duration: &duration,
		AutoRollover: &offeringpb.AutoRollover{
			TargetServiceId: lo.ToPtr(int64(2)),
			AfterMinute:     lo.ToPtr(int32(30)),
		},
	}

	// 设置mock期望
	// BasicProcessor 现在使用 upsert 逻辑，需要设置 ListByServiceID 和 Create
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// AutoRolloverProcessor 使用 upsert 逻辑
	helper.mockRolloverRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockRolloverRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockRolloverRepo)
	helper.mockRolloverRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	err := helper.manager.Save(context.Background(), mockQuery, 1, attributes)
	assert.NoError(t, err)
}

func TestManager_Save_ProcessorError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	mockQuery := &query.Query{}
	// 测试保存基础属性时出错
	duration := int32(60)
	attributes := &offeringpb.ServiceAttributes{
		Duration: &duration,
	}

	// 设置mock期望 - 模拟保存失败
	// BasicProcessor 现在使用 upsert 逻辑，需要设置 ListByServiceID 和 Create
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model2.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("save error"))

	err := helper.manager.Save(context.Background(), mockQuery, 1, attributes)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "save error")
}

func TestManager_Save_UnsupportedKey(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	mockQuery := &query.Query{}
	// 测试不支持的属性键 - 现在这个测试不再适用，因为新的接口只处理预定义的字段
	// 这个测试可以删除或者改为测试其他场景
	err := helper.manager.Save(context.Background(), mockQuery, 1, nil)
	assert.NoError(t, err)
}

func TestManager_ListByServiceID(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 设置mock期望 - 基础属性返回空列表，auto_rollover返回空列表
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAttribute{}, nil).AnyTimes()
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAutoRollover{}, nil).AnyTimes()
	helper.mockRolloverRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockRolloverRepo).AnyTimes()
	// 设置 staff 和 lodging processor 的 mock 期望
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()

	attributes, err := helper.manager.ListByServiceID(context.Background(), 1)
	assert.NoError(t, err)
	// 由于基础属性返回空，其他处理器返回默认值，所以结果应该包含默认值
	assert.NotNil(t, attributes)
	assert.NotNil(t, attributes.AvailableLodgingType)
	assert.True(t, attributes.AvailableLodgingType.IsAll)
	assert.NotNil(t, attributes.AvailableStaff)
	assert.True(t, attributes.AvailableStaff.IsAll)
}

func TestManager_ListByServiceID_WithAttributes(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 模拟基础属性返回一些数据
	now := time.Now()
	mockAttributes := []*model2.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
	}

	// 设置mock期望
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockAttributes, nil).AnyTimes()
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAutoRollover{}, nil).AnyTimes()
	// 设置 staff 和 lodging processor 的 mock 期望
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()

	attributes, err := helper.manager.ListByServiceID(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, attributes)
	assert.NotNil(t, attributes.Duration)
	assert.Equal(t, int32(60), *attributes.Duration)
	// 其他处理器返回默认值
	assert.NotNil(t, attributes.AvailableLodgingType)
	assert.True(t, attributes.AvailableLodgingType.IsAll)
	assert.NotNil(t, attributes.AvailableStaff)
	assert.True(t, attributes.AvailableStaff.IsAll)
}

func TestManager_ListByServiceID_ProcessorError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 设置mock期望 - 只有基础属性返回错误，其他处理器正常返回
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, errors.New("list error")).AnyTimes()
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model2.ServiceAutoRollover{}, nil).AnyTimes()
	// 设置 staff 和 lodging processor 的 mock 期望 - 这些应该正常返回
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, nil).AnyTimes()

	attributes, err := helper.manager.ListByServiceID(context.Background(), 1)
	// 由于基础属性处理器返回错误，Manager应该返回错误，阻塞后续processor处理
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to list attributes with processor")
	assert.Contains(t, err.Error(), "list error")
	// 由于错误，结果应该为nil
	assert.Nil(t, attributes)
}

func TestManager_DeleteByServiceID(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	mockQuery := &query.Query{}
	// 设置mock期望
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo)
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(1), nil)
	helper.mockRolloverRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockRolloverRepo)
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil)
	helper.mockStaffRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockStaffRepo)
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil)
	helper.mockLodgingRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockLodgingRepo)
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), nil)

	err := helper.manager.DeleteByServiceID(context.Background(), mockQuery, 1)
	assert.NoError(t, err)
}

func TestManager_DeleteByServiceID_ProcessorError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	mockQuery := &query.Query{}
	// 设置mock期望 - 基础属性删除失败
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo)
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), gomock.Any()).Return(int64(0), errors.New("delete error"))

	err := helper.manager.DeleteByServiceID(context.Background(), mockQuery, 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete error")
}

func TestManager_GetProcessor(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 验证所有处理器都已注册
	assert.Len(t, helper.manager.processors, 4)

	// 验证处理器类型
	var basicProcessor, autoRolloverProcessor, staffProcessor, lodgingProcessor Processor
	for _, processor := range helper.manager.processors {
		switch processor.(type) {
		case *BasicProcessor:
			basicProcessor = processor
		case *AutoRolloverProcessor:
			autoRolloverProcessor = processor
		case *StaffProcessor:
			staffProcessor = processor
		case *LodgingProcessor:
			lodgingProcessor = processor
		}
	}

	// 验证所有处理器都已找到
	assert.NotNil(t, basicProcessor, "BasicProcessor should be registered")
	assert.NotNil(t, autoRolloverProcessor, "AutoRolloverProcessor should be registered")
	assert.NotNil(t, staffProcessor, "StaffProcessor should be registered")
	assert.NotNil(t, lodgingProcessor, "LodgingProcessor should be registered")
}

func TestManager_SupportedKeys(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 获取所有支持的键
	allKeys := make(map[offeringpb.AttributeKey]bool)
	for _, processor := range helper.manager.processors {
		for _, key := range processor.SupportedKeys() {
			allKeys[key] = true
		}
	}

	// 验证基础属性键
	assert.True(t, allKeys[offeringpb.AttributeKey_DURATION])
	assert.True(t, allKeys[offeringpb.AttributeKey_MAX_DURATION])
	assert.True(t, allKeys[offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE])
	assert.True(t, allKeys[offeringpb.AttributeKey_AVAILABLE_STAFF])

	// 验证auto_rollover键
	assert.True(t, allKeys[offeringpb.AttributeKey_AUTO_ROLLOVER])
}

func TestManager_WithStaffAndLodgingProcessors(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 验证所有处理器都已注册
	assert.Len(t, helper.manager.processors, 4)

	// 查找不同类型的处理器
	var basicProcessor, staffProcessor, lodgingProcessor Processor
	for _, processor := range helper.manager.processors {
		switch processor.(type) {
		case *BasicProcessor:
			basicProcessor = processor
		case *StaffProcessor:
			staffProcessor = processor
		case *LodgingProcessor:
			lodgingProcessor = processor
		}
	}

	// 验证所有处理器都已找到
	assert.NotNil(t, basicProcessor, "BasicProcessor should be registered")
	assert.NotNil(t, staffProcessor, "StaffProcessor should be registered")
	assert.NotNil(t, lodgingProcessor, "LodgingProcessor should be registered")

	// 验证 staff 和 lodging processor 是不同的实例
	assert.NotEqual(t, staffProcessor, lodgingProcessor)

	// 验证基础处理器支持基础属性键
	basicKeys := basicProcessor.SupportedKeys()
	assert.Contains(t, basicKeys, offeringpb.AttributeKey_DURATION)
	assert.Contains(t, basicKeys, offeringpb.AttributeKey_MAX_DURATION)

	// 验证 staff 和 lodging 处理器支持各自的属性键
	staffKeys := staffProcessor.SupportedKeys()
	assert.Contains(t, staffKeys, offeringpb.AttributeKey_AVAILABLE_STAFF)

	lodgingKeys := lodgingProcessor.SupportedKeys()
	assert.Contains(t, lodgingKeys, offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE)
}

// TestManager_mergeServiceAttributes 测试合并功能
func TestManager_mergeServiceAttributes(t *testing.T) {
	manager := &Manager{}

	t.Run("合并基础字段", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{}
		source := &offeringpb.ServiceAttributes{
			Duration:    int32Ptr(30),
			MaxDuration: int32Ptr(60),
		}

		err := manager.mergeServiceAttributes(target, source)
		assert.NoError(t, err)
		assert.Equal(t, int32(30), *target.Duration)
		assert.Equal(t, int32(60), *target.MaxDuration)
	})

	t.Run("合并指针字段", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{
			Duration: int32Ptr(20),
		}
		source := &offeringpb.ServiceAttributes{
			MaxDuration: int32Ptr(90),
		}

		err := manager.mergeServiceAttributes(target, source)
		assert.NoError(t, err)
		assert.Equal(t, int32(20), *target.Duration)    // 保持原值
		assert.Equal(t, int32(90), *target.MaxDuration) // 被覆盖
	})

	t.Run("合并布尔字段", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{}
		source := &offeringpb.ServiceAttributes{
			IsRequiredStaff: boolPtr(true),
		}

		err := manager.mergeServiceAttributes(target, source)
		assert.NoError(t, err)
		assert.Equal(t, true, *target.IsRequiredStaff)
	})

	t.Run("合并字符串字段", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{}
		source := &offeringpb.ServiceAttributes{
			ObAlias: stringPtr("test_alias"),
		}

		err := manager.mergeServiceAttributes(target, source)
		assert.NoError(t, err)
		assert.Equal(t, "test_alias", *target.ObAlias)
	})

	t.Run("空值不覆盖", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{
			Duration: int32Ptr(30),
		}
		source := &offeringpb.ServiceAttributes{
			Duration: nil, // 空值
		}

		err := manager.mergeServiceAttributes(target, source)
		assert.NoError(t, err)
		assert.Equal(t, int32(30), *target.Duration) // 保持原值
	})

	t.Run("nil 参数处理", func(t *testing.T) {
		target := &offeringpb.ServiceAttributes{}

		// source 为 nil
		err := manager.mergeServiceAttributes(target, nil)
		assert.NoError(t, err)

		// target 为 nil
		err = manager.mergeServiceAttributes(nil, &offeringpb.ServiceAttributes{})
		assert.NoError(t, err)
	})
}

// 辅助函数
func int32Ptr(i int32) *int32 {
	return &i
}

func boolPtr(b bool) *bool {
	return &b
}

func stringPtr(s string) *string {
	return &s
}
