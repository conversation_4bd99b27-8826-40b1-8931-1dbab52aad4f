package attribute

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	attrmock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestAutoRolloverProcessor_Save(t *testing.T) {
	t.Run("保存自动续期配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(2)),
				AfterMinute:     lo.ToPtr(int32(30)),
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, rollover *model.ServiceAutoRollover) error {
				assert.Equal(t, int64(1), rollover.ServiceID)
				assert.Equal(t, int64(2), rollover.TargetServiceID)
				assert.Equal(t, int32(30), rollover.AfterMinute)
				return nil
			})

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.NoError(t, err)
	})

	t.Run("更新现有自动续期配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(3)),
				AfterMinute:     lo.ToPtr(int32(45)),
			},
		}

		existingRollover := &model.ServiceAutoRollover{
			ID:              1,
			ServiceID:       1,
			TargetServiceID: 2,
			AfterMinute:     30,
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(existingRollover, nil)
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, rollover *model.ServiceAutoRollover) error {
				assert.Equal(t, int64(1), rollover.ID)
				assert.Equal(t, int64(3), rollover.TargetServiceID)
				assert.Equal(t, int32(45), rollover.AfterMinute)
				return nil
			})

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.NoError(t, err)
	})

	t.Run("没有自动续期配置时直接返回", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{}

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.NoError(t, err)
	})

	t.Run("nil 属性时直接返回", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		err := processor.Save(context.Background(), mockQuery, 1, nil)
		assert.NoError(t, err)
	})

	t.Run("创建记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(2)),
				AfterMinute:     lo.ToPtr(int32(30)),
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "create error")
	})

	t.Run("查询现有记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(2)),
				AfterMinute:     lo.ToPtr(int32(30)),
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, errors.New("query error"))

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "query error")
	})

	t.Run("更新记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(3)),
				AfterMinute:     lo.ToPtr(int32(45)),
			},
		}

		existingRollover := &model.ServiceAutoRollover{
			ID:              1,
			ServiceID:       1,
			TargetServiceID: 2,
			AfterMinute:     30,
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(existingRollover, nil)
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

		err := processor.Save(context.Background(), mockQuery, 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "update error")
	})
}

func TestAutoRolloverProcessor_Remove(t *testing.T) {
	t.Run("删除支持的属性键", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil)

		err := processor.Remove(context.Background(), mockQuery, 1, offeringpb.AttributeKey_AUTO_ROLLOVER)
		assert.NoError(t, err)
	})

	t.Run("删除不支持的属性键时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
		err := processor.Remove(context.Background(), mockQuery, 1, offeringpb.AttributeKey_DURATION)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported attribute key for auto rollover processor")
	})

	t.Run("删除失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), errors.New("delete error"))

		err := processor.Remove(context.Background(), mockQuery, 1, offeringpb.AttributeKey_AUTO_ROLLOVER)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "delete error")
	})
}

func TestAutoRolloverProcessor_RemoveByServiceID(t *testing.T) {
	t.Run("删除指定服务的所有自动续期配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil)

		err := processor.RemoveByServiceID(context.Background(), mockQuery, 1)
		assert.NoError(t, err)
	})

	t.Run("删除失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		mockQuery := &query.Query{}
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo)
		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), errors.New("delete error"))

		err := processor.RemoveByServiceID(context.Background(), mockQuery, 1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "delete error")
	})
}

func TestAutoRolloverProcessor_SupportedKeys(t *testing.T) {
	t.Run("返回支持的属性键列表", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		keys := processor.SupportedKeys()
		assert.Len(t, keys, 1)
		assert.Contains(t, keys, offeringpb.AttributeKey_AUTO_ROLLOVER)
	})
}

func TestAutoRolloverProcessor_IsSupportedKey(t *testing.T) {
	t.Run("检查支持的属性键", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		assert.True(t, processor.IsSupportedKey(offeringpb.AttributeKey_AUTO_ROLLOVER))

		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_DURATION))
		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_MAX_DURATION))
		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_AVAILABLE_STAFF))
	})
}

func TestAutoRolloverProcessor_Register(t *testing.T) {
	t.Run("向管理器注册处理器", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		manager := NewManagerWithRepositories(
			nil,      // serviceRepo
			nil,      // basicRepo
			mockRepo, // autoRolloverRepo
			nil,      // lodgingScopeRepo
			nil,      // staffScopeRepo
		)

		assert.NotPanics(t, func() {
			processor.Register(manager)
		})
	})
}

func TestAutoRolloverProcessor_Constructor(t *testing.T) {
	t.Run("创建带有自定义 repository 的处理器", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		assert.NotNil(t, processor)
		assert.Equal(t, mockRepo, processor.repo)
	})
}

func TestAutoRolloverProcessor_Converter(t *testing.T) {
	t.Run("ToModel 转换", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		// 测试 nil 属性
		result := processor.ToModel(1, nil)
		assert.Nil(t, result)

		// 测试没有 AutoRollover 的属性
		attr := &offeringpb.ServiceAttributes{}
		result = processor.ToModel(1, attr)
		assert.Nil(t, result)

		// 测试正常的自动续期配置
		attr = &offeringpb.ServiceAttributes{
			AutoRollover: &offeringpb.AutoRollover{
				TargetServiceId: lo.ToPtr(int64(2)),
				AfterMinute:     lo.ToPtr(int32(30)),
			},
		}
		result = processor.ToModel(1, attr)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.ServiceID)
		assert.Equal(t, int64(2), result.TargetServiceID)
		assert.Equal(t, int32(30), result.AfterMinute)
	})

	t.Run("FromModel 转换", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		// 测试 nil rollover
		result := processor.FromModel(nil)
		assert.NotNil(t, result)
		assert.Nil(t, result.AutoRollover)

		// 测试正常的 rollover 配置
		rollover := &model.ServiceAutoRollover{
			ID:              1,
			ServiceID:       1,
			TargetServiceID: 2,
			AfterMinute:     30,
		}
		result = processor.FromModel(rollover)
		assert.NotNil(t, result)
		assert.NotNil(t, result.GetAutoRollover())
		assert.Equal(t, int64(2), result.GetAutoRollover().GetTargetServiceId())
		assert.Equal(t, int32(30), result.GetAutoRollover().GetAfterMinute())
	})
}

func TestAutoRolloverProcessor_ListByServiceID(t *testing.T) {
	t.Run("获取单个服务的自动续期配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRollover := &model.ServiceAutoRollover{
			ID:              1,
			ServiceID:       1,
			TargetServiceID: 2,
			AfterMinute:     30,
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceAutoRollover{mockRollover}, nil)

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.NotNil(t, result.GetAutoRollover())
		assert.Equal(t, int64(2), result.GetAutoRollover().GetTargetServiceId())
		assert.Equal(t, int32(30), result.GetAutoRollover().GetAfterMinute())
	})

	t.Run("服务没有自动续期配置时返回空配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceAutoRollover{}, nil)

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Nil(t, result.GetAutoRollover())
	})

	t.Run("查询失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, errors.New("query error"))

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "query error")
	})
}

func TestAutoRolloverProcessor_ListByServiceIDs(t *testing.T) {
	t.Run("批量获取多个服务的自动续期配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		now := time.Now()
		mockRollovers := []*model.ServiceAutoRollover{
			{
				ID:              1,
				ServiceID:       1,
				TargetServiceID: 2,
				AfterMinute:     30,
				CreateTime:      &now,
				UpdateTime:      &now,
			},
			{
				ID:              2,
				ServiceID:       2,
				TargetServiceID: 3,
				AfterMinute:     45,
				CreateTime:      &now,
				UpdateTime:      &now,
			},
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockRollovers, nil)

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)

		assert.NotNil(t, result[1].GetAutoRollover())
		assert.Equal(t, int64(2), result[1].GetAutoRollover().GetTargetServiceId())
		assert.Equal(t, int32(30), result[1].GetAutoRollover().GetAfterMinute())

		assert.NotNil(t, result[2].GetAutoRollover())
		assert.Equal(t, int64(3), result[2].GetAutoRollover().GetTargetServiceId())
		assert.Equal(t, int32(45), result[2].GetAutoRollover().GetAfterMinute())
	})

	t.Run("部分服务没有配置时返回默认值", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		now := time.Now()
		mockRollovers := []*model.ServiceAutoRollover{
			{
				ID:              1,
				ServiceID:       1,
				TargetServiceID: 2,
				AfterMinute:     30,
				CreateTime:      &now,
				UpdateTime:      &now,
			},
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockRollovers, nil)

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)

		assert.NotNil(t, result[1].GetAutoRollover())
		assert.Equal(t, int64(2), result[1].GetAutoRollover().GetTargetServiceId())
		assert.Equal(t, int32(30), result[1].GetAutoRollover().GetAfterMinute())

		assert.Nil(t, result[2].GetAutoRollover())
	})

	t.Run("查询失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockAutoRollOverRepository(ctrl)
		processor := NewAutoRolloverProcessorWithRepository(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(nil, errors.New("query error"))

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "query error")
	})
}
