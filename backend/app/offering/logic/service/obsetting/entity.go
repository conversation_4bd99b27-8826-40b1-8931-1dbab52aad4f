package obsetting

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ServiceOBSetting represents a service online booking setting entity
type ServiceOBSetting struct {
	ID            int64
	BusinessID    int64
	ServiceID     int64
	IsAvailable   bool
	ShowBasePrice *offeringpb.ServiceOBSetting_ShowBasePriceMode
	IsAllStaff    bool
	StaffIDs      []int64
	CreateTime    time.Time
	UpdateTime    time.Time
	ShowDuration  *offeringpb.ServiceOBSetting_ShowDuration
}
