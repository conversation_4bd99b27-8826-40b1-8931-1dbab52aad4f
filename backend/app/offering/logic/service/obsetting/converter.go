package obsetting

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// EntityToModel converts a domain entity to a model
func EntityToModel(e *ServiceOBSetting) *model.ServiceObSetting {
	if e == nil {
		return nil
	}

	return &model.ServiceObSetting{
		ID:            e.ID,
		BusinessID:    e.BusinessID,
		ServiceID:     e.ServiceID,
		IsAvailable:   e.IsAvailable,
		ShowBasePrice: e.ShowBasePrice,
		IsAllStaff:    e.IsAllStaff,
		CreateTime:    &e.CreateTime,
		UpdateTime:    &e.UpdateTime,
		ShowDuration:  e.ShowDuration,
	}
}

// ModelToEntity converts a model to a domain entity
func ModelToEntity(m *model.ServiceObSetting) *ServiceOBSetting {
	if m == nil {
		return nil
	}

	return &ServiceOBSetting{
		ID:            m.ID,
		BusinessID:    m.BusinessID,
		ServiceID:     m.ServiceID,
		IsAvailable:   m.IsAvailable,
		ShowBasePrice: m.ShowBasePrice,
		IsAllStaff:    m.IsAllStaff,
		CreateTime:    *m.CreateTime,
		UpdateTime:    *m.UpdateTime,
		ShowDuration:  m.ShowDuration,
	}
}

// ModelToEntityList converts a list of models to domain entities
func ModelToEntityList(ms []*model.ServiceObSetting) []*ServiceOBSetting {
	if ms == nil {
		return nil
	}

	entities := make([]*ServiceOBSetting, 0, len(ms))
	for _, m := range ms {
		entities = append(entities, ModelToEntity(m))
	}

	return entities
}

// EntityToProto converts a domain entity to a protobuf message
func EntityToProto(e *ServiceOBSetting) *offeringpb.ServiceOBSetting {
	if e == nil {
		return nil
	}

	proto := &offeringpb.ServiceOBSetting{
		ServiceId:   e.ServiceID,
		IsAvailable: e.IsAvailable,
		IsAllStaff:  e.IsAllStaff,
		StaffIds:    e.StaffIDs,
	}

	if e.ShowBasePrice != nil {
		proto.ShowBasePrice = *e.ShowBasePrice
	}
	if e.ShowDuration != nil {
		proto.ShowDuration = *e.ShowDuration
	}

	return proto
}

// EntityToProtoList converts a list of domain entities to protobuf messages
func EntityToProtoList(es []*ServiceOBSetting) []*offeringpb.ServiceOBSetting {
	if es == nil {
		return nil
	}

	protos := make([]*offeringpb.ServiceOBSetting, 0, len(es))
	for _, e := range es {
		protos = append(protos, EntityToProto(e))
	}

	return protos
}

// UpdateOBServiceRequestToEntity converts UpdateOBServiceRequest to ServiceOBSetting entity
// This method handles all the optional field logic and sets default values appropriately
func UpdateOBServiceRequestToEntity(req *offeringpb.UpdateOBServiceRequest) *ServiceOBSetting {
	if req == nil {
		return nil
	}

	// 构建基础实体，包含必需字段
	entity := &ServiceOBSetting{
		BusinessID: req.GetBusinessId(),
		ServiceID:  req.GetServiceId(),
	}

	// 设置可选字段，如果请求中有值则使用，否则使用默认值
	if req.IsAvailable != nil {
		entity.IsAvailable = *req.IsAvailable
	}
	if req.ShowBasePrice != nil {
		showBasePrice := req.GetShowBasePrice()
		entity.ShowBasePrice = &showBasePrice
	}
	if req.IsAllStaff != nil {
		entity.IsAllStaff = *req.IsAllStaff
	}
	// 处理 staff_ids 字段
	if req.StaffIds != nil {
		entity.StaffIDs = req.StaffIds
	}
	if req.ShowDuration != nil {
		showDuration := req.GetShowDuration()
		entity.ShowDuration = &showDuration
	}

	return entity
}
