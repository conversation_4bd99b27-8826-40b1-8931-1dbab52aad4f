package obsetting

import (
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// TestEntityToModel 测试EntityToModel转换函数
func TestEntityToModel(t *testing.T) {
	now := time.Now()
	e := &ServiceOBSetting{
		ID:            1,
		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    true,
		StaffIDs:      []int64{1, 2, 3},
		CreateTime:    now,
		UpdateTime:    now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
	}

	m := EntityToModel(e)
	assert.NotNil(t, m)
	assert.Equal(t, int64(1), m.ID)
	assert.Equal(t, int64(123), m.BusinessID)
	assert.Equal(t, int64(1), m.ServiceID)
	assert.True(t, m.IsAvailable)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE, m.ShowBasePrice)
	assert.True(t, m.IsAllStaff)
	assert.Equal(t, &now, m.CreateTime)
	assert.Equal(t, &now, m.UpdateTime)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION, m.ShowDuration)
}

// TestEntityToModel_Nil 测试EntityToModel处理nil输入
func TestEntityToModel_Nil(t *testing.T) {
	m := EntityToModel(nil)
	assert.Nil(t, m)
}

// TestModelToEntity 测试ModelToEntity转换函数
func TestModelToEntity(t *testing.T) {
	now := time.Now()
	m := &model.ServiceObSetting{
		ID:            1,
		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: 1, // SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE
		IsAllStaff:    true,
		CreateTime:    &now,
		UpdateTime:    &now,
		ShowDuration:  1, // SHOW_DURATION
	}

	e := ModelToEntity(m)
	assert.NotNil(t, e)
	assert.Equal(t, int64(1), e.ID)
	assert.Equal(t, int64(123), e.BusinessID)
	assert.Equal(t, int64(1), e.ServiceID)
	assert.True(t, e.IsAvailable)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE, e.ShowBasePrice)
	assert.True(t, e.IsAllStaff)
	assert.Equal(t, now, e.CreateTime)
	assert.Equal(t, now, e.UpdateTime)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION, e.ShowDuration)
}

// TestModelToEntity_Nil 测试ModelToEntity处理nil输入
func TestModelToEntity_Nil(t *testing.T) {
	e := ModelToEntity(nil)
	assert.Nil(t, e)
}

// TestModelToEntityList 测试ModelToEntityList转换函数
func TestModelToEntityList(t *testing.T) {
	now := time.Now()
	models := []*model.ServiceObSetting{
		{
			ID:            1,
			BusinessID:    123,
			ServiceID:     1,
			IsAvailable:   true,
			ShowBasePrice: 2,
			IsAllStaff:    true,
			CreateTime:    &now,
			UpdateTime:    &now,
			ShowDuration:  1,
		},
		{
			ID:            2,
			BusinessID:    456,
			ServiceID:     2,
			IsAvailable:   false,
			ShowBasePrice: 1,
			IsAllStaff:    false,
			CreateTime:    &now,
			UpdateTime:    &now,
			ShowDuration:  0,
		},
	}

	entities := ModelToEntityList(models)
	assert.NotNil(t, entities)
	assert.Len(t, entities, 2)
	assert.Equal(t, int64(1), entities[0].ID)
	assert.Equal(t, int64(2), entities[1].ID)
}

// TestModelToEntityList_Nil 测试ModelToEntityList处理nil输入
func TestModelToEntityList_Nil(t *testing.T) {
	entities := ModelToEntityList(nil)
	assert.Nil(t, entities)
}

// TestModelToEntityList_Empty 测试ModelToEntityList处理空切片
func TestModelToEntityList_Empty(t *testing.T) {
	entities := ModelToEntityList([]*model.ServiceObSetting{})
	assert.NotNil(t, entities)
	assert.Len(t, entities, 0)
}

// TestEntityToProto 测试EntityToProto转换函数
func TestEntityToProto(t *testing.T) {
	now := time.Now()
	e := &ServiceOBSetting{
		ID:            1,
		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    true,
		CreateTime:    now,
		UpdateTime:    now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
	}

	proto := EntityToProto(e)
	assert.NotNil(t, proto)
	assert.Equal(t, int64(1), proto.ServiceId)
	assert.True(t, proto.IsAvailable)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE, proto.ShowBasePrice)
	assert.True(t, proto.IsAllStaff)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION, proto.ShowDuration)
}

// TestEntityToProto_Nil 测试EntityToProto处理nil输入
func TestEntityToProto_Nil(t *testing.T) {
	proto := EntityToProto(nil)
	assert.Nil(t, proto)
}

// TestEntityToProtoList 测试EntityToProtoList转换函数
func TestEntityToProtoList(t *testing.T) {
	now := time.Now()
	entities := []*ServiceOBSetting{
		{
			ID:            1,
			BusinessID:    123,
			ServiceID:     1,
			IsAvailable:   true,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
			IsAllStaff:    true,
			CreateTime:    now,
			UpdateTime:    now,
			ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
		},
		{
			ID:            2,
			BusinessID:    456,
			ServiceID:     2,
			IsAvailable:   false,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW,
			IsAllStaff:    false,
			CreateTime:    now,
			UpdateTime:    now,
			ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION_NO,
		},
	}

	protos := EntityToProtoList(entities)
	assert.NotNil(t, protos)
	assert.Len(t, protos, 2)
}

// TestEntityToProtoList_Nil 测试EntityToProtoList处理nil输入
func TestEntityToProtoList_Nil(t *testing.T) {
	protos := EntityToProtoList(nil)
	assert.Nil(t, protos)
}

// TestEntityToProtoList_Empty 测试EntityToProtoList处理空切片
func TestEntityToProtoList_Empty(t *testing.T) {
	protos := EntityToProtoList([]*ServiceOBSetting{})
	assert.NotNil(t, protos)
	assert.Len(t, protos, 0)
}

// TestUpdateOBServiceRequestToEntity 测试UpdateOBServiceRequestToEntity转换函数
func TestUpdateOBServiceRequestToEntity(t *testing.T) {
	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId:    123,
		ServiceId:     1,
		IsAvailable:   lo.ToPtr(true),
		ShowBasePrice: lo.ToPtr(offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE),
		IsAllStaff:    lo.ToPtr(true),
		StaffIds:      []int64{1, 2, 3},
		ShowDuration:  lo.ToPtr(offeringpb.ServiceOBSetting_SHOW_DURATION),
	}

	entity := UpdateOBServiceRequestToEntity(req)
	assert.NotNil(t, entity)
	assert.Equal(t, int64(123), entity.BusinessID)
	assert.Equal(t, int64(1), entity.ServiceID)
	assert.True(t, entity.IsAvailable)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE, entity.ShowBasePrice)
	assert.True(t, entity.IsAllStaff)
	assert.Equal(t, []int64{1, 2, 3}, entity.StaffIDs)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION, entity.ShowDuration)
}

// TestUpdateOBServiceRequestToEntity_PartialFields 测试部分字段的转换
func TestUpdateOBServiceRequestToEntity_PartialFields(t *testing.T) {
	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId: 456,
		ServiceId:  2,
		// 只设置部分字段，其他字段使用默认值
		IsAvailable: lo.ToPtr(false),
	}

	entity := UpdateOBServiceRequestToEntity(req)
	assert.NotNil(t, entity)
	assert.Equal(t, int64(456), entity.BusinessID)
	assert.Equal(t, int64(2), entity.ServiceID)
	assert.False(t, entity.IsAvailable)
	// 其他字段应该使用默认值
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW, entity.ShowBasePrice)
	assert.False(t, entity.IsAllStaff)
	assert.Nil(t, entity.StaffIDs)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION_NO, entity.ShowDuration)
}

// TestUpdateOBServiceRequestToEntity_NilRequest 测试处理nil请求
func TestUpdateOBServiceRequestToEntity_NilRequest(t *testing.T) {
	entity := UpdateOBServiceRequestToEntity(nil)
	assert.Nil(t, entity)
}

// TestUpdateOBServiceRequestToEntity_ShowDurationOnly 测试只设置ShowDuration字段的转换
func TestUpdateOBServiceRequestToEntity_ShowDurationOnly(t *testing.T) {
	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId:   789,
		ServiceId:    3,
		ShowDuration: lo.ToPtr(offeringpb.ServiceOBSetting_SHOW_DURATION_NO),
	}

	entity := UpdateOBServiceRequestToEntity(req)
	assert.NotNil(t, entity)
	assert.Equal(t, int64(789), entity.BusinessID)
	assert.Equal(t, int64(3), entity.ServiceID)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION_NO, entity.ShowDuration)
	// 其他字段应该使用默认值
	assert.False(t, entity.IsAvailable)
	assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW, entity.ShowBasePrice)
	assert.False(t, entity.IsAllStaff)
	assert.Nil(t, entity.StaffIDs)
}

// TestUpdateOBServiceRequestToEntity_EmptyStaffIds 测试空的staff_ids处理
func TestUpdateOBServiceRequestToEntity_EmptyStaffIds(t *testing.T) {
	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId: 123,
		ServiceId:  1,
		StaffIds:   []int64{}, // 空的staff_ids
	}

	entity := UpdateOBServiceRequestToEntity(req)
	assert.NotNil(t, entity)
	assert.Equal(t, []int64{}, entity.StaffIDs)
}
