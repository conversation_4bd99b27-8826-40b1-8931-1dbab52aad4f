package obsetting

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	organizationmodelpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	servicemock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting/mocks"
	organizationmock "github.com/MoeGolibrary/moego/backend/app/offering/repo/organization/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestLogic_ListByBusinessID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	now := time.Now()
	mockModels := []*model.ServiceObSetting{
		{
			ID:            1,
			BusinessID:    123,
			ServiceID:     1,
			IsAvailable:   true,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
			IsAllStaff:    true,
			CreateTime:    &now,
			UpdateTime:    &now,
		},
		{
			ID:            2,
			BusinessID:    123,
			ServiceID:     2,
			IsAvailable:   false,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW,
			IsAllStaff:    false,
			CreateTime:    &now,
			UpdateTime:    &now,
		},
	}

	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  50,
	}

	mockRepo.EXPECT().ListWithPagination(gomock.Any(), gomock.Any(), int32(0), int32(50)).Return(mockModels, int64(2), nil)
	mockRepo.EXPECT().GetStaffBinding(gomock.Any(), int64(1)).Return([]int64{1, 2}, nil)
	mockRepo.EXPECT().GetStaffBinding(gomock.Any(), int64(2)).Return([]int64{3, 4}, nil)

	result, total, err := logic.ListByBusinessID(context.Background(), 123, nil, pagination)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 2)
	assert.Equal(t, int64(2), total)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, int64(123), result[0].BusinessID)
	assert.Equal(t, int64(1), result[0].ServiceID)
	assert.True(t, result[0].IsAvailable)
	assert.Equal(t, []int64{1, 2}, result[0].StaffIDs)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, int64(123), result[1].BusinessID)
	assert.Equal(t, int64(2), result[1].ServiceID)
	assert.False(t, result[1].IsAvailable)
	assert.Equal(t, []int64{3, 4}, result[1].StaffIDs)
}

func TestLogic_ListByBusinessID_NoPagination(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	now := time.Now()
	mockModels := []*model.ServiceObSetting{
		{
			ID:            1,
			BusinessID:    123,
			ServiceID:     1,
			IsAvailable:   true,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
			IsAllStaff:    true,
			CreateTime:    &now,
			UpdateTime:    &now,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any()).Return(mockModels, nil)
	mockRepo.EXPECT().GetStaffBinding(gomock.Any(), int64(1)).Return([]int64{1, 2}, nil)

	result, total, err := logic.ListByBusinessID(context.Background(), 123, nil, nil)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), total)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, int64(123), result[0].BusinessID)
	assert.Equal(t, int64(1), result[0].ServiceID)
	assert.True(t, result[0].IsAvailable)
	assert.Equal(t, []int64{1, 2}, result[0].StaffIDs)
}

func TestLogic_ListByBusinessID_WithServiceFilter(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	now := time.Now()
	mockModels := []*model.ServiceObSetting{
		{
			ID:            1,
			BusinessID:    123,
			ServiceID:     1,
			IsAvailable:   true,
			ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
			IsAllStaff:    true,
			CreateTime:    &now,
			UpdateTime:    &now,
			ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
		},
	}

	serviceIDs := []int64{1, 2}
	pagination := &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  50,
	}

	// 验证传递的 filter 包含正确的 ServiceIDs
	mockRepo.EXPECT().ListWithPagination(gomock.Any(),
		gomock.Any(), // 这里会匹配包含 ServiceIDs 的 filter
		int32(0), int32(50)).Return(mockModels, int64(1), nil)
	mockRepo.EXPECT().GetStaffBinding(gomock.Any(), int64(1)).Return([]int64{1, 2}, nil)

	result, total, err := logic.ListByBusinessID(context.Background(), 123, serviceIDs, pagination)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), total)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, int64(123), result[0].BusinessID)
	assert.Equal(t, int64(1), result[0].ServiceID)
	assert.True(t, result[0].IsAvailable)
	assert.Equal(t, []int64{1, 2}, result[0].StaffIDs)
}

func TestLogic_InitByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	serviceID := int64(1)
	now := time.Now()

	// Mock service
	serviceModel := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   100,
		Name:             "Test Service",
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(mockServiceRepo)
	mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(serviceModel, nil)
	mockOrganizationRepo.EXPECT().ListBusinessesByCompanyId(gomock.Any(), int64(100)).Return([]*organizationmodelpb.LocationModel{
		{Id: 1}, {Id: 2}, {Id: 3},
	}, nil)

	// Mock checking existing settings (none exist)
	for _, businessID := range []int64{1, 2, 3} {
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
		mockRepo.EXPECT().List(gomock.Any(), &serviceobsetting.ListServiceOBSettingFilter{
			BusinessID: businessID,
			ServiceIDs: []int64{serviceID},
		}).Return([]*model.ServiceObSetting{}, nil)

		// Mock creating new setting
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
	}

	// 创建一个 mock query 用于测试
	mockQuery := &query.Query{}
	err := logic.InitByServiceID(context.Background(), mockQuery, serviceID)
	assert.NoError(t, err)
}

func TestLogic_InitByBusinessID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	businessID := int64(1)
	companyID := int64(100)
	now := time.Now()

	// Mock services
	services := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   companyID,
			Name:             "Service 1",
			Type:             offeringpb.Service_SERVICE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   companyID,
			Name:             "Service 2",
			Type:             offeringpb.Service_SERVICE,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock checking existing settings and creating new ones for each service
	for _, service := range services {
		// Mock checking existing settings (none exist)
		mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
		mockRepo.EXPECT().List(gomock.Any(), &serviceobsetting.ListServiceOBSettingFilter{
			BusinessID: businessID,
			ServiceIDs: []int64{service.ID},
		}).Return([]*model.ServiceObSetting{}, nil)

		// Mock creating new setting
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
	}

	// 创建一个 mock query 用于测试
	mockQuery := &query.Query{}
	err := logic.InitByBusinessID(context.Background(), mockQuery, companyID, businessID)
	assert.NoError(t, err)
}

func TestLogic_CreateDefaultOBSetting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	businessID := int64(123)
	serviceID := int64(456)

	// Mock checking existing settings (none exist)
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
	mockRepo.EXPECT().List(gomock.Any(), &serviceobsetting.ListServiceOBSettingFilter{
		BusinessID: businessID,
		ServiceIDs: []int64{serviceID},
	}).Return([]*model.ServiceObSetting{}, nil)

	// Mock creating new setting
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, setting *model.ServiceObSetting) error {
		// 验证默认值
		assert.Equal(t, businessID, setting.BusinessID)
		assert.Equal(t, serviceID, setting.ServiceID)
		assert.True(t, setting.IsAvailable)
		assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW, setting.ShowBasePrice)
		assert.True(t, setting.IsAllStaff)
		assert.Equal(t, offeringpb.ServiceOBSetting_SHOW_DURATION, setting.ShowDuration)
		return nil
	})

	// 创建一个 mock query 用于测试
	mockQuery := &query.Query{}
	err := logic.createDefaultOBSetting(context.Background(), mockQuery, businessID, serviceID)
	assert.NoError(t, err)
}

func TestLogic_CreateDefaultOBSetting_AlreadyExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)

	businessID := int64(123)
	serviceID := int64(456)
	now := time.Now()

	// Mock checking existing settings (already exists)
	existingSetting := &model.ServiceObSetting{
		ID:            1,
		BusinessID:    businessID,
		ServiceID:     serviceID,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    false,
		CreateTime:    &now,
		UpdateTime:    &now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION_NO,
	}

	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).AnyTimes()
	mockRepo.EXPECT().List(gomock.Any(), &serviceobsetting.ListServiceOBSettingFilter{
		BusinessID: businessID,
		ServiceIDs: []int64{serviceID},
	}).Return([]*model.ServiceObSetting{existingSetting}, nil)

	// Should not call Create since setting already exists

	// 创建一个 mock query 用于测试
	mockQuery := &query.Query{}
	err := logic.createDefaultOBSetting(context.Background(), mockQuery, businessID, serviceID)
	assert.NoError(t, err) // Should succeed but skip creation
}

func TestLogic_NewWithRepository(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockServiceRepo := servicemock.NewMockRepository(ctrl)
	mockOrganizationRepo := organizationmock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, mockServiceRepo, mockOrganizationRepo)
	assert.NotNil(t, logic)
	assert.Equal(t, mockRepo, logic.repo)
}

func TestLogic_UpdateOBService_CreateNew(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId:  123,
		ServiceId:   1,
		IsAvailable: lo.ToPtr(true),
		StaffIds:    []int64{1, 2, 3},
	}

	// 模拟 Upsert 成功
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().CreateStaffBinding(gomock.Any(), int64(1), []int64{1, 2, 3}).Return(nil)

	err := logic.UpdateOBService(context.Background(), req)
	assert.NoError(t, err)
}

func TestLogic_UpdateOBService_UpdateExisting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId:  456,
		ServiceId:   2,
		IsAvailable: lo.ToPtr(true),
		StaffIds:    []int64{4, 5, 6},
	}

	// 模拟 Upsert 成功
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().CreateStaffBinding(gomock.Any(), int64(2), []int64{4, 5, 6}).Return(nil)

	err := logic.UpdateOBService(context.Background(), req)
	assert.NoError(t, err)
}

func TestLogic_UpdateOBService_GetByServiceIDError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	req := &offeringpb.UpdateOBServiceRequest{
		BusinessId: 123,
		ServiceId:  1,
	}

	// 模拟 Upsert 失败
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(errors.New("upsert error"))

	err := logic.UpdateOBService(context.Background(), req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "upsert error")
}

func TestLogic_UpsertOBService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	now := time.Now()
	setting := &ServiceOBSetting{
		ID: 1,

		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    true,
		StaffIDs:      []int64{1, 2, 3},
		CreateTime:    now,
		UpdateTime:    now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
	}

	// 模拟 Upsert 成功
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().CreateStaffBinding(gomock.Any(), int64(1), []int64{1, 2, 3}).Return(nil)

	err := logic.UpsertOBService(context.Background(), setting)
	assert.NoError(t, err)
}

func TestLogic_UpsertOBService_NoStaffIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	now := time.Now()
	setting := &ServiceOBSetting{
		ID: 1,

		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    true,
		StaffIDs:      []int64{}, // 空的 staff IDs
		CreateTime:    now,
		UpdateTime:    now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION_NO,
	}

	// 模拟 Upsert 成功，不需要调用 CreateStaffBinding
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.UpsertOBService(context.Background(), setting)
	assert.NoError(t, err)
}

func TestLogic_UpsertOBService_UpsertError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo, nil, nil)

	now := time.Now()
	setting := &ServiceOBSetting{
		ID: 1,

		BusinessID:    123,
		ServiceID:     1,
		IsAvailable:   true,
		ShowBasePrice: offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE,
		IsAllStaff:    true,
		StaffIDs:      []int64{1, 2, 3},
		CreateTime:    now,
		UpdateTime:    now,
		ShowDuration:  offeringpb.ServiceOBSetting_SHOW_DURATION,
	}

	// 模拟 Upsert 失败
	mockRepo.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(errors.New("upsert error"))

	err := logic.UpsertOBService(context.Background(), setting)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "upsert error")
}
