package obsetting

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/organization"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func New() *Logic {
	return &Logic{
		repo:             serviceobsetting.NewRepository(),
		serviceRepo:      service.NewRepository(),
		organizationRepo: organization.NewRepository(),
	}
}

// NewWithRepository creates a new Logic with a custom repository (useful for testing)
func NewWithRepository(repo serviceobsetting.Repository,
	serviceRepo service.Repository,
	organizationRepo organization.Repository) *Logic {
	return &Logic{
		repo:             repo,
		serviceRepo:      serviceRepo,
		organizationRepo: organizationRepo,
	}
}

type Logic struct {
	repo             serviceobsetting.Repository
	serviceRepo      service.Repository
	organizationRepo organization.Repository
}

// ListByBusinessID gets service online booking settings by business ID with pagination and filter
func (l *Logic) ListByBusinessID(ctx context.Context,
	businessID int64,
	serviceIDs []int64,
	pagination *offeringpb.PaginationRef) ([]*ServiceOBSetting, int64, error) {
	filter := &serviceobsetting.ListServiceOBSettingFilter{
		BusinessID: businessID,
		ServiceIDs: serviceIDs,
	}

	var models []*model.ServiceObSetting
	var total int64
	var err error

	if pagination != nil {
		models, total, err = l.repo.ListWithPagination(ctx, filter, pagination.Offset, pagination.Limit)
	} else {
		// 如果没有分页参数，使用默认的 List 方法
		models, err = l.repo.List(ctx, filter)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(models))
	}

	if err != nil {
		return nil, 0, err
	}

	entities := ModelToEntityList(models)

	// Load staff binding information for all services
	for _, entity := range entities {
		staffIDs, err := l.repo.GetStaffBinding(ctx, entity.ServiceID)
		if err != nil {
			return nil, 0, err
		}
		entity.StaffIDs = staffIDs
	}

	return entities, total, nil
}

// UpdateOBService updates or creates the online booking setting for a service (upsert)
func (l *Logic) UpdateOBService(ctx context.Context, req *offeringpb.UpdateOBServiceRequest) error {
	// 使用 converter 方法将请求转换为实体
	setting := UpdateOBServiceRequestToEntity(req)
	if setting == nil {
		return fmt.Errorf("invalid request: request is nil")
	}

	// 使用 upsert 方法，利用唯一索引进行高效的创建或更新操作
	return l.UpsertOBService(ctx, setting)
}

// UpsertOBService creates or updates the online booking setting for a service using upsert
// This method uses the unique index (organization_type, organization_id, service_id) for efficient upsert
func (l *Logic) UpsertOBService(ctx context.Context, setting *ServiceOBSetting) error {
	// 使用 repository 的 Upsert 方法，利用唯一索引进行高效的 upsert 操作
	m := EntityToModel(setting)
	err := l.repo.Upsert(ctx, m)
	if err != nil {
		return err
	}

	// 同步 staff binding 信息
	if len(setting.StaffIDs) > 0 {
		err = l.repo.CreateStaffBinding(ctx, setting.ServiceID, setting.StaffIDs)
		if err != nil {
			return err
		}
	}

	return nil
}

// InitByServiceID 根据 service_id 初始化在线预约设置
// 获取该租户下的所有 business，为每个 business 创建默认的 ob setting 记录
// 这个方法需要接入到 createService 之后
func (l *Logic) InitByServiceID(ctx context.Context, query *query.Query, serviceID int64) error {
	// 1. 获取服务信息
	serviceModel, err := l.serviceRepo.WithQuery(query).Get(ctx, serviceID)
	if err != nil {
		return fmt.Errorf("failed to get service: %w", err)
	}

	// 2. 获取该租户下的所有 business IDs
	businessIDs, err := l.getAllBusinessIDsByOrganization(ctx,
		serviceModel.OrganizationType,
		serviceModel.OrganizationID)
	if err != nil {
		return fmt.Errorf("failed to get all business IDs: %w", err)
	}

	// 3. 为每个 business 创建默认的 ob setting 记录
	for _, businessID := range businessIDs {
		if err := l.createDefaultOBSetting(ctx, query, businessID, serviceID); err != nil {
			return err
		}
	}

	return nil
}

// InitByBusinessID 根据 business_id 初始化在线预约设置
// 查询该租户下的所有服务，为每个服务创建默认的 ob setting 记录
func (l *Logic) InitByBusinessID(ctx context.Context,
	query *query.Query, companyID int64, businessID int64) error {
	// 1. 从入参获取
	// companyID, err := l.organizationRepo.GetCompanyIDByBusinessID(ctx, businessID)
	// if err != nil {
	// 	return fmt.Errorf("failed to get company ID by business ID: %w", err)
	// }

	// 2. 查询该租户下的所有服务
	filter := &service.ListServiceFilter{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   companyID,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE}, // 只查询服务类型
	}

	services, _, err := l.serviceRepo.List(ctx, filter, nil)
	if err != nil {
		return fmt.Errorf("failed to list services: %w", err)
	}

	// 3. 为每个服务创建默认的 ob setting 记录
	for _, serviceModel := range services {
		if err := l.createDefaultOBSetting(ctx, query, businessID, serviceModel.ID); err != nil {
			return err
		}
	}

	return nil
}

// getAllBusinessIDsByOrganization 获取指定租户下的所有 business IDs
func (l *Logic) getAllBusinessIDsByOrganization(ctx context.Context,
	orgType organizationpb.OrganizationType,
	orgID int64) ([]int64, error) {
	// 目前只支持 COMPANY 类型，因为参数是 companyId
	if orgType != organizationpb.OrganizationType_COMPANY {
		return []int64{}, nil
	}

	// 调用 organization repository 获取该公司下的所有 business
	businesses, err := l.organizationRepo.ListBusinessesByCompanyID(ctx, orgID)
	if err != nil {
		return nil, fmt.Errorf("failed to list businesses by company: %w", err)
	}

	// 提取 business IDs
	businessIDs := make([]int64, 0, len(businesses))
	for _, business := range businesses {
		businessIDs = append(businessIDs, business.Id)
	}

	return businessIDs, nil
}

// createDefaultOBSetting 创建默认的在线预约设置记录
func (l *Logic) createDefaultOBSetting(ctx context.Context, query *query.Query, businessID, serviceID int64) error {
	// 检查是否已存在
	existingFilter := &serviceobsetting.ListServiceOBSettingFilter{
		BusinessID: businessID,
		ServiceIDs: []int64{serviceID},
	}
	existing, err := l.repo.WithQuery(query).List(ctx, existingFilter)
	if err != nil {
		return fmt.Errorf("failed to check existing setting: %w", err)
	}

	if len(existing) > 0 {
		// 已存在，跳过
		return nil
	}

	// 创建默认的 ob setting 记录
	showBasePrice := offeringpb.ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW
	showDuration := offeringpb.ServiceOBSetting_SHOW_DURATION
	defaultSetting := &model.ServiceObSetting{
		BusinessID:    businessID,
		ServiceID:     serviceID,
		IsAvailable:   true,           // 默认可用
		ShowBasePrice: &showBasePrice, // 默认不显示价格
		IsAllStaff:    true,           // 默认所有员工可用
		ShowDuration:  &showDuration,  // 默认显示时长
	}

	if err := l.repo.WithQuery(query).Create(ctx, defaultSetting); err != nil {
		return fmt.Errorf("failed to create ob setting for business %d, service %d: %w", businessID, serviceID, err)
	}

	return nil
}
