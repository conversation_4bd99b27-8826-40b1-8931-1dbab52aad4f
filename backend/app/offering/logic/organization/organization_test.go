package organization

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	organizationmocks "github.com/MoeGolibrary/moego/backend/app/offering/repo/organization/mocks"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestOrganizationInfoProvider_GetOrganizationInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := organizationmocks.NewMockRepository(ctrl)
	logic := NewLogic(mockRepo)

	tests := []struct {
		name          string
		orgType       organizationpb.OrganizationType
		orgID         int64
		mockSetup     func()
		expectedType  organizationpb.OrganizationType
		expectedID    int64
		expectedError bool
	}{
		{
			name:          "COMPANY类型直接返回",
			orgType:       organizationpb.OrganizationType_COMPANY,
			orgID:         123,
			mockSetup:     func() {}, // 不需要 mock
			expectedType:  organizationpb.OrganizationType_COMPANY,
			expectedID:    123,
			expectedError: false,
		},
		{
			name:    "BUSINESS类型转换为COMPANY",
			orgType: organizationpb.OrganizationType_BUSINESS,
			orgID:   456,
			mockSetup: func() {
				mockRepo.EXPECT().
					GetCompanyIdByBusinessId(gomock.Any(), int64(456)).
					Return(int64(789), nil)
			},
			expectedType:  organizationpb.OrganizationType_COMPANY,
			expectedID:    789,
			expectedError: false,
		},
		{
			name:    "BUSINESS类型转换失败",
			orgType: organizationpb.OrganizationType_BUSINESS,
			orgID:   456,
			mockSetup: func() {
				mockRepo.EXPECT().
					GetCompanyIdByBusinessId(gomock.Any(), int64(456)).
					Return(int64(0), errors.New("business not found"))
			},
			expectedType:  organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED,
			expectedID:    0,
			expectedError: true,
		},
		{
			name:          "ENTERPRISE类型暂时不支持",
			orgType:       organizationpb.OrganizationType_ENTERPRISE,
			orgID:         999,
			mockSetup:     func() {}, // 不需要 mock
			expectedType:  organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED,
			expectedID:    0,
			expectedError: true,
		},
		{
			name:          "不支持的组织类型",
			orgType:       organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED,
			orgID:         123,
			mockSetup:     func() {}, // 不需要 mock
			expectedType:  organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED,
			expectedID:    0,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			actualType, actualID, err := logic.GetOrganizationInfo(context.Background(), tt.orgType, tt.orgID)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedType, actualType)
			assert.Equal(t, tt.expectedID, actualID)
		})
	}
}
