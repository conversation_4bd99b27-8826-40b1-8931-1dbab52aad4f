load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "common",
    srcs = ["organization.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/common",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/organization",
        "//backend/proto/organization/v1:organization",
    ],
)

go_test(
    name = "common_test",
    srcs = ["organization_test.go"],
    embed = [":common"],
    deps = [
        "//backend/app/offering/repo/organization/mocks",
        "//backend/proto/organization/v1:organization",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)

go_library(
    name = "organization",
    srcs = ["organization.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/organization",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/organization",
        "//backend/proto/organization/v1:organization",
    ],
)

go_test(
    name = "organization_test",
    srcs = ["organization_test.go"],
    embed = [":organization"],
    deps = [
        "//backend/app/offering/repo/organization/mocks",
        "//backend/proto/organization/v1:organization",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
