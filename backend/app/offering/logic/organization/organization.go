package organization

import (
	"context"
	"errors"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/organization"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

type Logic struct {
	repo organization.Repository
}

func NewLogic(repo organization.Repository) *Logic {
	return &Logic{
		repo: repo,
	}
}

// GetOrganizationInfo 根据组织类型和ID获取公司级别的组织信息
// 业务逻辑：
// - BUSINESS: 转换为对应的 COMPANY（因为 service 存储在 Company level，business 只是作为一个 filter）
// - COMPANY: 直接返回（数据就在这个级别）
// - ENTERPRISE: 暂时拦截（未来会支持）
func (p *Logic) GetOrganizationInfo(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64,
) (organizationpb.OrganizationType, int64, error) {
	switch orgType {
	case organizationpb.OrganizationType_COMPANY:
		return orgType, orgID, nil
	case organizationpb.OrganizationType_BUSINESS:
		companyID, err := p.repo.GetCompanyIDByBusinessID(ctx, orgID)
		if err != nil {
			return organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED, 0, err
		}

		return organizationpb.OrganizationType_COMPANY, companyID, nil
	default:
		return organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED, 0,
			errors.New("unsupported organization type: " + orgType.String())
	}
}
