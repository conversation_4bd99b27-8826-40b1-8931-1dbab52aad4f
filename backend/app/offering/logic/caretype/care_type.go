package caretype

import (
	"context"

	caretype2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/caretype"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// Logic implements the business logic for CareTypeService.
type Logic struct {
	repo caretype2.Repository
}

// NewLogic creates a new Logic.
func NewLogic() *Logic {
	return &Logic{
		repo: caretype2.NewRepository(),
	}
}

// CreateCareType creates a new care type.
func (l *Logic) CreateCareType(ctx context.Context, e *CareType) (*CareType, error) {
	m := EntityToModel(e)
	err := l.repo.Create(ctx, m)
	if err != nil {
		return nil, err
	}

	// 更新 sort 为 ID
	m.Sort = m.ID
	err = l.repo.Update(ctx, m)
	if err != nil {
		return nil, err
	}

	return l.GetCareType(ctx, m.ID)
}

// GetCareType gets a care type by ID.
func (l *Logic) GetCareType(ctx context.Context, id int64) (*CareType, error) {
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return ModelToEntity(m), nil
}

// BatchGetCareTypes batch gets care types by IDs.
func (l *Logic) BatchGetCareTypes(ctx context.Context, ids []int64) ([]*CareType, error) {
	ms, err := l.repo.BatchGet(ctx, ids)
	if err != nil {
		return nil, err
	}

	return ModelToEntityList(ms), nil
}

// ListCareTypes lists care types.
func (l *Logic) ListCareTypes(ctx context.Context, req *pb.ListCareTypesRequest) ([]*CareType, error) {
	listFilter := &caretype2.ListCareTypeFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		Pagination:       req.GetPagination(),
	}
	ms, err := l.repo.List(ctx, listFilter)
	if err != nil {
		return nil, err
	}

	return ModelToEntityList(ms), nil
}

// UpdateCareType updates a care type.
func (l *Logic) UpdateCareType(ctx context.Context, e *CareType) (*CareType, error) {
	m := EntityToModel(e)
	err := l.repo.Update(ctx, m)
	if err != nil {
		return nil, err
	}

	return l.GetCareType(ctx, m.ID)
}

// DeleteCareType deletes a care type.
func (l *Logic) DeleteCareType(ctx context.Context, id int64) error {
	return l.repo.Delete(ctx, id)
}
