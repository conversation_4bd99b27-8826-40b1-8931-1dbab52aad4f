load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "inner",
    srcs = ["grooming_service_service.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/inner",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/addon/category",
        "//backend/app/offering/logic/caretype",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/category",
        "//backend/common/utils/money",
        "//backend/proto/offering/inner",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
    ],
)
