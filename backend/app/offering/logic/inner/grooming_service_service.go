package inner

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	addoncategory "github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/category"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	servicecategory "github.com/MoeGolibrary/moego/backend/app/offering/logic/service/category"
	"github.com/MoeGolibrary/moego/backend/common/utils/money"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func New() *Logic {
	return &Logic{
		logic:                service.NewLogic(),
		careTypeLogic:        caretype.NewLogic(),
		serviceCategoryLogic: servicecategory.NewLogic(),
		addonCategoryLogic:   addoncategory.NewLogic(),
		availabilityManager:  availability.NewManager(),
	}
}

type Logic struct {
	logic                *service.Logic
	careTypeLogic        *caretype.Logic
	serviceCategoryLogic *servicecategory.Logic
	addonCategoryLogic   *addoncategory.Logic
	availabilityManager  *availability.Manager
}

func (l *Logic) GetInnerApplicableServiceList(
	ctx context.Context, req *offeringinnerpb.GetInnerApplicableServiceListRequest) (
	*offeringinnerpb.GetInnerApplicableServiceListResponse, error) {
	// 调用 listAvailableServices 获取服务列表
	switch req.ServiceType {
	case offeringinnerpb.ServiceType_SERVICE:
		// 构建ListAvailableServicesRequest
		request, err := l.buildListAvailableServicesRequest(ctx, req)
		if err != nil {
			return nil, err
		}
		response, err := l.logic.ListAvailableServices(ctx, request)
		if err != nil {
			return nil, err
		}
		availableServices := response.Services
		// 获取 service
		servicesResp, err := l.logic.BatchGetServices(ctx, &offeringpb.BatchGetServicesRequest{
			Ids: lo.Map(availableServices, func(service *offeringpb.AvailableService, _ int) int64 {
				return service.Id
			}),
		})
		if err != nil {
			return nil, err
		}
		services := servicesResp.Services

		// 转换为 proto 格式
		serviceModels := l.convertToServiceModels(services, *req.ServiceItemType)

		return &offeringinnerpb.GetInnerApplicableServiceListResponse{
			ServiceList: serviceModels,
		}, nil
		// TODO: addon
	}

	return nil, nil
}

func (l *Logic) convertToServiceModels(
	services []*offeringpb.Service, serviceItemType offeringinnerpb.ServiceItemType) []*offeringinnerpb.ServiceModel {
	serviceModels := make([]*offeringinnerpb.ServiceModel, len(services))
	for _, s := range services {
		serviceModel := &offeringinnerpb.ServiceModel{
			Id:                      s.Id,
			Name:                    s.Name,
			Description:             lo.FromPtr(s.Description),
			Inactive:                s.Status != offeringpb.Service_ACTIVE,
			Images:                  s.Images,
			CategoryId:              lo.FromPtr(s.CategoryId),
			ColorCode:               s.ColorCode,
			RequireDedicatedStaff:   s.Attributes.GetIsRequiredStaff(),
			ServiceItemType:         serviceItemType,
			Type:                    offeringinnerpb.ServiceType_SERVICE,
			Price:                   money.ToFloat(s.Price),
			PriceUnit:               offeringinnerpb.ServicePriceUnit(s.Attributes.GetPriceUnit()),
			TaxId:                   s.TaxId,
			Duration:                lo.FromPtr(s.Attributes.Duration),
			AvailableBusinessIdList: s.AvailableBusiness.BusinessIds,
			IsDeleted:               s.IsDeleted,
		}

		if s.Attributes.AvailableLodgingType != nil &&
			!s.Attributes.AvailableLodgingType.IsAll {
			serviceModel.RequireDedicatedLodging = true
			serviceModel.LodgingFilter = true
			serviceModel.CustomizedLodgings = s.Attributes.AvailableLodgingType.LodgingTypeIds
		} else {
			serviceModel.RequireDedicatedLodging = false
			serviceModel.LodgingFilter = false
		}

		if s.Attributes.AvailableStaff != nil &&
			!s.Attributes.AvailableStaff.IsAll {
			serviceModel.RequireDedicatedStaff = true
			serviceModel.AvailableForAllStaff = false
			serviceModel.AvailableStaffs.IsAllAvailable = false
			serviceModel.AvailableStaffs.Ids = s.Attributes.AvailableStaff.StaffIds
		} else {
			serviceModel.RequireDedicatedStaff = false
			serviceModel.AvailableForAllStaff = true
			serviceModel.AvailableStaffs.IsAllAvailable = true
		}

		if s.Attributes.DefaultService != nil &&
			s.Attributes.DefaultService.Enabled {
			serviceModel.BundleServiceIds = s.Attributes.DefaultService.ServiceIds
		}

		if s.Attributes.ConditionalDefaultService != nil &&
			s.Attributes.ConditionalDefaultService.Enabled {
			serviceModel.AdditionalServiceRule.Enable = true
			serviceModel.AdditionalServiceRule.ApplyRules =
				lo.Map(s.Attributes.ConditionalDefaultService.ApplyRules, func(
					rule *offeringpb.ConditionalDefaultService_ApplyRule,
					_ int) *offeringinnerpb.AdditionalServiceRule_ApplyRule {
					return &offeringinnerpb.AdditionalServiceRule_ApplyRule{
						ServiceId:      rule.GetServiceId(),
						DateType:       offeringinnerpb.DateType(rule.GetDateType()),
						QuantityPerDay: rule.GetQuantityPerDay(),
					}
				})
		} else {
			serviceModel.AdditionalServiceRule.Enable = false
		}
	}

	return serviceModels
}

// GetInnerApplicableServiceListRequest 转换成ListAvailableServicesRequest
func (l *Logic) buildListAvailableServicesRequest(ctx context.Context,
	req *offeringinnerpb.GetInnerApplicableServiceListRequest) (
	*offeringpb.ListAvailableServicesRequest, error) {
	request := &offeringpb.ListAvailableServicesRequest{}
	availabilityContext := &offeringpb.ListAvailableServicesRequest_AvailabilityContext{}
	filter := &offeringpb.ListAvailableServicesRequest_Filter{}
	if req.BusinessId != nil {
		request.OrganizationType = organizationpb.OrganizationType_BUSINESS
		request.OrganizationId = *req.BusinessId
	} else if req.CompanyId != 0 {
		request.OrganizationType = organizationpb.OrganizationType_COMPANY
		request.OrganizationId = req.CompanyId
	} else {
		return nil, fmt.Errorf("business_id or company_id must be specified")
	}
	if req.ServiceItemType != nil {
		// 根据ServiceItemType 映射成 CareCategory
		careCategory := offeringpb.CareCategory(req.ServiceItemType.Number())

		// 查询 careType
		careTypes, err := l.careTypeLogic.ListCareTypes(ctx, &offeringpb.ListCareTypesRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   req.CompanyId,
		})
		if err != nil {
			return nil, err
		}
		// 根据 careCategory 筛选出符合的 careTypeId
		careTypePtr, _ := lo.Find(careTypes, func(careType *caretype.CareType) bool {
			return careType.CareCategory == careCategory
		})
		careTypeID := careTypePtr.ID
		filter.CareTypeIds = append(filter.CareTypeIds, careTypeID)
	}

	if req.PetId != nil {
		availabilityContext.PetIds = append(availabilityContext.PetIds, *req.PetId)
	}
	if req.Keyword != nil {
		filter.Keyword = req.Keyword
	}
	if req.Inactive != nil {
		if !*req.Inactive {
			filter.Statuses = append(filter.Statuses, offeringpb.Service_ACTIVE)
		} else {
			filter.Statuses = append(filter.Statuses, offeringpb.Service_INACTIVE)
		}
	}

	if req.Filter != nil {
		if req.Filter.FilterByService != nil {
			availabilityContext.ServiceIds = req.Filter.FilterByService.ServiceIds
		}
	}
	request.Context = availabilityContext

	return request, nil
}
