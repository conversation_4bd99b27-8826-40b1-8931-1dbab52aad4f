package addon

import (
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestServiceModelToProto(t *testing.T) {
	// Test case 1: Normal service model
	service := &model.Service{
		ID:               123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Sort:             100,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		Type:             offeringpb.Service_ADD_ON,
	}

	result := ServiceModelToProto(service)

	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.Id)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(456), result.OrganizationId)
	assert.Equal(t, int64(789), *result.CategoryId)
	assert.Equal(t, "Test Service", result.Name)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, int64(100), *result.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.AddOn_ACTIVE, result.Status)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
	assert.False(t, result.IsDeleted)

	// Test case 2: Nil service model
	result = ServiceModelToProto(nil)
	assert.Nil(t, result)
}

func TestCreateDefToServiceModel(t *testing.T) {
	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
	}

	// Execute
	result := CreateDefToServiceModel(createDef)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationID)
	assert.Equal(t, "Test AddOn", result.Name)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
	assert.Equal(t, offeringpb.Service_ACTIVE, result.Status)
	assert.Equal(t, offeringpb.Service_ADD_ON, result.Type)
}

func TestUpdateDefToServiceModel(t *testing.T) {
	// Test data
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:          123,
		Name:        lo.ToPtr("Updated AddOn"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.AddOn_INACTIVE),
	}

	// Execute
	result := UpdateDefToServiceModel(updateDef)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Updated AddOn", result.Name)
	assert.Equal(t, "Updated Description", *result.Description)
	assert.Equal(t, "#00FF00", result.ColorCode)
	assert.Equal(t, []string{"updated1.jpg", "updated2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.Service_INACTIVE, result.Status)
}
