package addon

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/applicable"
	organization2 "github.com/MoeGolibrary/moego/backend/app/offering/logic/organization"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/override"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	servicecategory2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/organization"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func NewLogic() *Logic {
	return &Logic{
		query:               query.Use(db.GetDB()),
		serviceRepo:         service2.NewRepository(),
		attributeManager:    attribute.NewManager(),
		availabilityManager: availability.NewManager(),
		businessScopeLogic:  service.NewBusinessScope(),
		applicableLogic:     applicable.NewLogic(),
		categoryRepo:        servicecategory2.NewRepository(),
		staffProcessor:      attribute.NewStaffProcessor(),
		overrideLogic:       override.NewLogic(),
		organizationLogic:   organization2.NewLogic(organization.NewRepository()),
	}
}

type Logic struct {
	query               transaction.Manager
	serviceRepo         service2.Repository
	attributeManager    *attribute.Manager
	availabilityManager *availability.Manager
	businessScopeLogic  *service.BusinessScope
	applicableLogic     *applicable.Logic
	categoryRepo        servicecategory2.Repository
	staffProcessor      *attribute.StaffProcessor
	overrideLogic       *override.Logic
	organizationLogic   *organization2.Logic
}

func (l *Logic) checkNameUnique(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64, name string) error {
	_, total, err := l.serviceRepo.List(ctx, &service2.ListServiceFilter{
		OrganizationType: orgType,
		OrganizationID:   orgID,
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
		Keyword:          lo.ToPtr(name),
	}, nil)
	if err != nil {
		return err
	}
	if total > 0 {
		return errors.New("addon name already exists")
	}

	return nil
}

// CreateAddOn creates a new addon.
func (l *Logic) CreateAddOn(ctx context.Context, createDef *offeringpb.AddOnCreateDef) (int64, error) {
	if err := l.checkNameUnique(
		ctx, createDef.GetOrganizationType(), createDef.GetOrganizationId(), createDef.GetName()); err != nil {
		return 0, err
	}

	addon := CreateDefToServiceModel(createDef)
	err := l.query.Transaction(func(tx *query.Query) error {
		// 1. 创建 addon
		err := l.serviceRepo.WithQuery(tx).Create(ctx, addon)
		if err != nil {
			return err
		}

		// 2. 更新 sort 为 ID
		addon.Sort = addon.ID
		err = l.serviceRepo.WithQuery(tx).Update(ctx, addon)
		if err != nil {
			return err
		}

		// 3. 保存 AddOn 相关属性
		if err := l.saveAddOnAttributes(ctx, tx, addon.ID, createDef); err != nil {
			return err
		}

		orgType, orgID := createDef.GetOrganizationType(), createDef.GetOrganizationId()
		// 4. 保存 business scope
		if err := l.businessScopeLogic.CreateBusinessScope(ctx, tx, service.AvailableBusinessScope{
			OrganizationType:  orgType,
			OrganizationID:    orgID,
			ServiceID:         addon.ID,
			AvailableBusiness: createDef.GetAvailableBusiness(),
		}); err != nil {
			return err
		}

		// 5. 保存 applicable service
		if err := l.applicableLogic.Create(
			ctx, tx, orgType, orgID, addon.ID, createDef.GetApplicableService()); err != nil {
			return err
		}

		// 6. 保存 Pet availability
		if err := l.availabilityManager.Save(ctx, tx, &availability.PetAvailability{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        addon.ID,
			PetTypeBreed:     createDef.GetAvailableTypeBreed(),
			PetSize:          createDef.GetAvailablePetSize(),
			CoatType:         createDef.GetAvailableCoatType(),
		}); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return addon.ID, nil
}

// saveAddOnAttributes 保存 AddOn 特有的属性
func (l *Logic) saveAddOnAttributes(
	ctx context.Context, tx *query.Query, addonID int64, createDef *offeringpb.AddOnCreateDef) error {
	addonAttributes := &offeringpb.ServiceAttributes{
		IsRequiredStaff: lo.ToPtr(createDef.GetIsRequiredStaff()),
		Duration:        lo.ToPtr(createDef.GetDuration()),
	}

	return l.attributeManager.Save(ctx, tx, addonID, addonAttributes)
}

// UpdateAddOn updates an addon.
func (l *Logic) UpdateAddOn(ctx context.Context, updateDef *offeringpb.AddOnUpdateDef) error {
	existing, err := l.serviceRepo.Get(ctx, updateDef.GetId())
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("addon not found")
	}

	// 如果名称有变化，检查名称唯一性
	if updateDef.GetName() != existing.Name {
		if err := l.checkNameUnique(
			ctx, existing.OrganizationType, existing.OrganizationID, updateDef.GetName()); err != nil {
			return err
		}
	}

	addon := UpdateDefToServiceModel(updateDef)
	err = l.query.Transaction(func(tx *query.Query) error {
		// 1. 更新 service
		if err := l.serviceRepo.WithQuery(tx).Update(ctx, addon); err != nil {
			return err
		}

		// 2. 更新 AddOn 相关属性
		if err := l.updateAddOnAttributes(ctx, tx, addon.ID, updateDef); err != nil {
			return err
		}

		orgType, orgID := addon.OrganizationType, addon.OrganizationID
		// 3. 更新 business scope
		if err := l.businessScopeLogic.UpdateBusinessScope(ctx, tx, service.AvailableBusinessScope{
			OrganizationType:  orgType,
			OrganizationID:    orgID,
			ServiceID:         addon.ID,
			AvailableBusiness: updateDef.GetAvailableBusiness(),
		}); err != nil {
			return err
		}

		// 4. 更新 applicable service
		if err := l.applicableLogic.Upsert(
			ctx, tx, orgType, orgID, addon.ID, updateDef.GetApplicableService()); err != nil {
			return err
		}

		// 5. 更新 Pet availability
		if err := l.availabilityManager.Save(ctx, tx, &availability.PetAvailability{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        addon.ID,
			PetTypeBreed:     updateDef.GetAvailableTypeBreed(),
			PetSize:          updateDef.GetAvailablePetSize(),
			CoatType:         updateDef.GetAvailableCoatType(),
		}); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// GetAddOn gets an addon by ID.
func (l *Logic) GetAddOn(ctx context.Context, id int64) (*offeringpb.AddOn, error) {
	// 1. 获取 service
	addon, err := l.serviceRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 2. 检查是否为 ADD_ON 类型
	if addon.Type != offeringpb.Service_ADD_ON {
		return nil, status.Error(codes.NotFound, "service is not an addon")
	}

	// 3. Model 转换为 Proto
	addonProto := ServiceModelToProto(addon)

	// 4. 加载 AddOn 特有的属性
	attributes, err := l.attributeManager.ListByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}
	if attributes != nil {
		addonProto.IsRequiredStaff = attributes.GetIsRequiredStaff()
		addonProto.Duration = attributes.GetDuration()
	}

	// 5. 加载 AvailableBusiness
	availableBusiness, err := l.businessScopeLogic.GetBusinessScope(ctx, id)
	if err != nil {
		return nil, err
	}
	addonProto.AvailableBusiness = availableBusiness

	// 6. 加载 ApplicableService
	applicableService, err := l.applicableLogic.GetByAddonID(ctx, addon.ID)
	if err != nil {
		return nil, err
	}
	addonProto.ApplicableService = applicableService

	// 7. 加载 Pet availability
	petAvailability, err := l.availabilityManager.GetByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}
	addonProto.AvailableTypeBreed = petAvailability.PetTypeBreed
	addonProto.AvailablePetSize = petAvailability.PetSize
	addonProto.AvailableCoatType = petAvailability.CoatType

	return addonProto, nil
}

// DeleteAddOn deletes an addon.
func (l *Logic) DeleteAddOn(ctx context.Context, id int64) error {
	err := l.query.Transaction(func(tx *query.Query) error {
		// 1. 删除 Business scope
		err := l.businessScopeLogic.DeleteBusinessScope(ctx, tx, id)
		if err != nil {
			return err
		}

		// 2. 删除 applicable service 配置
		err = l.applicableLogic.DeleteByAddonID(ctx, tx, id)
		if err != nil {
			return err
		}

		// 3. 删除 AddOn 特有的属性
		err = l.attributeManager.DeleteByServiceID(ctx, tx, id)
		if err != nil {
			return err
		}

		// 4. 删除 Pet availability
		err = l.availabilityManager.DeleteByServiceID(ctx, tx, id)
		if err != nil {
			return err
		}

		// 5. 删除 AddOn (使用 Service model)
		return l.serviceRepo.Delete(ctx, id)
	})

	if err != nil {
		return err
	}

	return nil
}

// ListAddOns lists addons based on the given request parameters.
func (l *Logic) ListAddOns(
	ctx context.Context, req *offeringpb.ListAddOnsRequest) (*offeringpb.ListAddOnsResponse, error) {
	// 1. 查询 addon 列表
	filter := l.buildListAddOnsFilter(req)
	pagination := l.getPaginationParams(req)
	services, total, err := l.serviceRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})
	// 2. 查询 addon 的 attributes
	attributeMap, err := l.attributeManager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 3. 查询 addon 的 business scope
	businessScopeMap, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 4. 查询 addon 的 applicable service
	applicableServiceMap, err := l.applicableLogic.ListByAddonIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 5. 构建响应
	addons := lo.Map(services, func(service *model.Service, _ int) *offeringpb.AddOn {
		addon := ServiceModelToProto(service)
		attributes := attributeMap[service.ID]
		if attributes != nil {
			addon.IsRequiredStaff = attributes.GetIsRequiredStaff()
			addon.Duration = attributes.GetDuration()
		}
		addon.AvailableBusiness = businessScopeMap[service.ID]
		addon.ApplicableService = applicableServiceMap[service.ID]

		return addon
	})

	return &offeringpb.ListAddOnsResponse{
		AddOns:     addons,
		Total:      int32(total),
		Pagination: pagination,
	}, nil
}

// buildListAddOnsFilter 构造 addon 列表查询的过滤条件
func (l *Logic) buildListAddOnsFilter(req *offeringpb.ListAddOnsRequest) *service2.ListServiceFilter {
	var filter = &service2.ListServiceFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	if req.Filter != nil {
		filter.Statuses = lo.Map(req.Filter.Statuses, func(s offeringpb.AddOn_Status, _ int) offeringpb.Service_Status {
			return offeringpb.Service_Status(s)
		})
		filter.Sources = req.GetFilter().GetSources()
		filter.Keyword = req.GetFilter().Keyword

		return filter
	}

	return filter
}

// getPaginationParams 获取分页参数
func (l *Logic) getPaginationParams(req *offeringpb.ListAddOnsRequest) *offeringpb.PaginationRef {
	if req.Pagination != nil {
		return req.Pagination
	}

	return &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  200,
	}
}

// BatchUpdateAddOns 批量更新 addon 信息（公共方法）
func (l *Logic) BatchUpdateAddOns(
	ctx context.Context, req *offeringpb.BatchUpdateAddOnsRequest) (*offeringpb.BatchUpdateAddOnsResponse, error) {
	var result *offeringpb.BatchUpdateAddOnsResponse
	err := l.query.Transaction(func(tx *query.Query) error {
		var err error
		result, err = l.batchUpdateAddOns(ctx, tx, req)

		return err
	})

	return result, err
}

// batchUpdateAddOns 批量更新 addon 信息（内部方法）
func (l *Logic) batchUpdateAddOns(ctx context.Context, tx *query.Query,
	req *offeringpb.BatchUpdateAddOnsRequest) (*offeringpb.BatchUpdateAddOnsResponse, error) {
	// 1. 获取需要更新的 addon 列表
	addonIDs := lo.Map(req.GetUpdateAddOns(), func(a *offeringpb.AddOnUpdateDef, _ int) int64 { return a.GetId() })
	filter := &service2.ListServiceFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		IDs:              addonIDs,
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	services, _, err := l.serviceRepo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	serviceMap := lo.KeyBy(services, func(s *model.Service) int64 { return s.ID })

	// 2. 批量更新 addon
	for _, updateDef := range req.GetUpdateAddOns() {
		// 3. 检查 addon 是否存在
		_, exists := serviceMap[updateDef.GetId()]
		if !exists {
			return nil, errors.New("addon not found")
		}

		update := UpdateDefToServiceModel(updateDef)
		if err := l.serviceRepo.WithQuery(tx).Update(ctx, update); err != nil {
			return nil, err
		}

		// 更新关联数据
		if err := l.updateAddOnRelatedData(ctx, tx, updateDef, req); err != nil {
			return nil, err
		}
	}

	// 4. 构造响应
	return &offeringpb.BatchUpdateAddOnsResponse{}, nil
}

// updateAddOnRelatedData 更新 addon 关联数据
func (l *Logic) updateAddOnRelatedData(
	ctx context.Context, tx *query.Query,
	updateDef *offeringpb.AddOnUpdateDef,
	req *offeringpb.BatchUpdateAddOnsRequest) error {
	// 更新 AvailableBusiness
	if updateDef.GetAvailableBusiness() != nil {
		err := l.businessScopeLogic.UpdateBusinessScope(ctx, tx, service.AvailableBusinessScope{
			OrganizationType:  req.GetOrganizationType(),
			OrganizationID:    req.GetOrganizationId(),
			ServiceID:         updateDef.GetId(),
			AvailableBusiness: updateDef.GetAvailableBusiness(),
		})
		if err != nil {
			return fmt.Errorf("failed to update business scope: %w", err)
		}
	}

	// 更新 is_required_staff 属性
	if updateDef.IsRequiredStaff != nil {
		err := l.updateAddOnAttributes(ctx, tx, updateDef.GetId(), updateDef)
		if err != nil {
			return fmt.Errorf("failed to update addon attributes: %w", err)
		}
	}

	return nil
}

// updateAddOnAttributes 更新 AddOn 特有的属性
func (l *Logic) updateAddOnAttributes(
	ctx context.Context, tx *query.Query, addonID int64, updateDef *offeringpb.AddOnUpdateDef) error {
	addonAttributes := &offeringpb.ServiceAttributes{
		IsRequiredStaff: updateDef.IsRequiredStaff,
		Duration:        updateDef.Duration,
	}

	return l.attributeManager.Save(ctx, tx, addonID, addonAttributes)
}

func (l *Logic) ListAvailableAddOns(
	ctx context.Context, req *offeringpb.ListAvailableAddOnsRequest,
) (*offeringpb.ListAvailableAddOnsResponse, error) {
	// 1. 获取租户信息
	orgType, orgID, err := l.organizationLogic.GetOrganizationInfo(
		ctx, req.GetOrganizationType(), req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	// 2. 构造过滤条件
	filter := service.AddOnRequestToFilter(req)
	filter.OrganizationType = orgType
	filter.OrganizationID = orgID

	// 3. 检查是否应该应用可用性检查
	if l.shouldApplyAvailabilityChecks(req) {
		addonIDs, err := l.getAvailableAddOnIDs(ctx, req, orgType, orgID)
		if err != nil {
			return nil, err
		}
		if len(addonIDs) == 0 {
			return l.buildEmptyAvailableAddOnsResponse(req), nil
		}
		filter.IDs = addonIDs
	}

	// 4. 根据 filter 查询 addon，包含分页
	addons, total, err := l.serviceRepo.List(ctx, filter, req.Pagination)
	if err != nil {
		return nil, err
	}

	// 5. 转换为 proto 对象，并丰富关联数据
	addonProtos, err := l.buildAvailableAddOnsResponse(ctx, addons)
	if err != nil {
		return nil, err
	}

	// 构造响应
	resp := &offeringpb.ListAvailableAddOnsResponse{
		AddOns: addonProtos,
		Total:  int32(total),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp, nil
}

// shouldApplyAvailabilityChecks 检查是否应该应用可用性检查
func (l *Logic) shouldApplyAvailabilityChecks(req *offeringpb.ListAvailableAddOnsRequest) bool {
	return req.GetOrganizationType() == organizationpb.OrganizationType_BUSINESS ||
		(req.GetContext() != nil && (req.GetContext().StaffId != nil ||
			len(req.GetContext().GetPetIds()) > 0 ||
			len(req.GetContext().GetServiceIds()) > 0))
}

// getAvailableAddOnIDs 获取所有可用性检查的 addon ID 列表
func (l *Logic) getAvailableAddOnIDs(
	ctx context.Context, req *offeringpb.ListAvailableAddOnsRequest,
	orgType organizationpb.OrganizationType, orgID int64) ([]int64, error) {

	// 用二维数组保存所有查询结果
	var allAddOnIDLists [][]int64

	// 1. Business scope 检查
	if req.GetOrganizationType() == organizationpb.OrganizationType_BUSINESS {
		addonIDs, err := l.businessScopeLogic.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetOrganizationId())
		if err != nil {
			return nil, fmt.Errorf("failed to get business available addon IDs: %w", err)
		}
		allAddOnIDLists = append(allAddOnIDLists, addonIDs)
	}

	// 2. Staff scope 检查
	if req.GetContext().StaffId != nil {
		addonIDs, err := l.staffProcessor.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetContext().GetStaffId())
		if err != nil {
			return nil, fmt.Errorf("failed to get staff available addon IDs: %w", err)
		}
		allAddOnIDLists = append(allAddOnIDLists, addonIDs)
	}

	// 3. Pet availability 检查
	if len(req.GetContext().GetPetIds()) > 0 {
		addonIDs, err := l.availabilityManager.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetContext().GetPetIds())
		if err != nil {
			return nil, fmt.Errorf("failed to get pet available addon IDs: %w", err)
		}
		allAddOnIDLists = append(allAddOnIDLists, addonIDs)
	}

	// 4. 获取多个 service 共同的 additional addon IDs
	if len(req.GetContext().GetServiceIds()) > 0 {
		addonIDs, err := l.applicableLogic.ListAdditionalAddOnIDs(ctx, req.GetContext().GetServiceIds())
		if err != nil {
			return nil, fmt.Errorf("failed to get additional addon IDs: %w", err)
		}
		allAddOnIDLists = append(allAddOnIDLists, addonIDs)
	}

	// 如果没有启用的检查，返回空列表
	if len(allAddOnIDLists) == 0 {
		return []int64{}, nil
	}

	// 遍历二维数组取交集
	result := allAddOnIDLists[0]
	for i := 1; i < len(allAddOnIDLists); i++ {
		result = lo.Intersect(result, allAddOnIDLists[i])
	}

	return result, nil
}

// buildEmptyAvailableAddOnsResponse 构造空的可用 addon 响应
func (l *Logic) buildEmptyAvailableAddOnsResponse(
	req *offeringpb.ListAvailableAddOnsRequest) *offeringpb.ListAvailableAddOnsResponse {
	resp := &offeringpb.ListAvailableAddOnsResponse{
		AddOns: []*offeringpb.AvailableAddOn{},
		Total:  0,
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp
}

// buildAvailableAddOnsResponse 构建可用 addon 响应
func (l *Logic) buildAvailableAddOnsResponse(
	ctx context.Context, addons []*model.Service) ([]*offeringpb.AvailableAddOn, error) {
	// 1. 提取 addon ID 列表
	addonIDs := lo.Map(addons, func(addon *model.Service, _ int) int64 {
		return addon.ID
	})

	// 2. 批量获取 Attributes
	attributesMap, err := l.attributeManager.ListByServiceIDs(ctx, addonIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon attributes: %w", err)
	}

	// 3. 批量获取 Business overrides
	businessOverridesMap, err := l.overrideLogic.ListBusinessOverridesByServiceIDs(ctx, addonIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load business overrides: %w", err)
	}

	// 4. 批量获取 Pet overrides
	petOverrides, _, err := l.overrideLogic.ListPetOverride(ctx, &service2.ListPetOverrideFilter{
		ServiceIDs: addonIDs,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to load pet overrides: %w", err)
	}
	petOverridesMap := lo.GroupBy(petOverrides, func(petOverride *offeringpb.PetOverride) int64 {
		return petOverride.ServiceId
	})

	// 5. 转换为 proto 对象
	pbs := lo.Map(addons, func(addon *model.Service, _ int) *offeringpb.AvailableAddOn {
		pb := service.ModelToAvailableAddOnProto(addon)

		// 设置 addon 特有的属性
		attributes := attributesMap[addon.ID]
		if attributes != nil {
			pb.IsRequiredStaff = lo.FromPtr(attributes.IsRequiredStaff)
			pb.Duration = lo.FromPtr(attributes.Duration)
		}

		pb.BusinessOverrides = businessOverridesMap[addon.ID]
		pb.PetOverrides = petOverridesMap[addon.ID]

		return pb
	})

	return pbs, nil
}
