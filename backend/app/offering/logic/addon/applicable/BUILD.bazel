load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "applicable",
    srcs = [
        "applicable.go",
        "converter.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/applicable",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/additional",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "applicable_test",
    srcs = [
        "applicable_test.go",
        "converter_test.go",
    ],
    embed = [":applicable"],
    deps = [
        "//backend/app/offering/logic/transaction/mocks",
        "//backend/app/offering/repo/db/additional/mocks",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/service/mocks",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "converter_test",
    srcs = ["converter_test.go"],
    embed = [":applicable"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_stretchr_testify//assert",
    ],
)
