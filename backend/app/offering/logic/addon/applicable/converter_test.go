package applicable

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// int64Ptr 辅助函数，用于创建 int64 指针
func int64Ptr(v int64) *int64 {
	return &v
}

func TestApplicableServiceToModels_Success(t *testing.T) {
	// Test data
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: int64Ptr(1),
				ServiceIds: []int64{10, 20},
			},
			{
				CareTypeId: int64Ptr(2),
				ServiceIds: []int64{30},
			},
		},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Len(t, models, 3)

	// 验证第一个模型（serviceID 10）
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, models[0].OrganizationType)
	assert.Equal(t, int64(123), models[0].OrganizationID)
	assert.Equal(t, int64(10), models[0].ServiceID)
	assert.Equal(t, int64(1), models[0].AdditionalServiceID)

	// 验证第二个模型（serviceID 20）
	assert.Equal(t, int64(20), models[1].ServiceID)
	assert.Equal(t, int64(1), models[1].AdditionalServiceID)

	// 验证第三个模型（serviceID 30）
	assert.Equal(t, int64(30), models[2].ServiceID)
	assert.Equal(t, int64(1), models[2].AdditionalServiceID)
}

func TestApplicableServiceToModels_WithNilApplicableService(t *testing.T) {
	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.Len(t, models, 0)
}

func TestApplicableServiceToModels_WithEmptySpecificServices(t *testing.T) {
	// Test data
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Len(t, models, 0)
}

func TestApplicableServiceToModels_WithEmptyServiceIds(t *testing.T) {
	// Test data
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: int64Ptr(1),
				ServiceIds: []int64{},
			},
		},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Len(t, models, 0)
}

func TestApplicableServiceToModels_WithNilCareTypeId(t *testing.T) {
	// Test data
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: nil,
				ServiceIds: []int64{10},
			},
		},
	}

	// Execute
	models := ProtoToModels(
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Len(t, models, 1)
	assert.Equal(t, int64(10), models[0].ServiceID)
	assert.Equal(t, int64(1), models[0].AdditionalServiceID)
}

func TestModelsToApplicableServiceWithCareTypeMap_Success(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		// careType 1 的服务
		{
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
		{
			ServiceID:           11,
			AdditionalServiceID: 1,
		},
		// careType 2 的服务
		{
			ServiceID:           20,
			AdditionalServiceID: 1,
		},
	}

	careTypeIDByServiceID := map[int64]int64{
		10: 1,
		11: 1,
		20: 2,
	}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 2)

	// 验证 careType 1
	careType1Found := false
	for _, specSvc := range result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 1 {
			careType1Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(10))
			assert.Contains(t, specSvc.ServiceIds, int64(11))
		}
	}
	assert.True(t, careType1Found)

	// 验证 careType 2
	careType2Found := false
	for _, specSvc := range result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 2 {
			careType2Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(20))
		}
	}
	assert.True(t, careType2Found)
}

func TestModelsToApplicableServiceWithCareTypeMap_WithEmptyModels(t *testing.T) {
	// Execute
	result := ModelsToProto([]*model.AdditionalService{}, map[int64]int64{})

	// Assertions
	assert.Nil(t, result)
}

func TestModelsToApplicableServiceWithCareTypeMap_WithZeroCareTypeID(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
	}

	careTypeIDByServiceID := map[int64]int64{
		10: 0, // careTypeID 为 0
	}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 1)
	assert.Equal(t, int64(0), *result.SpecificServices[0].CareTypeId)
	assert.Contains(t, result.SpecificServices[0].ServiceIds, int64(10))
}

func TestModelsToApplicableServiceWithCareTypeMap_WithServiceNotFoundInMap(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
	}

	careTypeIDByServiceID := map[int64]int64{
		// 10 不在 map 中，默认 careTypeID 为 0
	}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 1)
	assert.Equal(t, int64(0), *result.SpecificServices[0].CareTypeId)
	assert.Contains(t, result.SpecificServices[0].ServiceIds, int64(10))
}

func TestModelsToApplicableServiceWithCareTypeMap_WithZeroServiceID(t *testing.T) {
	// Test data - 包含零值 ServiceID
	models := []*model.AdditionalService{
		{
			ServiceID:           0,
			AdditionalServiceID: 1,
		},
		{
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
	}

	careTypeIDByServiceID := map[int64]int64{
		10: 1,
	}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 1)
	assert.Len(t, result.SpecificServices[0].ServiceIds, 1)
	assert.Contains(t, result.SpecificServices[0].ServiceIds, int64(10))
	assert.NotContains(t, result.SpecificServices[0].ServiceIds, int64(0))
}

func TestModelsToApplicableServiceWithCareTypeMap_WithOnlyZeroServiceIDs(t *testing.T) {
	// Test data - 所有 ServiceID 都是零值
	models := []*model.AdditionalService{
		{
			ServiceID:           0,
			AdditionalServiceID: 1,
		},
	}

	careTypeIDByServiceID := map[int64]int64{}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.Nil(t, result) // 所有 ServiceID 都是零值，返回 nil
}

func TestModelsToApplicableServiceWithCareTypeMap_WithMultipleCareTypes(t *testing.T) {
	// Test data
	models := []*model.AdditionalService{
		{ServiceID: 10, AdditionalServiceID: 1},
		{ServiceID: 11, AdditionalServiceID: 1},
		{ServiceID: 20, AdditionalServiceID: 1},
		{ServiceID: 21, AdditionalServiceID: 1},
		{ServiceID: 30, AdditionalServiceID: 1},
	}

	careTypeIDByServiceID := map[int64]int64{
		10: 1,
		11: 1,
		20: 2,
		21: 2,
		30: 3,
	}

	// Execute
	result := ModelsToProto(models, careTypeIDByServiceID)

	// Assertions
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 3)

	// 验证 careType 1
	careType1Found := false
	for _, specSvc := range result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 1 {
			careType1Found = true
			assert.Len(t, specSvc.ServiceIds, 2)
			assert.Contains(t, specSvc.ServiceIds, int64(10))
			assert.Contains(t, specSvc.ServiceIds, int64(11))
		}
	}
	assert.True(t, careType1Found)

	// 验证 careType 2
	careType2Found := false
	for _, specSvc := range result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 2 {
			careType2Found = true
			assert.Len(t, specSvc.ServiceIds, 2)
			assert.Contains(t, specSvc.ServiceIds, int64(20))
			assert.Contains(t, specSvc.ServiceIds, int64(21))
		}
	}
	assert.True(t, careType2Found)

	// 验证 careType 3
	careType3Found := false
	for _, specSvc := range result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 3 {
			careType3Found = true
			assert.Len(t, specSvc.ServiceIds, 1)
			assert.Contains(t, specSvc.ServiceIds, int64(30))
		}
	}
	assert.True(t, careType3Found)
}
