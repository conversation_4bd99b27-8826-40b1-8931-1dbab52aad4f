package applicable

import (
	"context"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

type Logic struct {
	repo        additional.Repository
	serviceRepo service.Repository
}

func NewLogic() *Logic {
	return &Logic{
		repo:        additional.NewRepository(),
		serviceRepo: service.NewRepository(),
	}
}

func NewLogicWithRepository(
	repo additional.Repository, serviceRepo service.Repository) *Logic {
	return &Logic{
		repo:        repo,
		serviceRepo: serviceRepo,
	}
}

// Create 创建 addon 的 applicable service 配置
func (l *Logic) Create(
	ctx context.Context, query *query.Query,
	orgType organizationpb.OrganizationType, orgID, addonID int64,
	applicableService *offeringpb.ApplicableService) error {
	if applicableService == nil {
		return nil
	}

	models := ProtoToModels(orgType, orgID, addonID, applicableService)
	if len(models) == 0 {
		return nil
	}

	return l.repo.WithTX(query).BatchCreate(ctx, models)
}

// Upsert 批量写入 addon 的 applicable service 配置
func (l *Logic) Upsert(
	ctx context.Context, query *query.Query,
	orgType organizationpb.OrganizationType, orgID, addonID int64,
	applicableService *offeringpb.ApplicableService) error {
	if applicableService == nil {
		return nil
	}

	err := l.DeleteByAddonID(ctx, query, addonID)
	if err != nil {
		return err
	}

	return l.Create(ctx, query, orgType, orgID, addonID, applicableService)
}

// GetByAddonID 获取单个 addon 的 applicable service 配置
func (l *Logic) GetByAddonID(ctx context.Context, addonID int64) (*offeringpb.ApplicableService, error) {
	addonIDToApplicableService, err := l.ListByAddonIDs(ctx, []int64{addonID})
	if err != nil {
		return nil, err
	}
	if len(addonIDToApplicableService) == 0 {
		return nil, nil
	}

	return addonIDToApplicableService[addonID], nil
}

// ListByAddonIDs 批量获取多个 addon 的 applicable service 配置
// 返回值：addonID -> ApplicableService
func (l *Logic) ListByAddonIDs(
	ctx context.Context, addonIDs []int64) (map[int64]*offeringpb.ApplicableService, error) {
	if len(addonIDs) == 0 {
		return make(map[int64]*offeringpb.ApplicableService), nil
	}

	models, err := l.repo.ListByAdditionalServiceIDs(ctx, addonIDs)
	if err != nil {
		return nil, err
	}

	// 按 addonID 分组（AdditionalServiceID 存储的是 addonID）
	modelsByAddonID := lo.GroupBy(models, func(model *model.AdditionalService) int64 {
		return model.AdditionalServiceID
	})

	// 获取所有相关的 service 信息，用于确定 careTypeID
	allServiceIDs := lo.FilterMap(models, func(model *model.AdditionalService, _ int) (int64, bool) {
		return model.ServiceID, model.ServiceID > 0
	})

	// serviceID -> careTypeID
	careTypeIDByServiceID := make(map[int64]int64)
	if len(allServiceIDs) > 0 {
		services, err := l.serviceRepo.BatchGet(ctx, allServiceIDs)
		if err != nil {
			return nil, err
		}
		careTypeIDByServiceID = lo.SliceToMap(services, func(service *model.Service) (int64, int64) {
			return service.ID, service.CareTypeID
		})
	}

	// 转换为 proto 格式
	result := make(map[int64]*offeringpb.ApplicableService, len(addonIDs))
	for _, addonID := range addonIDs {
		if models, exists := modelsByAddonID[addonID]; exists {
			result[addonID] = ModelsToProto(models, careTypeIDByServiceID)
		} else {
			result[addonID] = nil
		}
	}

	return result, nil
}

// DeleteByAddonID 删除 addon 的所有 applicable service 配置
func (l *Logic) DeleteByAddonID(ctx context.Context, query *query.Query, addonID int64) error {
	return l.repo.WithTX(query).DeleteByAdditionalServiceID(ctx, addonID)
}

// ListAdditionalAddOnIDs 批量获取多个 service 共同的 additional addon IDs
func (l *Logic) ListAdditionalAddOnIDs(ctx context.Context, serviceIDs []int64) ([]int64, error) {
	if len(serviceIDs) == 0 {
		return []int64{}, nil
	}

	// 1. 获取所有 service 的 additional service 配置
	models, err := l.repo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	if len(models) == 0 {
		return []int64{}, nil
	}

	// 2. 按 serviceID 分组提取每个 service 的 additional service IDs
	serviceIDToAddonIDsMap := lo.MapValues(
		lo.GroupBy(models, func(model *model.AdditionalService) int64 {
			return model.ServiceID
		}),
		func(models []*model.AdditionalService, _ int64) []int64 {
			return lo.FilterMap(models, func(m *model.AdditionalService, _ int) (int64, bool) {
				return m.AdditionalServiceID, m.AdditionalServiceID > 0
			})
		},
	)

	// 3. 取所有 service 的 additional service IDs 的交集
	var allAdditionalServiceIDs [][]int64
	for _, serviceID := range serviceIDs {
		additionalServiceIDs, exists := serviceIDToAddonIDsMap[serviceID]
		if !exists || len(additionalServiceIDs) == 0 {
			// 如果某个 service 没有配置，则交集为空
			return []int64{}, nil
		}

		allAdditionalServiceIDs = append(allAdditionalServiceIDs, additionalServiceIDs)
	}

	additionalServiceIDs := allAdditionalServiceIDs[0]
	for i := 1; i < len(allAdditionalServiceIDs); i++ {
		additionalServiceIDs = lo.Intersect(additionalServiceIDs, allAdditionalServiceIDs[i])
	}

	if len(additionalServiceIDs) == 0 {
		return []int64{}, nil
	}

	services, err := l.serviceRepo.BatchGet(ctx, additionalServiceIDs)
	if err != nil {
		return nil, err
	}

	// 4. 过滤出 ADD_ON 类型的 additional service IDs
	return lo.FilterMap(services, func(service *model.Service, _ int) (int64, bool) {
		return service.ID, service.Type == offeringpb.Service_ADD_ON
	}), nil
}
