package applicable

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ProtoToModels 将 ApplicableService proto 转换为数据库模型
func ProtoToModels(
	organizationType organizationpb.OrganizationType,
	organizationID, addonID int64,
	applicableService *offeringpb.ApplicableService) []*model.AdditionalService {
	if applicableService == nil {
		return []*model.AdditionalService{}
	}

	var models []*model.AdditionalService

	// 转换 specific_services
	for _, specificService := range applicableService.SpecificServices {
		for _, serviceID := range specificService.GetServiceIds() {
			models = append(models, &model.AdditionalService{
				OrganizationType:    organizationType,
				OrganizationID:      organizationID,
				ServiceID:           serviceID,
				AdditionalServiceID: addonID, // addon 作为 additional service 存在
			})
		}
	}

	return models
}

// ModelsToProto 将数据库模型转换为 ApplicableService proto，使用 careTypeID 映射
func ModelsToProto(
	models []*model.AdditionalService, careTypeIDByServiceID map[int64]int64) *offeringpb.ApplicableService {
	if len(models) == 0 {
		return nil
	}

	// 按 careTypeID 分组 serviceIDs
	careTypeToServices := make(map[int64][]int64)

	for _, model := range models {
		if model.ServiceID > 0 {
			// 根据 service 的 careTypeID 分组
			careTypeID := careTypeIDByServiceID[model.ServiceID]
			careTypeToServices[careTypeID] = append(careTypeToServices[careTypeID], model.ServiceID)
		}
	}

	// 如果没有配置，返回 nil
	if len(careTypeToServices) == 0 {
		return nil
	}

	var specificServices []*offeringpb.SpecificService

	// 处理每个 careType 的服务
	for careTypeID, serviceIDs := range careTypeToServices {
		if len(serviceIDs) > 0 {
			specificServices = append(specificServices, &offeringpb.SpecificService{
				CareTypeId: &careTypeID,
				ServiceIds: serviceIDs,
			})
		}
	}

	return &offeringpb.ApplicableService{
		SpecificServices: specificServices,
	}
}
