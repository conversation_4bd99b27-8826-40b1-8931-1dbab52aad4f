package applicable

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction/mocks"
	mockadditional "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	mockservice "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl        *gomock.Controller
	mockQuery   *mocks.MockQuery
	mockRepo    *mockadditional.MockRepository
	mockService *mockservice.MockRepository
	logic       *Logic
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)
	mockQuery := mocks.NewMockQuery()
	mockRepo := mockadditional.NewMockRepository(ctrl)
	mockService := mockservice.NewMockRepository(ctrl)

	logic := NewLogicWithRepository(mockRepo, mockService)

	return &testHelper{
		ctrl:        ctrl,
		mockQuery:   mockQuery,
		mockRepo:    mockRepo,
		mockService: mockService,
		logic:       logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

func TestLogic_Create_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	careTypeID1 := int64(1)
	careTypeID2 := int64(2)
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: &careTypeID1,
				ServiceIds: []int64{10, 20},
			},
			{
				CareTypeId: &careTypeID2,
				ServiceIds: []int64{30},
			},
		},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil, // 传入 nil，因为 repo.WithTX 会被 mock
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_WithNilApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_WithEmptySpecificServices(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data with empty SpecificServices
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{},
	}

	// Execute (should not call BatchCreate because len(models) == 0)
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Create_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	careTypeID1 := int64(1)
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: &careTypeID1,
				ServiceIds: []int64{10},
			},
		},
	}

	// Mock expectations - 模拟 repository 错误
	expectedErr := errors.New("repository error")
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(expectedErr)

	// Execute
	err := helper.logic.Create(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_Upsert_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	careTypeID1 := int64(1)
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: &careTypeID1,
				ServiceIds: []int64{10, 20},
			},
		},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo).Times(2)
	helper.mockRepo.EXPECT().DeleteByAdditionalServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_Upsert_DeleteError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	careTypeID1 := int64(1)
	applicableService := &offeringpb.ApplicableService{
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId: &careTypeID1,
				ServiceIds: []int64{10},
			},
		},
	}

	// Mock expectations - 模拟 delete 错误
	expectedErr := errors.New("delete error")
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByAdditionalServiceID(gomock.Any(), int64(1)).Return(expectedErr)

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		applicableService,
	)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_Upsert_WithNilApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	err := helper.logic.Upsert(
		context.Background(),
		nil,
		organizationpb.OrganizationType_COMPANY,
		123,
		1,
		nil,
	)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_GetByAddonID_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonID := int64(1)
	models := []*model.AdditionalService{
		{
			ID:                  1,
			OrganizationType:    organizationpb.OrganizationType_COMPANY,
			OrganizationID:      123,
			ServiceID:           10,
			AdditionalServiceID: addonID,
		},
		{
			ID:                  2,
			OrganizationType:    organizationpb.OrganizationType_COMPANY,
			OrganizationID:      123,
			ServiceID:           20,
			AdditionalServiceID: addonID,
		},
	}

	services := []*model.Service{
		{ID: 10, CareTypeID: 1},
		{ID: 20, CareTypeID: 1},
	}

	// Mock expectations - GetByAddonID 调用 ListByAddonIDs，需要 mock ListByAdditionalServiceIDs
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{addonID}).Return(models, nil)
	helper.mockService.EXPECT().BatchGet(gomock.Any(), []int64{10, 20}).Return(services, nil)

	// Execute
	result, err := helper.logic.GetByAddonID(context.Background(), addonID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.SpecificServices, 1)
	// careTypeID 会被正确设置
	assert.NotNil(t, result.SpecificServices[0].CareTypeId)
	assert.Equal(t, int64(1), *result.SpecificServices[0].CareTypeId)
	assert.Contains(t, result.SpecificServices[0].ServiceIds, int64(10))
	assert.Contains(t, result.SpecificServices[0].ServiceIds, int64(20))
}

func TestLogic_GetByAddonID_EmptyModels(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonID := int64(1)

	// Mock expectations - GetByAddonID 调用 ListByAddonIDs，需要 mock ListByAdditionalServiceIDs
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{addonID}).Return([]*model.AdditionalService{}, nil)
	// 空 models 时不需要调用 BatchGet

	// Execute
	result, err := helper.logic.GetByAddonID(context.Background(), addonID)

	// Assertions
	assert.NoError(t, err)
	assert.Nil(t, result) // 空模型返回 nil
}

func TestLogic_GetByAddonID_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonID := int64(1)
	expectedErr := errors.New("repository error")

	// Mock expectations - GetByAddonID 调用 ListByAddonIDs，需要 mock ListByAdditionalServiceIDs
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{addonID}).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.GetByAddonID(context.Background(), addonID)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListByAddonIDs_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonIDs := []int64{1, 2}
	models := []*model.AdditionalService{
		{
			ID:                  1,
			ServiceID:           10,
			AdditionalServiceID: 1, // addonID
		},
		{
			ID:                  2,
			ServiceID:           20,
			AdditionalServiceID: 1, // addonID
		},
		{
			ID:                  3,
			ServiceID:           30,
			AdditionalServiceID: 1, // addonID - service 30 也属于 addon 1
		},
		{
			ID:                  4,
			ServiceID:           40,
			AdditionalServiceID: 2, // addonID
		},
	}

	services := []*model.Service{
		{ID: 10, CareTypeID: 1},
		{ID: 20, CareTypeID: 1},
		{ID: 30, CareTypeID: 2}, // addon 1 有不同的 careType
		{ID: 40, CareTypeID: 2},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), addonIDs).Return(models, nil)
	helper.mockService.EXPECT().BatchGet(gomock.Any(), []int64{10, 20, 30, 40}).Return(services, nil)

	// Execute
	result, err := helper.logic.ListByAddonIDs(context.Background(), addonIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 2)

	// Verify addon 1 - 有 2 个不同的 careType
	assert.NotNil(t, result[1])
	assert.Len(t, result[1].SpecificServices, 2) // careType 1 和 careType 2

	// 查找 careType 1 和 careType 2 的服务（顺序可能不确定）
	careType1Found := false
	careType2Found := false
	for _, specSvc := range result[1].SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 1 {
			careType1Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(10))
			assert.Contains(t, specSvc.ServiceIds, int64(20))
		}
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 2 {
			careType2Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(30))
		}
	}
	assert.True(t, careType1Found)
	assert.True(t, careType2Found)

	// Verify addon 2
	assert.NotNil(t, result[2])
	assert.Len(t, result[2].SpecificServices, 1) // careType 2
}

func TestLogic_ListByAddonIDs_EmptyAddonIDs(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	result, err := helper.logic.ListByAddonIDs(context.Background(), []int64{})

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Empty(t, result)
}

func TestLogic_ListByAddonIDs_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonIDs := []int64{1, 2}
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), addonIDs).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.ListByAddonIDs(context.Background(), addonIDs)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListByAddonIDs_ServiceRepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonIDs := []int64{1}
	models := []*model.AdditionalService{
		{
			ID:                  1,
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
	}

	expectedErr := errors.New("service repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), addonIDs).Return(models, nil)
	helper.mockService.EXPECT().BatchGet(gomock.Any(), []int64{10}).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.ListByAddonIDs(context.Background(), addonIDs)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListByAddonIDs_WithCareTypeGrouping(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - addon 1 有多个 careType 的服务
	addonIDs := []int64{1}
	models := []*model.AdditionalService{
		// careType 1 的服务
		{
			ID:                  1,
			ServiceID:           10,
			AdditionalServiceID: 1,
		},
		{
			ID:                  2,
			ServiceID:           11,
			AdditionalServiceID: 1,
		},
		// careType 2 的服务
		{
			ID:                  3,
			ServiceID:           20,
			AdditionalServiceID: 1,
		},
	}

	services := []*model.Service{
		{ID: 10, CareTypeID: 1},
		{ID: 11, CareTypeID: 1},
		{ID: 20, CareTypeID: 2},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), addonIDs).Return(models, nil)
	helper.mockService.EXPECT().BatchGet(gomock.Any(), []int64{10, 11, 20}).Return(services, nil)

	// Execute
	result, err := helper.logic.ListByAddonIDs(context.Background(), addonIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 1)

	addon1Result := result[1]
	assert.NotNil(t, addon1Result)
	assert.Len(t, addon1Result.SpecificServices, 2) // careType 1 和 careType 2

	// 验证 careType 1
	careType1Found := false
	for _, specSvc := range addon1Result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 1 {
			careType1Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(10))
			assert.Contains(t, specSvc.ServiceIds, int64(11))
		}
	}
	assert.True(t, careType1Found)

	// 验证 careType 2
	careType2Found := false
	for _, specSvc := range addon1Result.SpecificServices {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 2 {
			careType2Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(20))
		}
	}
	assert.True(t, careType2Found)
}

func TestLogic_DeleteByAddonID_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonID := int64(1)

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByAdditionalServiceID(gomock.Any(), addonID).Return(nil)

	// Execute
	err := helper.logic.DeleteByAddonID(context.Background(), nil, addonID)

	// Assertions
	assert.NoError(t, err)
}

func TestLogic_DeleteByAddonID_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	addonID := int64(1)
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockRepo)
	helper.mockRepo.EXPECT().DeleteByAdditionalServiceID(gomock.Any(), addonID).Return(expectedErr)

	// Execute
	err := helper.logic.DeleteByAddonID(context.Background(), nil, addonID)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestLogic_ListAdditionalAddOnIds_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceIDs := []int64{1, 2}
	models := []*model.AdditionalService{
		// Service 1 配置：指向 addon 10
		{ServiceID: 1, AdditionalServiceID: 10, AdditionalCareTypeID: 0},
		// Service 2 配置：指向 addon 10
		{ServiceID: 2, AdditionalServiceID: 10, AdditionalCareTypeID: 0},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(models, nil)

	// Mock BatchGet for addon 10
	helper.mockService.EXPECT().BatchGet(gomock.Any(), []int64{10}).Return([]*model.Service{
		{ID: 10, Type: offeringpb.Service_ADD_ON},
	}, nil)

	// Execute
	result, err := helper.logic.ListAdditionalAddOnIDs(context.Background(), serviceIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Contains(t, result, int64(10)) // 共同的 addon ID
}

func TestLogic_ListAdditionalAddOnIds_EmptyServiceIDs(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Execute
	result, err := helper.logic.ListAdditionalAddOnIDs(context.Background(), []int64{})

	// Assertions
	assert.NoError(t, err)
	assert.Empty(t, result)
}

func TestLogic_ListAdditionalAddOnIds_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	serviceIDs := []int64{1, 2}
	expectedErr := errors.New("repository error")

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(nil, expectedErr)

	// Execute
	result, err := helper.logic.ListAdditionalAddOnIDs(context.Background(), serviceIDs)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListAdditionalAddOnIds_NoCommonConfiguration(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - 两个 service 没有共同的配置
	serviceIDs := []int64{1, 2}
	models := []*model.AdditionalService{
		// Service 1 配置：指向 addon 10
		{ServiceID: 1, AdditionalServiceID: 10, AdditionalCareTypeID: 0},
		// Service 2 配置：指向 addon 20（不同）
		{ServiceID: 2, AdditionalServiceID: 20, AdditionalCareTypeID: 0},
	}

	// Mock expectations
	helper.mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).Return(models, nil)

	// Execute
	result, err := helper.logic.ListAdditionalAddOnIDs(context.Background(), serviceIDs)

	// Assertions
	assert.NoError(t, err)
	assert.Empty(t, result) // 没有共同的 addon
}
