load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "addon",
    srcs = [
        "addon.go",
        "converter.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/addon",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/addon/applicable",
        "//backend/app/offering/logic/organization",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/override",
        "//backend/app/offering/logic/transaction",
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service",
        "//backend/app/offering/repo/db/servicecategory",
        "//backend/app/offering/repo/organization",
        "//backend/common/utils/money",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "addon_test",
    srcs = [
        "addon_test.go",
        "converter_test.go",
    ],
    embed = [":addon"],
    deps = [
        "//backend/app/offering/logic/addon/applicable",
        "//backend/app/offering/logic/organization",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/override",
        "//backend/app/offering/logic/transaction/mocks",
        "//backend/app/offering/repo/db/additional/mocks",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/service",
        "//backend/app/offering/repo/db/service/mocks",
        "//backend/app/offering/repo/db/serviceattribute/mocks",
        "//backend/app/offering/repo/db/servicecategory/mocks",
        "//backend/app/offering/repo/organization/mocks",
        "//backend/app/offering/repo/pet/mocks",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_mock//gomock",
    ],
)
