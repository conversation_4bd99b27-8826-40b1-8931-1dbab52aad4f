package addon

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/applicable"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/organization"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/override"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction/mocks"
	mock7 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	dbservice "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	mock3 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	mock4 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	mock2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory/mocks"
	organizationmocks "github.com/MoeGolibrary/moego/backend/app/offering/repo/organization/mocks"
	mock6 "github.com/MoeGolibrary/moego/backend/app/offering/repo/pet/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl                     *gomock.Controller
	mockQuery                *mocks.MockQuery
	mockServiceRepo          *mock3.MockRepository
	mockCategoryRepo         *mock2.MockRepository
	mockBasicRepo            *mock4.MockRepository
	mockRolloverRepo         *mock4.MockAutoRollOverRepository
	mockLodgingRepo          *mock4.MockLodgingScopeRepository
	mockStaffRepo            *mock4.MockStaffScopeRepository
	mockBusinessScopeRepo    *mock3.MockBusinessScopeRepository
	mockAdditionalRepo       *mock7.MockRepository
	logic                    *Logic
	mockPetRepo              *mock6.MockRepository
	petScopeRepo             *mock3.MockPetScopeRepository
	petWeightRepo            *mock3.MockPetWeightRepository
	mockOrganizationRepo     *organizationmocks.MockRepository
	mockBusinessOverrideRepo *mock3.MockBusinessOverrideRepository
	mockStaffOverrideRepo    *mock3.MockStaffOverrideRepository
	mockPetOverrideRepo      *mock3.MockPetOverrideRepository
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)

	mockQuery := mocks.NewMockQuery()
	mockServiceRepo := mock3.NewMockRepository(ctrl)
	mockCategoryRepo := mock2.NewMockRepository(ctrl)
	mockBasicRepo := mock4.NewMockRepository(ctrl)
	mockRolloverRepo := mock4.NewMockAutoRollOverRepository(ctrl)
	mockLodgingRepo := mock4.NewMockLodgingScopeRepository(ctrl)
	mockStaffRepo := mock4.NewMockStaffScopeRepository(ctrl)
	mockBusinessScopeRepo := mock3.NewMockBusinessScopeRepository(ctrl)
	mockPetRepo := mock6.NewMockRepository(ctrl)
	mockPetScopeRepo := mock3.NewMockPetScopeRepository(ctrl)
	mockPetWeightRepo := mock3.NewMockPetWeightRepository(ctrl)
	mockOrganizationRepo := organizationmocks.NewMockRepository(ctrl)
	mockBusinessOverrideRepo := mock3.NewMockBusinessOverrideRepository(ctrl)
	mockStaffOverrideRepo := mock3.NewMockStaffOverrideRepository(ctrl)
	mockPetOverrideRepo := mock3.NewMockPetOverrideRepository(ctrl)
	mockAdditionalRepo := mock7.NewMockRepository(ctrl)

	// 创建必要的依赖项
	attributeManager := attribute.NewManagerWithRepositories(
		mockServiceRepo, mockBasicRepo, mockRolloverRepo, mockLodgingRepo, mockStaffRepo)
	businessScopeLogic := service.NewBusinessScopeWithRepository(mockBusinessScopeRepo)
	applicableLogic := applicable.NewLogicWithRepository(mockAdditionalRepo, mockServiceRepo)
	availabilityManager := availability.NewManagerWithRepository(mockPetRepo, mockPetScopeRepo, mockPetWeightRepo, mockServiceRepo)
	organizationLogic := organization.NewLogic(mockOrganizationRepo)
	overrideLogic := override.NewManagerWithRepository(mockBusinessOverrideRepo, mockStaffOverrideRepo, mockPetOverrideRepo)

	logic := &Logic{
		query:               mockQuery, // 使用 mock Query
		serviceRepo:         mockServiceRepo,
		categoryRepo:        mockCategoryRepo,
		attributeManager:    attributeManager,
		businessScopeLogic:  businessScopeLogic,
		applicableLogic:     applicableLogic,
		availabilityManager: availabilityManager,
		organizationLogic:   organizationLogic,
		overrideLogic:       overrideLogic,
	}

	return &testHelper{
		ctrl:                     ctrl,
		mockQuery:                mockQuery,
		mockServiceRepo:          mockServiceRepo,
		mockCategoryRepo:         mockCategoryRepo,
		mockBasicRepo:            mockBasicRepo,
		mockRolloverRepo:         mockRolloverRepo,
		mockLodgingRepo:          mockLodgingRepo,
		mockStaffRepo:            mockStaffRepo,
		mockBusinessScopeRepo:    mockBusinessScopeRepo,
		mockAdditionalRepo:       mockAdditionalRepo,
		petScopeRepo:             mockPetScopeRepo,
		petWeightRepo:            mockPetWeightRepo,
		mockOrganizationRepo:     mockOrganizationRepo,
		mockBusinessOverrideRepo: mockBusinessOverrideRepo,
		mockStaffOverrideRepo:    mockStaffOverrideRepo,
		mockPetOverrideRepo:      mockPetOverrideRepo,
		logic:                    logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

// stringPtr 辅助函数，用于创建字符串指针
func stringPtr(s string) *string {
	return &s
}

// setupBuildAvailableAddOnsResponseMocks 设置 buildAvailableAddOnsResponse 方法相关的 mock 期望
func (h *testHelper) setupBuildAvailableAddOnsResponseMocks(addonIDs []int64) {
	// 1. 设置属性相关的 mock 期望
	h.setupListAddOnsAttributeMocks(addonIDs)

	// 2. 设置 attribute manager 中其他处理器的 mock 期望
	// 注意：即使 addonIDs 为空，这些方法仍然会被调用
	h.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return([]*model.ServiceLodgingScope{}, nil)
	h.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return([]*model.ServiceAutoRollover{}, nil)
	h.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return([]*model.ServiceStaffScope{}, nil)

	// 3. 设置 Business overrides 相关的 mock 期望
	// 注意：当 addonIDs 为空时，overrideLogic.ListBusinessOverridesByServiceIDs 会早期返回，不会调用 repository 方法
	if len(addonIDs) > 0 {
		h.mockBusinessOverrideRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return(map[int64][]*model.ServiceBusinessOverride{}, nil)
	}

	// 4. 设置 Pet overrides 相关的 mock 期望
	// 注意：ListPetOverride 方法没有对空的 ServiceIDs 进行早期返回检查，总是会调用 repository 方法
	h.mockPetOverrideRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.ServicePetOverride{}, int64(0), nil)
}

// setupListAddOnsAttributeMocks 设置 ListAddOns 属性相关的 mock 期望
func (h *testHelper) setupListAddOnsAttributeMocks(addonIDs []int64) {
	// 设置属性相关的 mock 期望
	// 注意：即使 addonIDs 为空，这个方法仍然会被调用
	if len(addonIDs) > 0 {
		h.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return([]*model.ServiceAttribute{
			{
				ServiceID:      1,
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "true",
			},
			{
				ServiceID:      1,
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "60",
			},
			{
				ServiceID:      2,
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "false",
			},
			{
				ServiceID:      2,
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "30",
			},
		}, nil)
	} else {
		h.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), addonIDs).Return([]*model.ServiceAttribute{}, nil)
	}
}

// setupCheckNameUniqueMocks 设置 checkNameUnique 的 mock 期望
func (h *testHelper) setupCheckNameUniqueMocks(isDuplicate bool) {
	if isDuplicate {
		// 如果名称重复，返回已存在的记录
		h.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Service{
			{ID: 1, Name: "Existing Name"},
		}, int64(1), nil)
	} else {
		// 如果名称不重复，返回空列表
		h.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Service{}, int64(0), nil)
	}
}

func TestNewLogic(t *testing.T) {
	// 跳过这个测试，因为需要真实的数据库连接
	t.Skip("Skipping TestNewLogic due to database dependency")

	logic := NewLogic()
	assert.NotNil(t, logic)
	assert.NotNil(t, logic.serviceRepo)
	assert.NotNil(t, logic.categoryRepo)
}

func TestCreateAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			SpecificServices: []*offeringpb.SpecificService{
				{
					CareTypeId: lo.ToPtr(int64(1)),
					ServiceIds: []int64{1, 2},
				},
			},
		},
	}

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique - 在 CreateAddOn 之前调用
	helper.setupCheckNameUniqueMocks(false)

	// Mock expectations
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo).AnyTimes()
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2) // 可能被调用两次

	// Mock expectations for additional service
	helper.mockAdditionalRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockAdditionalRepo).AnyTimes()
	helper.mockAdditionalRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes() // 创建默认的 4 个 scope 记录
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes() // 创建默认的 pet weight 记录

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

func TestGetAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for applicable service
	helper.mockAdditionalRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{serviceID}).Return([]*model.AdditionalService{}, nil)
	// GetByAddonID 调用 ListByAddonIDs，当没有 serviceIDs 时不需要 BatchGet

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return(nil, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationId)
	assert.Equal(t, "Test AddOn", result.Name)
	assert.Equal(t, offeringpb.AddOn_ACTIVE, result.Status)
}

func TestGetAddOn_NotAddOnType(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	service := &model.Service{
		ID:   serviceID,
		Type: offeringpb.Service_SERVICE, // Not ADD_ON type
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(service, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)

	grpcErr, ok := status.FromError(err)
	assert.True(t, ok)
	assert.Equal(t, codes.NotFound, grpcErr.Code())
	assert.Contains(t, grpcErr.Message(), "service is not an addon")
}

func TestDeleteAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	helper.mockQuery.ExpectTransactionSuccess()

	// Test data
	serviceID := int64(123)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo)
	helper.mockBusinessScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)

	// Mock expectations for additional service
	helper.mockAdditionalRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockAdditionalRepo)
	helper.mockAdditionalRepo.EXPECT().DeleteByAdditionalServiceID(gomock.Any(), serviceID).Return(nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockRolloverRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockRolloverRepo).AnyTimes()
	helper.mockStaffRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockStaffRepo).AnyTimes()
	helper.mockLodgingRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockLodgingRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID, gomock.Any()).Return(int64(1), nil)
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)

	// Mock expectations for service deletion (最后执行)
	helper.mockServiceRepo.EXPECT().Delete(gomock.Any(), serviceID).Return(nil)

	// Execute
	err := helper.logic.DeleteAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
}

func TestListAddOns_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	services := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn 1",
			Type:             offeringpb.Service_ADD_ON,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn 2",
			Type:             offeringpb.Service_ADD_ON,
		},
	}

	// Mock expectations for service list
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock expectations for attributes - 为每个 service 设置期望
	serviceIDs := []int64{1, 2}

	// Mock 底层的 processor 方法，因为 attributeManager 会调用这些方法
	// Basic attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAttribute{
			{
				ServiceID:      1,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "true",
			},
			{
				ServiceID:      1,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "60",
			},
			{
				ServiceID:      2,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "false",
			},
			{
				ServiceID:      2,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "30",
			},
		}, nil)

	// Auto rollover attributes
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAutoRollover{}, nil)

	// Staff scope attributes
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceStaffScope{}, nil)

	// Lodging scope attributes
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope - 为每个 service 设置期望
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceBusinessScope{
			{ServiceID: 1, IsAllBusiness: true, AvailableBusinessIds: []int64{}},
			{ServiceID: 2, IsAllBusiness: false, AvailableBusinessIds: []int64{1}},
		}, nil)

	// Mock expectations for applicable service
	helper.mockAdditionalRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.AdditionalService{}, nil)
	// 空 AdditionalService 时不需要调用 BatchGet

	// Execute
	result, err := helper.logic.ListAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.AddOns, 2)
	assert.Equal(t, int32(2), result.Total)

	// Verify first addon
	assert.Equal(t, int64(1), result.AddOns[0].Id)
	assert.Equal(t, "AddOn 1", result.AddOns[0].Name)
	assert.True(t, result.AddOns[0].IsRequiredStaff)
	assert.Equal(t, int32(60), result.AddOns[0].Duration)
	assert.True(t, result.AddOns[0].AvailableBusiness.IsAll)
	assert.Nil(t, result.AddOns[0].ApplicableService) // 没有配置时返回 nil

	// Verify second addon
	assert.Equal(t, int64(2), result.AddOns[1].Id)
	assert.Equal(t, "AddOn 2", result.AddOns[1].Name)
	assert.False(t, result.AddOns[1].IsRequiredStaff)
	assert.Equal(t, int32(30), result.AddOns[1].Duration)
	assert.False(t, result.AddOns[1].AvailableBusiness.IsAll)
	assert.Len(t, result.AddOns[1].AvailableBusiness.BusinessIds, 1)
	assert.Equal(t, int64(1), result.AddOns[1].AvailableBusiness.BusinessIds[0])
}

func TestListAddOns_WithApplicableServices(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	services := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn With Applicable Services",
			Type:             offeringpb.Service_ADD_ON,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn Without Applicable Services",
			Type:             offeringpb.Service_ADD_ON,
		},
	}

	// Mock expectations for service list
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock expectations for attributes
	serviceIDs := []int64{1, 2}
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceBusinessScope{
			{ServiceID: 1, IsAllBusiness: true, AvailableBusinessIds: []int64{}},
			{ServiceID: 2, IsAllBusiness: true, AvailableBusinessIds: []int64{}},
		}, nil)

	// Mock expectations for applicable service
	// Addon 1 有 applicable services，包括 service 10, 11（careType 1）和 service 20, 21（careType 2）
	// Addon 2 没有 applicable services
	targetServiceIDs := []int64{10, 11, 20, 21}
	targetServices := []*model.Service{
		{ID: 10, CareTypeID: 1},
		{ID: 11, CareTypeID: 1},
		{ID: 20, CareTypeID: 2},
		{ID: 21, CareTypeID: 2},
	}
	helper.mockServiceRepo.EXPECT().BatchGet(gomock.Any(), targetServiceIDs).Return(targetServices, nil)

	helper.mockAdditionalRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.AdditionalService{
			// Addon 1 的配置
			{ServiceID: 10, AdditionalServiceID: 1}, // careType 1
			{ServiceID: 11, AdditionalServiceID: 1}, // careType 1
			{ServiceID: 20, AdditionalServiceID: 1}, // careType 2
			{ServiceID: 21, AdditionalServiceID: 1}, // careType 2
			// Addon 2 没有配置
		}, nil)

	// Execute
	result, err := helper.logic.ListAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.AddOns, 2)

	// Verify first addon with applicable services
	assert.Equal(t, int64(1), result.AddOns[0].Id)
	assert.Equal(t, "AddOn With Applicable Services", result.AddOns[0].Name)
	assert.NotNil(t, result.AddOns[0].ApplicableService)
	assert.Len(t, result.AddOns[0].ApplicableService.SpecificServices, 2)

	// 验证第一个 careType 的服务
	careType1Services := result.AddOns[0].ApplicableService.SpecificServices
	careType1Found := false
	careType2Found := false
	for _, specSvc := range careType1Services {
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 1 {
			careType1Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(10))
			assert.Contains(t, specSvc.ServiceIds, int64(11))
		}
		if specSvc.CareTypeId != nil && *specSvc.CareTypeId == 2 {
			careType2Found = true
			assert.Contains(t, specSvc.ServiceIds, int64(20))
			assert.Contains(t, specSvc.ServiceIds, int64(21))
		}
	}
	assert.True(t, careType1Found)
	assert.True(t, careType2Found)

	// Verify second addon without applicable services
	assert.Equal(t, int64(2), result.AddOns[1].Id)
	assert.Equal(t, "AddOn Without Applicable Services", result.AddOns[1].Name)
	assert.Nil(t, result.AddOns[1].ApplicableService)
}

func TestBuildListAddOnsFilter(t *testing.T) {
	logic := &Logic{}

	// Test case 1: Basic filter
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
	}

	filter := logic.buildListAddOnsFilter(req)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filter.OrganizationType)
	assert.Equal(t, int64(123), filter.OrganizationID)
	assert.Len(t, filter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filter.Types[0])

	// Test case 2: With filter
	reqWithFilter := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnsRequest_Filter{
			Statuses: []offeringpb.AddOn_Status{offeringpb.AddOn_ACTIVE},
			Sources:  []offeringpb.OfferingSource{offeringpb.OfferingSource_MOEGO},
			Keyword:  lo.ToPtr("test"),
		},
	}

	filterWithFilter := logic.buildListAddOnsFilter(reqWithFilter)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filterWithFilter.OrganizationType)
	assert.Equal(t, int64(123), filterWithFilter.OrganizationID)
	assert.Len(t, filterWithFilter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filterWithFilter.Types[0])
	assert.Len(t, filterWithFilter.Statuses, 1)
	assert.Equal(t, offeringpb.Service_ACTIVE, filterWithFilter.Statuses[0])
	assert.Len(t, filterWithFilter.Sources, 1)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, filterWithFilter.Sources[0])
	assert.Equal(t, "test", *filterWithFilter.Keyword)
}

func TestGetPaginationParams(t *testing.T) {
	logic := &Logic{}

	// Test case 1: With pagination
	req := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 10,
			Limit:  20,
		},
	}

	pagination := logic.getPaginationParams(req)
	assert.Equal(t, int32(10), pagination.Offset)
	assert.Equal(t, int32(20), pagination.Limit)

	// Test case 2: Without pagination (should return default)
	reqNoPagination := &offeringpb.ListAddOnsRequest{}

	paginationDefault := logic.getPaginationParams(reqNoPagination)
	assert.Equal(t, int32(0), paginationDefault.Offset)
	assert.Equal(t, int32(200), paginationDefault.Limit)
}

func TestUpdateAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - 只包含基本字段，不包含复杂的属性、业务范围和关联服务
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:   123,
		Name: lo.ToPtr("Updated AddOn"),
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for Get (UpdateAddOn 开始时调用)
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(123)).Return(&model.Service{
		ID:               123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Old AddOn",
		Type:             offeringpb.Service_ADD_ON,
	}, nil)

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Mock expectations for service update only
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope (因为 AvailableBusiness 不为 nil)
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo)
	helper.mockBusinessScopeRepo.EXPECT().UpdateByServiceID(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability (Update 方法总是会调用 availabilityManager.Update)
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes() // 创建默认的 4 个 scope 记录
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).AnyTimes()
	helper.petWeightRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes() // 创建默认的 pet weight 记录

	// Execute
	err := helper.logic.UpdateAddOn(context.Background(), updateDef)

	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Test data
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns: []*offeringpb.AddOnUpdateDef{
			{
				Id:     1,
				Status: lo.ToPtr(offeringpb.AddOn_INACTIVE),
			},
			{
				Id:     2,
				Status: lo.ToPtr(offeringpb.AddOn_ACTIVE),
			},
		},
	}

	// Mock expectations for service list (获取需要更新的 addon 列表)
	services := []*model.Service{
		{ID: 1, Type: offeringpb.Service_ADD_ON},
		{ID: 2, Type: offeringpb.Service_ADD_ON},
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock expectations for service updates
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo).Times(2)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)
	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_EmptyUpdates(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Test data - 空的更新列表
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns:     []*offeringpb.AddOnUpdateDef{},
	}

	// Mock expectations for service list (空的更新列表不需要查询)
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]*model.Service{}, int64(0), nil)

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_UpdateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Test data
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns: []*offeringpb.AddOnUpdateDef{
			{
				Id:     1,
				Status: lo.ToPtr(offeringpb.AddOn_INACTIVE),
			},
		},
	}

	// Mock expectations for service list (获取需要更新的 addon 列表)
	services := []*model.Service{
		{ID: 1, Type: offeringpb.Service_ADD_ON},
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(1), nil)

	// Mock expectations for service update - 模拟更新失败
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(fmt.Errorf("update failed"))

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "update failed")
}

func TestCreateAddOn_ServiceCreateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique - 在 CreateAddOn 之前调用
	helper.setupCheckNameUniqueMocks(false)

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
	}

	// Mock expectations - 模拟服务创建失败
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("service create failed"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "service create failed")
}

func TestCreateAddOn_AttributesError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique - 在 CreateAddOn 之前调用
	helper.setupCheckNameUniqueMocks(false)

	// Test data - 只包含基本字段，不包含复杂的属性、业务范围和关联服务
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})

	// Mock expectations for service update (更新 sort 字段)
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes (因为 saveAddOnAttributes 总是被调用，这里会失败)
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("attribute create failed"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "attribute create failed")
}

func TestGetAddOn_ServiceNotFound(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)

	// Mock expectations - 模拟服务不存在
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(nil, fmt.Errorf("service not found"))

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "service not found")
}

func TestBuildListAddOnsFilter_WithAllFilters(t *testing.T) {
	logic := &Logic{}

	// Test case: 包含所有过滤条件
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnsRequest_Filter{
			Statuses: []offeringpb.AddOn_Status{offeringpb.AddOn_ACTIVE, offeringpb.AddOn_INACTIVE},
			Sources: []offeringpb.OfferingSource{
				offeringpb.OfferingSource_MOEGO, offeringpb.OfferingSource_ENTERPRISE},
			Keyword:     lo.ToPtr("test keyword"),
			CategoryIds: []int64{1, 2, 3},
		},
	}

	filter := logic.buildListAddOnsFilter(req)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filter.OrganizationType)
	assert.Equal(t, int64(123), filter.OrganizationID)
	assert.Len(t, filter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filter.Types[0])
	assert.Len(t, filter.Statuses, 2)
	assert.Len(t, filter.Sources, 2)
	assert.Equal(t, "test keyword", *filter.Keyword)
	// CategoriesIDs 字段目前没有被实现，所以不进行断言
}

// TestCreateAddOn_WithPetAvailability 测试创建 AddOn 时包含 Pet availability 配置
func TestCreateAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique - 在 CreateAddOn 之前调用
	helper.setupCheckNameUniqueMocks(false)

	// Test data with Pet availability
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn with Pet Availability",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			SpecificServices: []*offeringpb.SpecificService{},
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		AvailablePetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2},
		},
		AvailableCoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1},
		},
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// 注意：ApplicableService 的 SpecificServices 为空，所以不会调用 BatchCreate

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil) // 创建默认的 4 个 scope 记录
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil) // 创建默认的 pet weight 记录

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

// TestCreateAddOn_WithApplicableService 测试创建 AddOn 时包含 ApplicableService 配置
func TestCreateAddOn_WithApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Test data with ApplicableService
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn with ApplicableService",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  false,
		Duration:         30,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			SpecificServices: []*offeringpb.SpecificService{
				{
					CareTypeId: lo.ToPtr(int64(1)),
				},
			},
		},
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// 注意：ApplicableService 的 ServiceIds 为空，所以不会调用 BatchCreate

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil) // 创建默认的 4 个 scope 记录
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil) // 创建默认的 pet weight 记录

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

// TestUpdateAddOn_WithPetAvailability 测试更新 AddOn 时包含 Pet availability 配置
func TestUpdateAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for Get (UpdateAddOn 开始时调用)
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), int64(123)).Return(&model.Service{
		ID:               123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Old AddOn",
		Type:             offeringpb.Service_ADD_ON,
	}, nil)

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Test data with Pet availability
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:   123,
		Name: lo.ToPtr("Updated AddOn with Pet Availability"),
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       false,
			BusinessIds: []int64{1, 2},
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
		AvailablePetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2, 3},
		},
		AvailableCoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1, 2},
		},
	}

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().UpdateByServiceID(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability (Update 方法总是会调用 availabilityManager.Update)
	// Update 方法会先删除现有记录，然后调用 Save 方法保存新记录
	// 所以 DeleteByServiceID 会被调用两次：一次在 Update 中，一次在 Save 中
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).AnyTimes()
	helper.petWeightRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes() // 创建默认的 pet weight 记录

	// Execute
	err := helper.logic.UpdateAddOn(context.Background(), updateDef)

	// Assertions
	assert.NoError(t, err)
}

// TestGetAddOn_WithPetAvailability 测试获取 AddOn 时包含 Pet availability 配置
func TestGetAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn with Pet Availability",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{
			{
				ServiceID:      serviceID,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "true",
			},
			{
				ServiceID:      serviceID,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "60",
			},
		}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for applicable service
	helper.mockAdditionalRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{serviceID}).Return([]*model.AdditionalService{}, nil)
	// GetByAddonID 调用 ListByAddonIDs，当没有 serviceIDs 时不需要 BatchGet

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return([]*model.ServicePetAvailabilityScope{
		{
			ServiceID: serviceID,
			ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_TYPE,
			TargetID:  1,
		},
		{
			ServiceID: serviceID,
			ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
			TargetID:  1,
		},
	}, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{
		{
			ServiceID:  serviceID,
			IsAllRange: true,
		},
	}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, "Test AddOn with Pet Availability", result.Name)
	assert.True(t, result.IsRequiredStaff)
	assert.Equal(t, int32(60), result.Duration)
	assert.NotNil(t, result.AvailableTypeBreed)
	assert.NotNil(t, result.AvailablePetSize)
	assert.NotNil(t, result.AvailableCoatType)
}

// TestCreateAddOn_BusinessScopeError 测试创建 AddOn 时 business scope 错误
func TestCreateAddOn_BusinessScopeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope - 模拟错误
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("business scope error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "business scope error")
}

// TestCreateAddOn_ApplicableServiceError 测试创建 AddOn 时 applicable service 错误
func TestCreateAddOn_ApplicableServiceError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			SpecificServices: []*offeringpb.SpecificService{
				{
					CareTypeId: lo.ToPtr(int64(1)),
					ServiceIds: []int64{10, 20}, // 添加 ServiceIds 以便触发 BatchCreate
				},
			},
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for applicable service - 模拟错误
	helper.mockAdditionalRepo.EXPECT().WithTX(gomock.Any()).Return(helper.mockAdditionalRepo).AnyTimes()
	helper.mockAdditionalRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(fmt.Errorf("applicable service error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "applicable service error")
}

// TestCreateAddOn_PetAvailabilityError 测试创建 AddOn 时 pet availability 错误
func TestCreateAddOn_PetAvailabilityError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Mock expectations for transaction
	helper.mockQuery.ExpectTransactionSuccess()

	// Mock expectations for checkNameUnique
	helper.setupCheckNameUniqueMocks(false)

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockServiceRepo)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBasicRepo).AnyTimes()
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.mockBusinessScopeRepo).AnyTimes()
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability - 模拟错误
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil).AnyTimes()
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(fmt.Errorf("pet availability error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "failed to batch create pet availability scopes")
}

// TestGetAddOn_WithApplicableService 测试获取 AddOn 时包含 ApplicableService 配置
func TestGetAddOn_WithApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn with ApplicableService",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for applicable service
	// 由于 applicableLogic 使用真实的数据库操作，这里不需要 mock
	// Mock expectations for applicable service
	helper.mockAdditionalRepo.EXPECT().ListByAdditionalServiceIDs(gomock.Any(), []int64{serviceID}).Return([]*model.AdditionalService{}, nil)
	// GetByAddonID 调用 ListByAddonIDs，当没有 serviceIDs 时不需要 BatchGet

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return([]*model.ServicePetAvailabilityScope{}, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, "Test AddOn with ApplicableService", result.Name)
	// ApplicableService 可能为 nil（没有配置时）
}

// TestGetPaginationParams_EdgeCases 测试分页参数的边界情况
func TestGetPaginationParams_EdgeCases(t *testing.T) {
	logic := &Logic{}

	// Test case 1: 零值分页
	req1 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  0,
		},
	}

	pagination1 := logic.getPaginationParams(req1)
	assert.Equal(t, int32(0), pagination1.Offset)
	assert.Equal(t, int32(0), pagination1.Limit)

	// Test case 2: 大值分页
	req2 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 1000,
			Limit:  1000,
		},
	}

	pagination2 := logic.getPaginationParams(req2)
	assert.Equal(t, int32(1000), pagination2.Offset)
	assert.Equal(t, int32(1000), pagination2.Limit)

	// Test case 3: 负数分页（应该被处理为默认值）
	req3 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: -10,
			Limit:  -5,
		},
	}

	pagination3 := logic.getPaginationParams(req3)
	assert.Equal(t, int32(-10), pagination3.Offset) // 注意：这里没有验证逻辑，只是测试当前行为
	assert.Equal(t, int32(-5), pagination3.Limit)
}

// ==================== ListAvailableAddOns 测试 ====================

func TestLogic_ListAvailableAddOns_Basic(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟 addon 列表
	now := time.Now()
	mockAddOns := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "Available AddOn 1",
			Description:      stringPtr("Description 1"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       3,
			Name:             "Available AddOn 2",
			Description:      stringPtr("Description 2"),
			ColorCode:        "#00FF00",
			Sort:             2,
			Images:           []string{"image2.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置 mock 期望 - 没有上下文参数，直接查询 addon
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), req.Pagination).Return(mockAddOns, int64(2), nil)

	// 设置 buildAvailableAddOnsResponse 相关的 mock 期望
	helper.setupBuildAvailableAddOnsResponseMocks([]int64{1, 2})

	// 执行测试
	result, err := helper.logic.ListAvailableAddOns(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(2), result.Total)
	assert.Len(t, result.AddOns, 2)
	assert.Equal(t, "Available AddOn 1", result.AddOns[0].Name)
	assert.Equal(t, "Available AddOn 2", result.AddOns[1].Name)
	assert.Equal(t, req.Pagination, result.Pagination)
}

func TestLogic_ListAvailableAddOns_WithoutContext(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 不包含上下文参数，避免触发可用性检查
	req := &offeringpb.ListAvailableAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		// 移除 Context 参数，这样 shouldApplyAvailabilityChecks 会返回 false
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 模拟 addon 列表
	now := time.Now()
	mockAddOns := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Available AddOn 1",
			Status:           offeringpb.Service_ACTIVE,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置 mock 期望 - 直接查询 addon
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), req.Pagination).Return(mockAddOns, int64(1), nil)

	// 设置 buildAvailableAddOnsResponse 相关的 mock 期望
	helper.setupBuildAvailableAddOnsResponseMocks([]int64{1})

	// 执行测试
	result, err := helper.logic.ListAvailableAddOns(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(1), result.Total)
	assert.Len(t, result.AddOns, 1)
}

func TestLogic_ListAvailableAddOns_NoAvailableAddOns(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 返回空结果
	req := &offeringpb.ListAvailableAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 设置 mock 期望 - 返回空列表
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), req.Pagination).Return([]*model.Service{}, int64(0), nil)

	// 即使 addon 列表为空，buildAvailableAddOnsResponse 仍然会调用相关方法
	// 需要为空的服务ID列表设置 mock 期望
	helper.setupBuildAvailableAddOnsResponseMocks([]int64{})

	// 执行测试
	result, err := helper.logic.ListAvailableAddOns(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(0), result.Total)
	assert.Len(t, result.AddOns, 0)
	assert.Equal(t, req.Pagination, result.Pagination)
}

func TestLogic_ListAvailableAddOns_RepositoryError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	req := &offeringpb.ListAvailableAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
	}

	// 模拟数据库错误
	expectedErr := errors.New("database connection failed")
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), nil).Return(nil, int64(0), expectedErr)

	// 执行测试
	result, err := helper.logic.ListAvailableAddOns(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

func TestLogic_ListAvailableAddOns_OrganizationInfoError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 使用 BUSINESS 类型，会触发组织信息转换
	req := &offeringpb.ListAvailableAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_BUSINESS,
		OrganizationId:   456,
	}

	// 模拟组织信息转换错误
	expectedErr := errors.New("business not found")
	helper.mockOrganizationRepo.EXPECT().GetCompanyIdByBusinessId(gomock.Any(), int64(456)).Return(int64(0), expectedErr)

	// 执行测试
	result, err := helper.logic.ListAvailableAddOns(context.Background(), req)

	// 验证结果
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)
}

// ==================== buildAvailableAddOnsResponse 测试 ====================

func TestLogic_buildAvailableAddOnsResponse_Basic(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据
	now := time.Now()
	addons := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			CategoryID:       2,
			Name:             "AddOn 1",
			Description:      stringPtr("Description 1"),
			ColorCode:        "#FF0000",
			Sort:             1,
			Images:           []string{"image1.jpg"},
			Source:           offeringpb.OfferingSource_MOEGO,
			Status:           offeringpb.Service_ACTIVE,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	// 设置 mock 期望
	helper.setupBuildAvailableAddOnsResponseMocks([]int64{1})

	// 执行测试
	result, err := helper.logic.buildAvailableAddOnsResponse(context.Background(), addons)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), result[0].Id)
	assert.Equal(t, "AddOn 1", result[0].Name)
	assert.True(t, result[0].IsRequiredStaff)
	assert.Equal(t, int32(60), result[0].Duration)
}

func TestLogic_buildAvailableAddOnsResponse_EmptyAddOns(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// 准备测试数据 - 空 addon 列表
	addons := []*model.Service{}

	// 即使 addon 列表为空，buildAvailableAddOnsResponse 仍然会调用相关方法
	// 需要为空的服务ID列表设置 mock 期望
	helper.setupBuildAvailableAddOnsResponseMocks([]int64{})

	// 执行测试
	result, err := helper.logic.buildAvailableAddOnsResponse(context.Background(), addons)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 0)
}

// ==================== shouldApplyAvailabilityChecks 测试 ====================

func TestLogic_shouldApplyAvailabilityChecks(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	t.Run("无上下文对象", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.False(t, result)
	})

	t.Run("有StaffId", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Context: &offeringpb.ListAvailableAddOnsRequest_AvailabilityContext{
				StaffId: lo.ToPtr(int64(1)),
			},
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.True(t, result)
	})

	t.Run("有PetIds", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Context: &offeringpb.ListAvailableAddOnsRequest_AvailabilityContext{
				PetIds: []int64{1, 2},
			},
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.True(t, result)
	})

	t.Run("有ServiceIds", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Context: &offeringpb.ListAvailableAddOnsRequest_AvailabilityContext{
				ServiceIds: []int64{1, 2},
			},
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.True(t, result)
	})

	t.Run("BUSINESS类型", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_BUSINESS,
			OrganizationId:   123,
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.True(t, result)
	})

	t.Run("空的上下文对象", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Context:          &offeringpb.ListAvailableAddOnsRequest_AvailabilityContext{},
		}

		result := helper.logic.shouldApplyAvailabilityChecks(req)
		assert.False(t, result)
	})
}

func TestLogic_buildEmptyAvailableAddOnsResponse(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	t.Run("有分页信息", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
			Pagination: &offeringpb.PaginationRef{
				Offset: 10,
				Limit:  20,
			},
		}

		result := helper.logic.buildEmptyAvailableAddOnsResponse(req)

		assert.NotNil(t, result)
		assert.Equal(t, int32(0), result.Total)
		assert.Len(t, result.AddOns, 0)
		assert.Equal(t, req.Pagination, result.Pagination)
	})

	t.Run("无分页信息", func(t *testing.T) {
		req := &offeringpb.ListAvailableAddOnsRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   123,
		}

		result := helper.logic.buildEmptyAvailableAddOnsResponse(req)

		assert.NotNil(t, result)
		assert.Equal(t, int32(0), result.Total)
		assert.Len(t, result.AddOns, 0)
		assert.Nil(t, result.Pagination)
	})
}
