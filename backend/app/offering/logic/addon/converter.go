package addon

import (
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/common/utils/money"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ServiceModelToProto converts Service model directly to AddOn proto.
func ServiceModelToProto(m *model.Service) *offeringpb.AddOn {
	if m == nil {
		return nil
	}

	pb := &offeringpb.AddOn{
		Id:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationId:   m.OrganizationID,
		CategoryId:       &m.CategoryID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           offeringpb.AddOn_Status(m.Status),
		IsDeleted:        m.DeleteTime != nil,
		Price:            money.FromDecimal(m.<PERSON>mount, m.PriceCurrency),
		TaxId:            m.TaxID,
		CreateTime:       timestamppb.New(lo.FromPtr(m.CreateTime)),
		UpdateTime:       timestamppb.New(lo.FromPtr(m.UpdateTime)),
		DeleteTime:       timestamppb.New(lo.FromPtr(m.DeleteTime)),
	}

	return pb
}

// CreateDefToServiceModel converts AddOnCreateDef directly to Service model.
func CreateDefToServiceModel(pb *offeringpb.AddOnCreateDef) *model.Service {
	if pb == nil {
		return nil
	}

	addon := &model.Service{
		OrganizationType: pb.GetOrganizationType(),
		OrganizationID:   pb.GetOrganizationId(),
		CategoryID:       pb.GetCategoryId(),
		Name:             pb.GetName(),
		Description:      pb.Description,
		ColorCode:        pb.GetColorCode(),
		Images:           pb.GetImages(),
		Source:           pb.GetSource(),
		Status:           offeringpb.Service_Status(pb.GetStatus()),
		Type:             offeringpb.Service_ADD_ON, // 标记为 AddOn 类型
		TaxID:            pb.GetTaxId(),
	}

	if pb.Price != nil {
		addon.PriceAmount = money.ToDecimal(pb.GetPrice())
		addon.PriceCurrency = pb.GetPrice().GetCurrencyCode()
	}

	return addon
}

// UpdateDefToServiceModel converts AddOnUpdateDef directly to Service model.
func UpdateDefToServiceModel(pb *offeringpb.AddOnUpdateDef) *model.Service {
	if pb == nil {
		return nil
	}

	m := &model.Service{
		ID: pb.GetId(),
	}

	if pb.CategoryId != nil {
		m.CategoryID = pb.GetCategoryId()
	}
	if pb.Name != nil {
		m.Name = pb.GetName()
	}
	if pb.Description != nil {
		m.Description = pb.Description
	}
	if pb.ColorCode != nil {
		m.ColorCode = pb.GetColorCode()
	}
	if pb.Sort != nil {
		m.Sort = pb.GetSort()
	}
	if pb.Images != nil {
		m.Images = pb.GetImages()
	}
	if pb.Status != nil {
		m.Status = offeringpb.Service_Status(pb.GetStatus())
	}
	if pb.Price != nil {
		m.PriceAmount = money.ToDecimal(pb.GetPrice())
		m.PriceCurrency = pb.GetPrice().GetCurrencyCode()
	}
	if pb.TaxId != nil {
		m.TaxID = pb.GetTaxId()
	}

	return m
}
