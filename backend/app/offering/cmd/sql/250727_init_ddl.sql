CREATE TABLE care_type
(
    id                BIGSERIAL PRIMARY KEY,

    organization_type VARCHAR(50) NOT NULL DEFAULT '',
    organization_id   BIGINT      NOT NULL DEFAULT 0,

    name              VARCHAR(50) NOT NULL DEFAULT '',
    care_category     VARCHAR(50) NOT NULL DEFAULT '',
    description       VARCHAR(512)         DEFAULT NULL,

    create_time       TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time       TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time       TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (organization_type, organization_id, name)
);

-- === Column Comments ===
COMMENT ON TABLE care_type IS 'Defines a Care Type available to an organization level (enterprise, company, business)';
COMMENT ON COLUMN care_type.id IS 'Primary key ID of the care type';
COMMENT ON COLUMN care_type.organization_type IS 'Level of the organization: ENTERPRISE, COMPANY, BUSINESS, etc.';
COMMENT ON COLUMN care_type.organization_id IS 'ID of the organization unit corresponding to the organization_type';
COMMENT ON COLUMN care_type.name IS 'Name of the care type, unique within the same organization';
COMMENT ON COLUMN care_type.care_category IS 'System code of the care category (e.g. BOARDING, DAYCARE, GROOMING, ..., CUSTOM)';
COMMENT ON COLUMN care_type.description IS 'Optional description of the care type';


CREATE TABLE care_type_attribute
(
    id            BIGSERIAL PRIMARY KEY,

    care_type_id  BIGINT       NOT NULL DEFAULT 0,

    attribute_key VARCHAR(255) NOT NULL DEFAULT '',
    field_name    VARCHAR(255) NOT NULL DEFAULT '',
    label         TEXT,
    value_type    VARCHAR(50)  NOT NULL DEFAULT '',
    options       JSONB,
    description   TEXT,
    is_required   BOOLEAN      NOT NULL DEFAULT FALSE,
    default_value TEXT         NOT NULL DEFAULT '',

    create_time   TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time   TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time   TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (care_type_id, attribute_key)
);

-- === Column Comments ===
COMMENT ON TABLE care_type_attribute IS 'Attribute definition assigned to a specific care type';

COMMENT ON COLUMN care_type_attribute.id IS 'Primary key ID of the care type attribute entry';
COMMENT ON COLUMN care_type_attribute.care_type_id IS 'Care type to which this attribute is assigned';

COMMENT ON COLUMN care_type_attribute.attribute_key IS 'Unique name (key) of the attribute within the care type (e.g., DURATION, MAX_DURATION)';
COMMENT ON COLUMN care_type_attribute.field_name IS 'The field name used in frontend JSON payloads for this attribute (e.g., petType, petSize)';
COMMENT ON COLUMN care_type_attribute.label IS 'Display label for frontend UI (e.g., Pet Size)';
COMMENT ON COLUMN care_type_attribute.value_type IS 'Value type of the attribute (STRING, NUMBER, BOOLEAN, ENUM)';
COMMENT ON COLUMN care_type_attribute.options IS 'JSON list of options (used when value_type is ENUM or BOOLEAN)';
COMMENT ON COLUMN care_type_attribute.description IS 'Optional explanation or usage hint';
COMMENT ON COLUMN care_type_attribute.is_required IS 'Whether this attribute is required for the care type';
COMMENT ON COLUMN care_type_attribute.default_value IS 'Default value assigned to this attribute';

create table service_category
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type VARCHAR(255) NOT NULL DEFAULT '',
    organization_id   BIGINT       NOT NULL DEFAULT 0,
    care_type_id      BIGINT       NOT NULL DEFAULT 0,
    name              VARCHAR(150) NOT NULL DEFAULT '',
    sort              BIGINT       NOT NULL DEFAULT 0,
    type              VARCHAR(50)  NOT NULL DEFAULT '',
    create_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time       TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (organization_type, organization_id, name)
);

-- === Column Comments ===
COMMENT ON TABLE service_category IS 'Service category';
COMMENT ON COLUMN service_category.id IS 'Primary key ID of the service category';
COMMENT ON COLUMN service_category.organization_type IS 'Level of the organization: ENTERPRISE, COMPANY, BUSINESS, etc.';
COMMENT ON COLUMN service_category.organization_id IS 'Organization ID corresponding to the type';
COMMENT ON COLUMN service_category.care_type_id IS 'Care type ID corresponding to the service category';
COMMENT ON COLUMN service_category.name IS 'Name of the service category';
COMMENT ON COLUMN service_category.sort IS 'Sort order for UI display';
COMMENT ON COLUMN service_category.type IS 'The type of service category, SERVICE, ADDON';

CREATE TABLE service
(
    id                BIGSERIAL PRIMARY KEY,

    organization_type VARCHAR(50)  NOT NULL DEFAULT '',
    organization_id   BIGINT       NOT NULL DEFAULT 0,

    type              VARCHAR(50)  NOT NULL DEFAULT '',
    care_type_id      BIGINT       NOT NULL DEFAULT 0,
    category_id       BIGINT       NOT NULL DEFAULT 0,

    name              VARCHAR(255) NOT NULL DEFAULT '',
    description       TEXT,
    color_code        VARCHAR(7)   NOT NULL DEFAULT '',
    sort              BIGINT       NOT NULL DEFAULT 0,
    images            VARCHAR(255)[]        DEFAULT '{}',

    source            VARCHAR(50)  NOT NULL DEFAULT '',
    status            VARCHAR(50)  NOT NULL DEFAULT '',
    
    price_amount      DECIMAL(15,2) NOT NULL DEFAULT 0,
    price_currency    VARCHAR(3)    NOT NULL DEFAULT '',
    tax_id            BIGINT        NOT NULL DEFAULT 0,

    create_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time       TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (organization_type, organization_id, type, care_type_id, name)
);

-- === Column Comments ===
COMMENT ON TABLE service IS 'Service definition used to configure reusable service logic';

COMMENT ON COLUMN service.id IS 'Primary key ID of the service';
COMMENT ON COLUMN service.organization_type IS 'Level of the organization: ENTERPRISE, COMPANY, BUSINESS, etc.';
COMMENT ON COLUMN service.organization_id IS 'Organization ID corresponding to the type';
COMMENT ON COLUMN service.type IS 'The type of service, SERVICE, ADDON';
COMMENT ON COLUMN service.care_type_id IS 'Reference to care_type used in this service';
COMMENT ON COLUMN service.category_id IS 'Optional category to organize services';

COMMENT ON COLUMN service.name IS 'Name of the service, unique within the same organization';
COMMENT ON COLUMN service.description IS 'Optional description of the service';
COMMENT ON COLUMN service.color_code IS 'Color code for UI display, such as #F15A2B';
COMMENT ON COLUMN service.sort IS 'Sort order for UI display';
COMMENT ON COLUMN service.images IS 'List of image URLs in JSON array';

COMMENT ON COLUMN service.source IS 'Source of the service: MOEGO, ENTERPRISE_HUB';
COMMENT ON COLUMN service.status IS 'Status of the service: ACTIVE, INACTIVE';
COMMENT ON COLUMN service.price_amount IS 'Price amount of the service';
COMMENT ON COLUMN service.price_currency IS 'Currency code of the service price (e.g., USD, EUR)';
COMMENT ON COLUMN service.tax_id IS 'Tax ID associated with the service';

-- === Recommended Indexes ===
CREATE INDEX idx_care_type
    ON service (care_type_id);


CREATE TABLE service_attribute
(
    id              BIGSERIAL PRIMARY KEY,

    service_id      BIGINT       NOT NULL DEFAULT 0,
    field_name      VARCHAR(255) NOT NULL DEFAULT '',
    attribute_key   VARCHAR(255) NOT NULL DEFAULT '',
    attribute_value TEXT         NOT NULL,

    create_time     TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time     TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time     TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (service_id, attribute_key)
);

-- === Column Comments ===
COMMENT ON COLUMN service_attribute.id IS 'Primary key ID of the service attribute value';
COMMENT ON COLUMN service_attribute.service_id IS 'Reference to the service';
COMMENT ON COLUMN service_attribute.attribute_key IS 'Unique name (key) of the attribute within the care type (e.g., DURATION, MAX_DURATION)';
COMMENT ON COLUMN service_attribute.attribute_value IS 'Concrete value assigned to the attribute for this service-care_type pair';

drop table service_auto_rollover;

-- Defines the additional services config for services.
CREATE TABLE additional_service
(
    id                      BIGSERIAL PRIMARY KEY,

    organization_type       VARCHAR(50) NOT NULL DEFAULT '',
    organization_id         BIGINT      NOT NULL DEFAULT 0,
    service_id              BIGINT      NOT NULL DEFAULT 0,
    additional_service_id   BIGINT      NOT NULL DEFAULT 0,
    additional_care_type_id BIGINT      NOT NULL DEFAULT 0,

    create_time             TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time             TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time             TIMESTAMPTZ          DEFAULT NULL
);

-- === Column Comments ===
COMMENT ON TABLE additional_service IS 'Defines the association between services and addons.';
COMMENT ON COLUMN additional_service.service_id IS 'The ID of the **Service** that the rule is configured FOR.';
COMMENT ON COLUMN additional_service.additional_service_id IS 'Rule Type 1: The ID of a specific service/addon that is being linked TO.';
COMMENT ON COLUMN additional_service.additional_care_type_id IS 'Rule Type 2: The ID of a care type. Links to all services within this care type.';

-- === Indexes ===
CREATE INDEX idx_additional_service_organization
    ON additional_service (organization_type, organization_id);

CREATE UNIQUE INDEX idx_additional_service
    ON additional_service (additional_service_id, service_id)
    WHERE additional_service_id != 0;

CREATE UNIQUE INDEX idx_additional_care_type
    ON additional_service (service_id, additional_care_type_id)
    WHERE additional_care_type_id != 0;


-- === Service Auto Rollover ===
CREATE TABLE service_auto_rollover
(
    id                BIGSERIAL PRIMARY KEY,

    service_id        BIGINT  NOT NULL DEFAULT 0,
    enabled           BOOLEAN NOT NULL DEFAULT FALSE,
    target_service_id BIGINT  NOT NULL DEFAULT 0,
    after_minute      INT     NOT NULL DEFAULT 0,

    create_time       TIMESTAMPTZ      DEFAULT now(),
    update_time       TIMESTAMPTZ      DEFAULT now(),
    delete_time       TIMESTAMPTZ      DEFAULT NULL,

    UNIQUE (service_id, target_service_id)
);

-- === Column Comments ===
COMMENT ON TABLE service_auto_rollover IS 'Configuration for automatically rolling over a service to another if not completed after a specified duration';
COMMENT ON COLUMN service_auto_rollover.id IS 'Primary key ID of the service auto rollover configuration';
COMMENT ON COLUMN service_auto_rollover.enabled IS 'Whether auto rollover is enabled';
COMMENT ON COLUMN service_auto_rollover.service_id IS 'ID of the service to monitor for rollover';
COMMENT ON COLUMN service_auto_rollover.target_service_id IS 'Target service to switch to when rollover condition is met';
COMMENT ON COLUMN service_auto_rollover.after_minute IS 'Number of minutes after the max duration to trigger auto rollover';

-- === Service Scope Tables ===

CREATE TABLE service_business_scope
(
    id                     BIGSERIAL PRIMARY KEY,
    organization_type      VARCHAR(50) NOT NULL DEFAULT '',
    organization_id        BIGINT      NOT NULL DEFAULT 0,
    service_id             BIGINT      NOT NULL DEFAULT 0,
    is_all_business        BOOLEAN     NOT NULL DEFAULT FALSE,
    available_business_ids BIGINT[]             DEFAULT '{}',
    create_time            TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time            TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time            TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (service_id)
);

-- === Comments ===
COMMENT ON TABLE service_business_scope IS 'Business scope for a service';
COMMENT ON COLUMN service_business_scope.available_business_ids IS 'List of business IDs that can use this service';

CREATE TABLE service_staff_scope
(
    id                  BIGSERIAL PRIMARY KEY,
    organization_type   VARCHAR(50) NOT NULL DEFAULT '',
    organization_id     BIGINT      NOT NULL DEFAULT 0,
    service_id          BIGINT      NOT NULL DEFAULT 0,
    is_all_staff        BOOLEAN     NOT NULL DEFAULT FALSE,
    available_staff_ids BIGINT[]             DEFAULT '{}',
    create_time         TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time         TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time         TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (service_id)
);

-- === Comments ===
COMMENT ON TABLE service_staff_scope IS 'Staff scope for a service';
COMMENT ON COLUMN service_staff_scope.available_staff_ids IS 'List of staff IDs that can use this service';

CREATE TABLE service_lodging_scope
(
    id                         BIGSERIAL PRIMARY KEY,
    organization_type          VARCHAR(50) NOT NULL DEFAULT '',
    organization_id            BIGINT      NOT NULL DEFAULT 0,
    service_id                 BIGINT      NOT NULL DEFAULT 0,
    is_all_lodging_type        BOOLEAN     NOT NULL DEFAULT FALSE,
    available_lodging_type_ids BIGINT[]             DEFAULT '{}',
    create_time                TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time                TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time                TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (service_id)
);

-- === Comments ===
COMMENT ON TABLE service_lodging_scope IS 'Lodging scope for a service';
COMMENT ON COLUMN service_lodging_scope.available_lodging_type_ids IS 'List of lodging type IDs that can use this service';