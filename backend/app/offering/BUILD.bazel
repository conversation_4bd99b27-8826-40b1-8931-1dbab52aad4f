load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "offering_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/offering/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/offering/inner",
        "//backend/proto/offering/v1:offering",
    ],
)

go_binary(
    name = "offering",
    embed = [":offering_lib"],
    visibility = ["//visibility:public"],
)
