package additional

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
)

const (
	batchSize = 100
)

//go:generate mockgen -package=mock -destination=mocks/mock_additional_repo.go . Repository
type Repository interface {
	WithTX(q *query.Query) Repository

	BatchCreate(ctx context.Context, scopes []*model.AdditionalService) error
	ListByServiceID(ctx context.Context, serviceID int64) ([]*model.AdditionalService, error)
	ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.AdditionalService, error)
	ListByAdditionalServiceID(ctx context.Context, additionalServiceID int64) ([]*model.AdditionalService, error)
	ListByAdditionalServiceIDs(ctx context.Context, additionalServiceIDs []int64) ([]*model.AdditionalService, error)
	DeleteByServiceID(ctx context.Context, serviceID int64) error
	DeleteByAdditionalServiceID(ctx context.Context, additionalServiceID int64) error
}

// repository implements the data access logic for AdditionalService.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// NewRepositoryWithQuery creates a new repository with a custom query.
func NewRepositoryWithQuery(query *query.Query) Repository {
	return &repository{
		query: query,
	}
}

// WithTX sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithTX(query *query.Query) Repository {
	return NewRepositoryWithQuery(query)
}

// ==================== 通用方法 ====================

// BatchCreate creates multiple additional service configurations.
func (r *repository) BatchCreate(
	ctx context.Context, models []*model.AdditionalService) error {
	if len(models) == 0 {
		return nil
	}

	return r.query.AdditionalService.WithContext(ctx).CreateInBatches(models, batchSize)
}

// ListByServiceID gets all additional service configurations by service ID.
func (r *repository) ListByServiceID(
	ctx context.Context, serviceID int64) ([]*model.AdditionalService, error) {
	return r.ListByServiceIDs(ctx, []int64{serviceID})
}

// ListByServiceIDs lists additional service configurations by multiple service IDs.
func (r *repository) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) ([]*model.AdditionalService, error) {
	if len(serviceIDs) == 0 {
		return []*model.AdditionalService{}, nil
	}

	return r.query.AdditionalService.WithContext(ctx).
		Where(r.query.AdditionalService.ServiceID.In(serviceIDs...)).
		Where(r.query.AdditionalService.DeleteTime.IsNull()).
		Find()
}

// ListByAdditionalServiceID gets all additional service configurations by additional service ID.
func (r *repository) ListByAdditionalServiceID(
	ctx context.Context, additionalServiceID int64) ([]*model.AdditionalService, error) {
	return r.ListByAdditionalServiceIDs(ctx, []int64{additionalServiceID})
}

// ListByAdditionalServiceIDs lists additional service configurations by multiple additional service IDs.
func (r *repository) ListByAdditionalServiceIDs(
	ctx context.Context, additionalServiceIDs []int64) ([]*model.AdditionalService, error) {
	if len(additionalServiceIDs) == 0 {
		return []*model.AdditionalService{}, nil
	}

	return r.query.AdditionalService.WithContext(ctx).
		Where(r.query.AdditionalService.AdditionalServiceID.In(additionalServiceIDs...)).
		Where(r.query.AdditionalService.DeleteTime.IsNull()).
		Find()
}

// DeleteByServiceID deletes all additional service configurations by service ID.
func (r *repository) DeleteByServiceID(
	ctx context.Context, serviceID int64) error {
	_, err := r.query.AdditionalService.WithContext(ctx).
		Where(r.query.AdditionalService.ServiceID.Eq(serviceID)).
		Delete()
	if err != nil {
		return err
	}

	return nil
}

// DeleteByAdditionalServiceID deletes all additional service configurations by additional service ID.
func (r *repository) DeleteByAdditionalServiceID(
	ctx context.Context, additionalServiceID int64) error {
	_, err := r.query.AdditionalService.WithContext(ctx).
		Where(r.query.AdditionalService.AdditionalServiceID.Eq(additionalServiceID)).
		Delete()
	if err != nil {
		return err
	}

	return nil
}
