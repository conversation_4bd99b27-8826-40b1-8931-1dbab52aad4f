// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_additional_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	additional "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional"
	model "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	query "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockRepository) BatchCreate(ctx context.Context, scopes []*model.AdditionalService) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, scopes)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockRepositoryMockRecorder) BatchCreate(ctx, scopes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockRepository)(nil).BatchCreate), ctx, scopes)
}

// DeleteByAdditionalServiceID mocks base method.
func (m *MockRepository) DeleteByAdditionalServiceID(ctx context.Context, additionalServiceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByAdditionalServiceID", ctx, additionalServiceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByAdditionalServiceID indicates an expected call of DeleteByAdditionalServiceID.
func (mr *MockRepositoryMockRecorder) DeleteByAdditionalServiceID(ctx, additionalServiceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByAdditionalServiceID", reflect.TypeOf((*MockRepository)(nil).DeleteByAdditionalServiceID), ctx, additionalServiceID)
}

// DeleteByServiceID mocks base method.
func (m *MockRepository) DeleteByServiceID(ctx context.Context, serviceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByServiceID", ctx, serviceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByServiceID indicates an expected call of DeleteByServiceID.
func (mr *MockRepositoryMockRecorder) DeleteByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByServiceID", reflect.TypeOf((*MockRepository)(nil).DeleteByServiceID), ctx, serviceID)
}

// ListByAdditionalServiceID mocks base method.
func (m *MockRepository) ListByAdditionalServiceID(ctx context.Context, additionalServiceID int64) ([]*model.AdditionalService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByAdditionalServiceID", ctx, additionalServiceID)
	ret0, _ := ret[0].([]*model.AdditionalService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByAdditionalServiceID indicates an expected call of ListByAdditionalServiceID.
func (mr *MockRepositoryMockRecorder) ListByAdditionalServiceID(ctx, additionalServiceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByAdditionalServiceID", reflect.TypeOf((*MockRepository)(nil).ListByAdditionalServiceID), ctx, additionalServiceID)
}

// ListByAdditionalServiceIDs mocks base method.
func (m *MockRepository) ListByAdditionalServiceIDs(ctx context.Context, additionalServiceIDs []int64) ([]*model.AdditionalService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByAdditionalServiceIDs", ctx, additionalServiceIDs)
	ret0, _ := ret[0].([]*model.AdditionalService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByAdditionalServiceIDs indicates an expected call of ListByAdditionalServiceIDs.
func (mr *MockRepositoryMockRecorder) ListByAdditionalServiceIDs(ctx, additionalServiceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByAdditionalServiceIDs", reflect.TypeOf((*MockRepository)(nil).ListByAdditionalServiceIDs), ctx, additionalServiceIDs)
}

// ListByServiceID mocks base method.
func (m *MockRepository) ListByServiceID(ctx context.Context, serviceID int64) ([]*model.AdditionalService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByServiceID", ctx, serviceID)
	ret0, _ := ret[0].([]*model.AdditionalService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByServiceID indicates an expected call of ListByServiceID.
func (mr *MockRepositoryMockRecorder) ListByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByServiceID", reflect.TypeOf((*MockRepository)(nil).ListByServiceID), ctx, serviceID)
}

// ListByServiceIDs mocks base method.
func (m *MockRepository) ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.AdditionalService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByServiceIDs", ctx, serviceIDs)
	ret0, _ := ret[0].([]*model.AdditionalService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByServiceIDs indicates an expected call of ListByServiceIDs.
func (mr *MockRepositoryMockRecorder) ListByServiceIDs(ctx, serviceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByServiceIDs", reflect.TypeOf((*MockRepository)(nil).ListByServiceIDs), ctx, serviceIDs)
}

// WithTX mocks base method.
func (m *MockRepository) WithTX(q *query.Query) additional.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTX", q)
	ret0, _ := ret[0].(additional.Repository)
	return ret0
}

// WithTX indicates an expected call of WithTX.
func (mr *MockRepositoryMockRecorder) WithTX(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTX", reflect.TypeOf((*MockRepository)(nil).WithTX), q)
}
