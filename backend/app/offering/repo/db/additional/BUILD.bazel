load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "additional",
    srcs = [
        "additional.go",
        "eneity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/additional",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/proto/offering/v1:offering",
    ],
)
