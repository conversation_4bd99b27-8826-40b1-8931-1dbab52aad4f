// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	"github.com/lib/pq"
)

const TableNameServiceLodgingScope = "service_lodging_scope"

// ServiceLodgingScope mapped from table <service_lodging_scope>
type ServiceLodgingScope struct {
	ID                      int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ServiceID               int64                           `gorm:"column:service_id;type:bigint;not null" json:"service_id"`
	IsAllLodging            bool                            `gorm:"column:is_all_lodging;type:boolean;not null" json:"is_all_lodging"`
	CreateTime              *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime              *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime              *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
	OrganizationType        organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(50);not null;default:0;serializer:proto_enum" json:"organization_type"`
	OrganizationID          int64                           `gorm:"column:organization_id;type:bigint;not null" json:"organization_id"`
	AvailableLodgingTypeIds pq.Int64Array                   `gorm:"column:available_lodging_type_ids;type:bigint[];default:{}" json:"available_lodging_type_ids"`
}

// TableName ServiceLodgingScope's table name
func (*ServiceLodgingScope) TableName() string {
	return TableNameServiceLodgingScope
}
