// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"

	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

const TableNameServicePetAvailabilityScope = "service_pet_availability_scope"

// ServicePetAvailabilityScope mapped from table <service_pet_availability_scope>
type ServicePetAvailabilityScope struct {
	ID               int64                               `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ServiceID        int64                               `gorm:"column:service_id;type:bigint;not null;index:idx_service_pet_availability_scope_service_id,priority:1;comment:The ID of the service being configured" json:"service_id"`                                                                               // The ID of the service being configured
	ScopeType        offeringpb.PetAvailabilityScopeType `gorm:"column:scope_type;type:character varying(50);not null;index:idx_service_pet_availability_scope_target_id,priority:1;comment:The scope type enum. Includes PET_TYPE, PET_BREED, COAT_TYPE, PET_SIZE, PET_CODE;serializer:proto_enum" json:"scope_type"` // The scope type enum. Includes PET_TYPE, PET_BREED, COAT_TYPE, PET_SIZE, PET_CODE
	TargetID         int64                               `gorm:"column:target_id;type:bigint;not null;index:idx_service_pet_availability_scope_target_id,priority:2;comment:The ID of the specific pet type, breed, coat type, size, or code" json:"target_id"`                                                        // The ID of the specific pet type, breed, coat type, size, or code
	CreateTime       *time.Time                          `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime       *time.Time                          `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	OrganizationType organizationpb.OrganizationType     `gorm:"column:organization_type;type:character varying(50);not null;serializer:proto_enum" json:"organization_type"`
	OrganizationID   int64                               `gorm:"column:organization_id;type:bigint;not null" json:"organization_id"`
}

// TableName ServicePetAvailabilityScope's table name
func (*ServicePetAvailabilityScope) TableName() string {
	return TableNameServicePetAvailabilityScope
}
