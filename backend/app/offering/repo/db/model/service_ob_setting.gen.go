// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"
)

const TableNameServiceObSetting = "service_ob_setting"

// ServiceObSetting mapped from table <service_ob_setting>
type ServiceObSetting struct {
	ID                int64                                         `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service online booking setting" json:"id"`                                               // Primary key ID of the service online booking setting
	ServiceID         int64                                         `gorm:"column:service_id;type:bigint;not null;comment:Reference to the service template" json:"service_id"`                                                                       // Reference to the service template
	IsAvailable       bool                                          `gorm:"column:is_available;type:boolean;not null;comment:Whether the service is available for online booking" json:"is_available"`                                                // Whether the service is available for online booking
	ShowBasePrice     offeringpb.ServiceOBSetting_ShowBasePriceMode `gorm:"column:show_base_price;type:character varying(50);not null;default:0;comment:Display price mode;serializer:proto_enum" json:"show_base_price"`                             // Display price mode
	IsAllStaff        bool                                          `gorm:"column:is_all_staff;type:boolean;not null;comment:Whether all staff are available for this service when booking online" json:"is_all_staff"`                               // Whether all staff are available for this service when booking online
	AllowGroupBooking bool                                          `gorm:"column:allow_group_booking;type:boolean;not null;comment:Whether this service can be booked together with other care types in one appointment" json:"allow_group_booking"` // Whether this service can be booked together with other care types in one appointment
	CreateTime        *time.Time                                    `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime        *time.Time                                    `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	BusinessID        int64                                         `gorm:"column:business_id;type:bigint;not null" json:"business_id"`
	ShowDuration      offeringpb.ServiceOBSetting_ShowDuration      `gorm:"column:show_duration;type:character varying(50);not null;default:0;comment:Display duration mode;serializer:proto_enum" json:"show_duration"` // Display duration mode
}

// TableName ServiceObSetting's table name
func (*ServiceObSetting) TableName() string {
	return TableNameServiceObSetting
}
