// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const TableNameServicePetOverride = "service_pet_override"

// ServicePetOverride mapped from table <service_pet_override>
type ServicePetOverride struct {
	ID         int64                     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ServiceID  int64                     `gorm:"column:service_id;type:bigint;not null" json:"service_id"`
	PetID      int64                     `gorm:"column:pet_id;type:bigint;not null;index:service_pet_override_pet_id_index,priority:1" json:"pet_id"`
	Overrides  offeringpb.OverrideValues `gorm:"column:overrides;type:jsonb;not null;default:{};comment:JSONB field for override values.;serializer:json" json:"overrides"` // JSONB field for override values.
	CreateTime *time.Time                `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime *time.Time                `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
}

// TableName ServicePetOverride's table name
func (*ServicePetOverride) TableName() string {
	return TableNameServicePetOverride
}
