// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

const TableNameServiceCategory = "service_category"

// ServiceCategory mapped from table <service_category>
type ServiceCategory struct {
	ID               int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	OrganizationType organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(255);not null;serializer:proto_enum" json:"organization_type"`
	OrganizationID   int64                           `gorm:"column:organization_id;type:bigint;not null" json:"organization_id"`
	CareTypeID       int64                           `gorm:"column:care_type_id;type:bigint;not null" json:"care_type_id"`
	Name             string                          `gorm:"column:name;type:character varying(150);not null" json:"name"`
	Sort             int64                           `gorm:"column:sort;type:bigint;not null" json:"sort"`
	CreateTime       *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime       *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime       *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
	Type             offeringpb.Service_Type         `gorm:"column:type;type:character varying(50);not null;serializer:proto_enum" json:"type"`
}

// TableName ServiceCategory's table name
func (*ServiceCategory) TableName() string {
	return TableNameServiceCategory
}
