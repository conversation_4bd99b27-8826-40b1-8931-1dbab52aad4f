// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"

	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	"github.com/shopspring/decimal"
)

const TableNameService = "service"

// Service mapped from table <service>
type Service struct {
	ID               int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service" json:"id"`                                                                                    // Primary key ID of the service
	OrganizationType organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(255);not null;comment:Level of the organization: 1enterprise, 2company, 3business, etc.;serializer:proto_enum" json:"organization_type"` // Level of the organization: 1enterprise, 2company, 3business, etc.
	OrganizationID   int64                           `gorm:"column:organization_id;type:bigint;not null;comment:Organization ID corresponding to the type" json:"organization_id"`                                                                   // Organization ID corresponding to the type
	CareTypeID       int64                           `gorm:"column:care_type_id;type:bigint;not null;index:idx_care_type,priority:1;comment:Reference to care_type used in this service" json:"care_type_id"`                                        // Reference to care_type used in this service
	CategoryID       int64                           `gorm:"column:category_id;type:bigint;not null;comment:Optional category to organize services" json:"category_id"`                                                                              // Optional category to organize services
	Name             string                          `gorm:"column:name;type:character varying(255);not null;comment:Name of the service, unique within the same organization" json:"name"`                                                          // Name of the service, unique within the same organization
	Description      *string                         `gorm:"column:description;type:text;comment:Optional description of the service" json:"description"`                                                                                            // Optional description of the service
	ColorCode        string                          `gorm:"column:color_code;type:character varying(7);not null;comment:Color code for UI display, such as #F15A2B" json:"color_code"`                                                              // Color code for UI display, such as #F15A2B
	Sort             int64                           `gorm:"column:sort;type:bigint;not null;comment:Sort order for UI display" json:"sort"`                                                                                                         // Sort order for UI display
	Images           []string                        `gorm:"column:images;type:jsonb;default:[];comment:List of image URLs in JSON array;serializer:json" json:"images"`                                                                             // List of image URLs in JSON array
	Source           offeringpb.OfferingSource       `gorm:"column:source;type:character varying(255);not null;comment:Source of the service: 1-MoeGo Platform 2-Enterprise Hub;serializer:proto_enum" json:"source"`                                // Source of the service: 1-MoeGo Platform 2-Enterprise Hub
	Status           offeringpb.Service_Status       `gorm:"column:status;type:character varying(255);not null;comment:Status of the service: 1-Active, 2-Inactive, 3-Deleted;serializer:proto_enum" json:"status"`                                  // Status of the service: 1-Active, 2-Inactive, 3-Deleted
	CreateTime       *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime       *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime       *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
	Type             offeringpb.Service_Type         `gorm:"column:type;type:character varying(255);not null;serializer:proto_enum" json:"type"`
	PriceAmount      decimal.Decimal                 `gorm:"column:price_amount;type:numeric(15,2);not null" json:"price_amount"`
	PriceCurrency    string                          `gorm:"column:price_currency;type:character varying(3);not null" json:"price_currency"`
	TaxID            int64                           `gorm:"column:tax_id;type:bigint;not null" json:"tax_id"`
}

// TableName Service's table name
func (*Service) TableName() string {
	return TableNameService
}
