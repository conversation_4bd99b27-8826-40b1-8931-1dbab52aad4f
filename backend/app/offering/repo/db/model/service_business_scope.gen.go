// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	"github.com/lib/pq"
)

const TableNameServiceBusinessScope = "service_business_scope"

// ServiceBusinessScope mapped from table <service_business_scope>
type ServiceBusinessScope struct {
	ID                   int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ServiceID            int64                           `gorm:"column:service_id;type:bigint;not null" json:"service_id"`
	IsAllBusiness        bool                            `gorm:"column:is_all_business;type:boolean;not null" json:"is_all_business"`
	CreateTime           *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime           *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime           *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
	OrganizationType     organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(50);not null;default:0;serializer:proto_enum" json:"organization_type"`
	OrganizationID       int64                           `gorm:"column:organization_id;type:bigint;not null" json:"organization_id"`
	AvailableBusinessIds pq.Int64Array                   `gorm:"column:available_business_ids;type:bigint[];default:{}" json:"available_business_ids"`
}

// TableName ServiceBusinessScope's table name
func (*ServiceBusinessScope) TableName() string {
	return TableNameServiceBusinessScope
}
