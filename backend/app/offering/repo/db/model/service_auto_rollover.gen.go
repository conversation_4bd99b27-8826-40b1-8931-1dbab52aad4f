// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameServiceAutoRollover = "service_auto_rollover"

// ServiceAutoRollover mapped from table <service_auto_rollover>
type ServiceAutoRollover struct {
	ID              int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service auto rollover configuration" json:"id"`           // Primary key ID of the service auto rollover configuration
	ServiceID       int64      `gorm:"column:service_id;type:bigint;not null;comment:ID of the service to monitor for rollover" json:"service_id"`                                // ID of the service to monitor for rollover
	Enabled         bool       `gorm:"column:enabled;type:boolean;not null;comment:Whether auto rollover is enabled" json:"enabled"`                                              // Whether auto rollover is enabled
	TargetServiceID int64      `gorm:"column:target_service_id;type:bigint;not null;comment:Target service to switch to when rollover condition is met" json:"target_service_id"` // Target service to switch to when rollover condition is met
	AfterMinute     int32      `gorm:"column:after_minute;type:integer;not null;comment:Number of minutes after the max duration to trigger auto rollover" json:"after_minute"`   // Number of minutes after the max duration to trigger auto rollover
	CreateTime      *time.Time `gorm:"column:create_time;type:timestamp with time zone;default:now()" json:"create_time"`
	UpdateTime      *time.Time `gorm:"column:update_time;type:timestamp with time zone;default:now()" json:"update_time"`
	DeleteTime      *time.Time `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName ServiceAutoRollover's table name
func (*ServiceAutoRollover) TableName() string {
	return TableNameServiceAutoRollover
}
