// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

const TableNameAdditionalService = "additional_service"

// AdditionalService mapped from table <additional_service>
type AdditionalService struct {
	ID                   int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	OrganizationType     organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(50);not null;index:idx_additional_service_organization,priority:1;serializer:proto_enum" json:"organization_type"`
	OrganizationID       int64                           `gorm:"column:organization_id;type:bigint;not null;index:idx_additional_service_organization,priority:2" json:"organization_id"`
	ServiceID            int64                           `gorm:"column:service_id;type:bigint;not null;uniqueIndex:idx_additional_care_type,priority:1;uniqueIndex:idx_additional_service,priority:1;comment:The ID of the **Service** that the rule is configured FOR." json:"service_id"`   // The ID of the **Service** that the rule is configured FOR.
	AdditionalServiceID  int64                           `gorm:"column:additional_service_id;type:bigint;not null;uniqueIndex:idx_additional_service,priority:2;comment:Rule Type 1: The ID of a specific service/addon that is being linked TO." json:"additional_service_id"`               // Rule Type 1: The ID of a specific service/addon that is being linked TO.
	AdditionalCareTypeID int64                           `gorm:"column:additional_care_type_id;type:bigint;not null;uniqueIndex:idx_additional_care_type,priority:2;comment:Rule Type 2: The ID of a care type. Links to all services within this care type." json:"additional_care_type_id"` // Rule Type 2: The ID of a care type. Links to all services within this care type.
	CreateTime           *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime           *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime           *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName AdditionalService's table name
func (*AdditionalService) TableName() string {
	return TableNameAdditionalService
}
