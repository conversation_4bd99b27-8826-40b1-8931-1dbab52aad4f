// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServicePetOverride(db *gorm.DB, opts ...gen.DOOption) servicePetOverride {
	_servicePetOverride := servicePetOverride{}

	_servicePetOverride.servicePetOverrideDo.UseDB(db, opts...)
	_servicePetOverride.servicePetOverrideDo.UseModel(&model.ServicePetOverride{})

	tableName := _servicePetOverride.servicePetOverrideDo.TableName()
	_servicePetOverride.ALL = field.NewAsterisk(tableName)
	_servicePetOverride.ID = field.NewInt64(tableName, "id")
	_servicePetOverride.ServiceID = field.NewInt64(tableName, "service_id")
	_servicePetOverride.PetID = field.NewInt64(tableName, "pet_id")
	_servicePetOverride.Overrides = field.NewField(tableName, "overrides")
	_servicePetOverride.CreateTime = field.NewTime(tableName, "create_time")
	_servicePetOverride.UpdateTime = field.NewTime(tableName, "update_time")

	_servicePetOverride.fillFieldMap()

	return _servicePetOverride
}

type servicePetOverride struct {
	servicePetOverrideDo servicePetOverrideDo

	ALL        field.Asterisk
	ID         field.Int64
	ServiceID  field.Int64
	PetID      field.Int64
	Overrides  field.Field // JSONB field for override values.
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (s servicePetOverride) Table(newTableName string) *servicePetOverride {
	s.servicePetOverrideDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s servicePetOverride) As(alias string) *servicePetOverride {
	s.servicePetOverrideDo.DO = *(s.servicePetOverrideDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *servicePetOverride) updateTableName(table string) *servicePetOverride {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.PetID = field.NewInt64(table, "pet_id")
	s.Overrides = field.NewField(table, "overrides")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")

	s.fillFieldMap()

	return s
}

func (s *servicePetOverride) WithContext(ctx context.Context) *servicePetOverrideDo {
	return s.servicePetOverrideDo.WithContext(ctx)
}

func (s servicePetOverride) TableName() string { return s.servicePetOverrideDo.TableName() }

func (s servicePetOverride) Alias() string { return s.servicePetOverrideDo.Alias() }

func (s servicePetOverride) Columns(cols ...field.Expr) gen.Columns {
	return s.servicePetOverrideDo.Columns(cols...)
}

func (s *servicePetOverride) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *servicePetOverride) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["pet_id"] = s.PetID
	s.fieldMap["overrides"] = s.Overrides
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
}

func (s servicePetOverride) clone(db *gorm.DB) servicePetOverride {
	s.servicePetOverrideDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s servicePetOverride) replaceDB(db *gorm.DB) servicePetOverride {
	s.servicePetOverrideDo.ReplaceDB(db)
	return s
}

type servicePetOverrideDo struct{ gen.DO }

func (s servicePetOverrideDo) Debug() *servicePetOverrideDo {
	return s.withDO(s.DO.Debug())
}

func (s servicePetOverrideDo) WithContext(ctx context.Context) *servicePetOverrideDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s servicePetOverrideDo) ReadDB() *servicePetOverrideDo {
	return s.Clauses(dbresolver.Read)
}

func (s servicePetOverrideDo) WriteDB() *servicePetOverrideDo {
	return s.Clauses(dbresolver.Write)
}

func (s servicePetOverrideDo) Session(config *gorm.Session) *servicePetOverrideDo {
	return s.withDO(s.DO.Session(config))
}

func (s servicePetOverrideDo) Clauses(conds ...clause.Expression) *servicePetOverrideDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s servicePetOverrideDo) Returning(value interface{}, columns ...string) *servicePetOverrideDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s servicePetOverrideDo) Not(conds ...gen.Condition) *servicePetOverrideDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s servicePetOverrideDo) Or(conds ...gen.Condition) *servicePetOverrideDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s servicePetOverrideDo) Select(conds ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s servicePetOverrideDo) Where(conds ...gen.Condition) *servicePetOverrideDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s servicePetOverrideDo) Order(conds ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s servicePetOverrideDo) Distinct(cols ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s servicePetOverrideDo) Omit(cols ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s servicePetOverrideDo) Join(table schema.Tabler, on ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s servicePetOverrideDo) LeftJoin(table schema.Tabler, on ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s servicePetOverrideDo) RightJoin(table schema.Tabler, on ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s servicePetOverrideDo) Group(cols ...field.Expr) *servicePetOverrideDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s servicePetOverrideDo) Having(conds ...gen.Condition) *servicePetOverrideDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s servicePetOverrideDo) Limit(limit int) *servicePetOverrideDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s servicePetOverrideDo) Offset(offset int) *servicePetOverrideDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s servicePetOverrideDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *servicePetOverrideDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s servicePetOverrideDo) Unscoped() *servicePetOverrideDo {
	return s.withDO(s.DO.Unscoped())
}

func (s servicePetOverrideDo) Create(values ...*model.ServicePetOverride) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s servicePetOverrideDo) CreateInBatches(values []*model.ServicePetOverride, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s servicePetOverrideDo) Save(values ...*model.ServicePetOverride) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s servicePetOverrideDo) First() (*model.ServicePetOverride, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetOverride), nil
	}
}

func (s servicePetOverrideDo) Take() (*model.ServicePetOverride, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetOverride), nil
	}
}

func (s servicePetOverrideDo) Last() (*model.ServicePetOverride, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetOverride), nil
	}
}

func (s servicePetOverrideDo) Find() ([]*model.ServicePetOverride, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServicePetOverride), err
}

func (s servicePetOverrideDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServicePetOverride, err error) {
	buf := make([]*model.ServicePetOverride, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s servicePetOverrideDo) FindInBatches(result *[]*model.ServicePetOverride, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s servicePetOverrideDo) Attrs(attrs ...field.AssignExpr) *servicePetOverrideDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s servicePetOverrideDo) Assign(attrs ...field.AssignExpr) *servicePetOverrideDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s servicePetOverrideDo) Joins(fields ...field.RelationField) *servicePetOverrideDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s servicePetOverrideDo) Preload(fields ...field.RelationField) *servicePetOverrideDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s servicePetOverrideDo) FirstOrInit() (*model.ServicePetOverride, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetOverride), nil
	}
}

func (s servicePetOverrideDo) FirstOrCreate() (*model.ServicePetOverride, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetOverride), nil
	}
}

func (s servicePetOverrideDo) FindByPage(offset int, limit int) (result []*model.ServicePetOverride, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s servicePetOverrideDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s servicePetOverrideDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s servicePetOverrideDo) Delete(models ...*model.ServicePetOverride) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *servicePetOverrideDo) withDO(do gen.Dao) *servicePetOverrideDo {
	s.DO = *do.(*gen.DO)
	return s
}
