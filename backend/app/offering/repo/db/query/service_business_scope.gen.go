// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceBusinessScope(db *gorm.DB, opts ...gen.DOOption) serviceBusinessScope {
	_serviceBusinessScope := serviceBusinessScope{}

	_serviceBusinessScope.serviceBusinessScopeDo.UseDB(db, opts...)
	_serviceBusinessScope.serviceBusinessScopeDo.UseModel(&model.ServiceBusinessScope{})

	tableName := _serviceBusinessScope.serviceBusinessScopeDo.TableName()
	_serviceBusinessScope.ALL = field.NewAsterisk(tableName)
	_serviceBusinessScope.ID = field.NewInt64(tableName, "id")
	_serviceBusinessScope.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceBusinessScope.IsAllBusiness = field.NewBool(tableName, "is_all_business")
	_serviceBusinessScope.CreateTime = field.NewTime(tableName, "create_time")
	_serviceBusinessScope.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceBusinessScope.DeleteTime = field.NewTime(tableName, "delete_time")
	_serviceBusinessScope.OrganizationType = field.NewField(tableName, "organization_type")
	_serviceBusinessScope.OrganizationID = field.NewInt64(tableName, "organization_id")
	_serviceBusinessScope.AvailableBusinessIds = field.NewField(tableName, "available_business_ids")

	_serviceBusinessScope.fillFieldMap()

	return _serviceBusinessScope
}

type serviceBusinessScope struct {
	serviceBusinessScopeDo serviceBusinessScopeDo

	ALL                  field.Asterisk
	ID                   field.Int64
	ServiceID            field.Int64
	IsAllBusiness        field.Bool
	CreateTime           field.Time
	UpdateTime           field.Time
	DeleteTime           field.Time
	OrganizationType     field.Field
	OrganizationID       field.Int64
	AvailableBusinessIds field.Field

	fieldMap map[string]field.Expr
}

func (s serviceBusinessScope) Table(newTableName string) *serviceBusinessScope {
	s.serviceBusinessScopeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceBusinessScope) As(alias string) *serviceBusinessScope {
	s.serviceBusinessScopeDo.DO = *(s.serviceBusinessScopeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceBusinessScope) updateTableName(table string) *serviceBusinessScope {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.IsAllBusiness = field.NewBool(table, "is_all_business")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.AvailableBusinessIds = field.NewField(table, "available_business_ids")

	s.fillFieldMap()

	return s
}

func (s *serviceBusinessScope) WithContext(ctx context.Context) *serviceBusinessScopeDo {
	return s.serviceBusinessScopeDo.WithContext(ctx)
}

func (s serviceBusinessScope) TableName() string { return s.serviceBusinessScopeDo.TableName() }

func (s serviceBusinessScope) Alias() string { return s.serviceBusinessScopeDo.Alias() }

func (s serviceBusinessScope) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceBusinessScopeDo.Columns(cols...)
}

func (s *serviceBusinessScope) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceBusinessScope) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["is_all_business"] = s.IsAllBusiness
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["available_business_ids"] = s.AvailableBusinessIds
}

func (s serviceBusinessScope) clone(db *gorm.DB) serviceBusinessScope {
	s.serviceBusinessScopeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceBusinessScope) replaceDB(db *gorm.DB) serviceBusinessScope {
	s.serviceBusinessScopeDo.ReplaceDB(db)
	return s
}

type serviceBusinessScopeDo struct{ gen.DO }

func (s serviceBusinessScopeDo) Debug() *serviceBusinessScopeDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceBusinessScopeDo) WithContext(ctx context.Context) *serviceBusinessScopeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceBusinessScopeDo) ReadDB() *serviceBusinessScopeDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceBusinessScopeDo) WriteDB() *serviceBusinessScopeDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceBusinessScopeDo) Session(config *gorm.Session) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceBusinessScopeDo) Clauses(conds ...clause.Expression) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceBusinessScopeDo) Returning(value interface{}, columns ...string) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceBusinessScopeDo) Not(conds ...gen.Condition) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceBusinessScopeDo) Or(conds ...gen.Condition) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceBusinessScopeDo) Select(conds ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceBusinessScopeDo) Where(conds ...gen.Condition) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceBusinessScopeDo) Order(conds ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceBusinessScopeDo) Distinct(cols ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceBusinessScopeDo) Omit(cols ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceBusinessScopeDo) Join(table schema.Tabler, on ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceBusinessScopeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceBusinessScopeDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceBusinessScopeDo) Group(cols ...field.Expr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceBusinessScopeDo) Having(conds ...gen.Condition) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceBusinessScopeDo) Limit(limit int) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceBusinessScopeDo) Offset(offset int) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceBusinessScopeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceBusinessScopeDo) Unscoped() *serviceBusinessScopeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceBusinessScopeDo) Create(values ...*model.ServiceBusinessScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceBusinessScopeDo) CreateInBatches(values []*model.ServiceBusinessScope, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceBusinessScopeDo) Save(values ...*model.ServiceBusinessScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceBusinessScopeDo) First() (*model.ServiceBusinessScope, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceBusinessScope), nil
	}
}

func (s serviceBusinessScopeDo) Take() (*model.ServiceBusinessScope, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceBusinessScope), nil
	}
}

func (s serviceBusinessScopeDo) Last() (*model.ServiceBusinessScope, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceBusinessScope), nil
	}
}

func (s serviceBusinessScopeDo) Find() ([]*model.ServiceBusinessScope, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceBusinessScope), err
}

func (s serviceBusinessScopeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceBusinessScope, err error) {
	buf := make([]*model.ServiceBusinessScope, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceBusinessScopeDo) FindInBatches(result *[]*model.ServiceBusinessScope, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceBusinessScopeDo) Attrs(attrs ...field.AssignExpr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceBusinessScopeDo) Assign(attrs ...field.AssignExpr) *serviceBusinessScopeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceBusinessScopeDo) Joins(fields ...field.RelationField) *serviceBusinessScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceBusinessScopeDo) Preload(fields ...field.RelationField) *serviceBusinessScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceBusinessScopeDo) FirstOrInit() (*model.ServiceBusinessScope, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceBusinessScope), nil
	}
}

func (s serviceBusinessScopeDo) FirstOrCreate() (*model.ServiceBusinessScope, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceBusinessScope), nil
	}
}

func (s serviceBusinessScopeDo) FindByPage(offset int, limit int) (result []*model.ServiceBusinessScope, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceBusinessScopeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceBusinessScopeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceBusinessScopeDo) Delete(models ...*model.ServiceBusinessScope) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceBusinessScopeDo) withDO(do gen.Dao) *serviceBusinessScopeDo {
	s.DO = *do.(*gen.DO)
	return s
}
