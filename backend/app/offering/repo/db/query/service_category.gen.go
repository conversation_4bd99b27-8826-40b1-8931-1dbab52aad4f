// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceCategory(db *gorm.DB, opts ...gen.DOOption) serviceCategory {
	_serviceCategory := serviceCategory{}

	_serviceCategory.serviceCategoryDo.UseDB(db, opts...)
	_serviceCategory.serviceCategoryDo.UseModel(&model.ServiceCategory{})

	tableName := _serviceCategory.serviceCategoryDo.TableName()
	_serviceCategory.ALL = field.NewAsterisk(tableName)
	_serviceCategory.ID = field.NewInt64(tableName, "id")
	_serviceCategory.OrganizationType = field.NewField(tableName, "organization_type")
	_serviceCategory.OrganizationID = field.NewInt64(tableName, "organization_id")
	_serviceCategory.CareTypeID = field.NewInt64(tableName, "care_type_id")
	_serviceCategory.Name = field.NewString(tableName, "name")
	_serviceCategory.Sort = field.NewInt64(tableName, "sort")
	_serviceCategory.CreateTime = field.NewTime(tableName, "create_time")
	_serviceCategory.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceCategory.DeleteTime = field.NewTime(tableName, "delete_time")
	_serviceCategory.Type = field.NewField(tableName, "type")

	_serviceCategory.fillFieldMap()

	return _serviceCategory
}

type serviceCategory struct {
	serviceCategoryDo serviceCategoryDo

	ALL              field.Asterisk
	ID               field.Int64
	OrganizationType field.Field
	OrganizationID   field.Int64
	CareTypeID       field.Int64
	Name             field.String
	Sort             field.Int64
	CreateTime       field.Time
	UpdateTime       field.Time
	DeleteTime       field.Time
	Type             field.Field

	fieldMap map[string]field.Expr
}

func (s serviceCategory) Table(newTableName string) *serviceCategory {
	s.serviceCategoryDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceCategory) As(alias string) *serviceCategory {
	s.serviceCategoryDo.DO = *(s.serviceCategoryDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceCategory) updateTableName(table string) *serviceCategory {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.CareTypeID = field.NewInt64(table, "care_type_id")
	s.Name = field.NewString(table, "name")
	s.Sort = field.NewInt64(table, "sort")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")
	s.Type = field.NewField(table, "type")

	s.fillFieldMap()

	return s
}

func (s *serviceCategory) WithContext(ctx context.Context) *serviceCategoryDo {
	return s.serviceCategoryDo.WithContext(ctx)
}

func (s serviceCategory) TableName() string { return s.serviceCategoryDo.TableName() }

func (s serviceCategory) Alias() string { return s.serviceCategoryDo.Alias() }

func (s serviceCategory) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceCategoryDo.Columns(cols...)
}

func (s *serviceCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceCategory) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["care_type_id"] = s.CareTypeID
	s.fieldMap["name"] = s.Name
	s.fieldMap["sort"] = s.Sort
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
	s.fieldMap["type"] = s.Type
}

func (s serviceCategory) clone(db *gorm.DB) serviceCategory {
	s.serviceCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceCategory) replaceDB(db *gorm.DB) serviceCategory {
	s.serviceCategoryDo.ReplaceDB(db)
	return s
}

type serviceCategoryDo struct{ gen.DO }

func (s serviceCategoryDo) Debug() *serviceCategoryDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceCategoryDo) WithContext(ctx context.Context) *serviceCategoryDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceCategoryDo) ReadDB() *serviceCategoryDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceCategoryDo) WriteDB() *serviceCategoryDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceCategoryDo) Session(config *gorm.Session) *serviceCategoryDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceCategoryDo) Clauses(conds ...clause.Expression) *serviceCategoryDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceCategoryDo) Returning(value interface{}, columns ...string) *serviceCategoryDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceCategoryDo) Not(conds ...gen.Condition) *serviceCategoryDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceCategoryDo) Or(conds ...gen.Condition) *serviceCategoryDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceCategoryDo) Select(conds ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceCategoryDo) Where(conds ...gen.Condition) *serviceCategoryDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceCategoryDo) Order(conds ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceCategoryDo) Distinct(cols ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceCategoryDo) Omit(cols ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceCategoryDo) Join(table schema.Tabler, on ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceCategoryDo) Group(cols ...field.Expr) *serviceCategoryDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceCategoryDo) Having(conds ...gen.Condition) *serviceCategoryDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceCategoryDo) Limit(limit int) *serviceCategoryDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceCategoryDo) Offset(offset int) *serviceCategoryDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceCategoryDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceCategoryDo) Unscoped() *serviceCategoryDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceCategoryDo) Create(values ...*model.ServiceCategory) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceCategoryDo) CreateInBatches(values []*model.ServiceCategory, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceCategoryDo) Save(values ...*model.ServiceCategory) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceCategoryDo) First() (*model.ServiceCategory, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceCategory), nil
	}
}

func (s serviceCategoryDo) Take() (*model.ServiceCategory, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceCategory), nil
	}
}

func (s serviceCategoryDo) Last() (*model.ServiceCategory, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceCategory), nil
	}
}

func (s serviceCategoryDo) Find() ([]*model.ServiceCategory, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceCategory), err
}

func (s serviceCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceCategory, err error) {
	buf := make([]*model.ServiceCategory, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceCategoryDo) FindInBatches(result *[]*model.ServiceCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceCategoryDo) Attrs(attrs ...field.AssignExpr) *serviceCategoryDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceCategoryDo) Assign(attrs ...field.AssignExpr) *serviceCategoryDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceCategoryDo) Joins(fields ...field.RelationField) *serviceCategoryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceCategoryDo) Preload(fields ...field.RelationField) *serviceCategoryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceCategoryDo) FirstOrInit() (*model.ServiceCategory, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceCategory), nil
	}
}

func (s serviceCategoryDo) FirstOrCreate() (*model.ServiceCategory, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceCategory), nil
	}
}

func (s serviceCategoryDo) FindByPage(offset int, limit int) (result []*model.ServiceCategory, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceCategoryDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceCategoryDo) Delete(models ...*model.ServiceCategory) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceCategoryDo) withDO(do gen.Dao) *serviceCategoryDo {
	s.DO = *do.(*gen.DO)
	return s
}
