// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newCareType(db *gorm.DB, opts ...gen.DOOption) careType {
	_careType := careType{}

	_careType.careTypeDo.UseDB(db, opts...)
	_careType.careTypeDo.UseModel(&model.CareType{})

	tableName := _careType.careTypeDo.TableName()
	_careType.ALL = field.NewAsterisk(tableName)
	_careType.ID = field.NewInt64(tableName, "id")
	_careType.OrganizationType = field.NewField(tableName, "organization_type")
	_careType.OrganizationID = field.NewInt64(tableName, "organization_id")
	_careType.Name = field.NewString(tableName, "name")
	_careType.CareCategory = field.NewField(tableName, "care_category")
	_careType.Description = field.NewString(tableName, "description")
	_careType.CreateTime = field.NewTime(tableName, "create_time")
	_careType.UpdateTime = field.NewTime(tableName, "update_time")
	_careType.DeleteTime = field.NewTime(tableName, "delete_time")
	_careType.Sort = field.NewInt64(tableName, "sort")

	_careType.fillFieldMap()

	return _careType
}

type careType struct {
	careTypeDo careTypeDo

	ALL              field.Asterisk
	ID               field.Int64  // Primary key ID of the care type
	OrganizationType field.Field  // Level of the organization: enterprise, company, business, etc.
	OrganizationID   field.Int64  // ID of the organization unit corresponding to the organization_type
	Name             field.String // Name of the care type, unique within the same organization
	CareCategory     field.Field  // Care category type
	Description      field.String // Optional description of the care type
	CreateTime       field.Time
	UpdateTime       field.Time
	DeleteTime       field.Time
	Sort             field.Int64

	fieldMap map[string]field.Expr
}

func (c careType) Table(newTableName string) *careType {
	c.careTypeDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c careType) As(alias string) *careType {
	c.careTypeDo.DO = *(c.careTypeDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *careType) updateTableName(table string) *careType {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.OrganizationType = field.NewField(table, "organization_type")
	c.OrganizationID = field.NewInt64(table, "organization_id")
	c.Name = field.NewString(table, "name")
	c.CareCategory = field.NewField(table, "care_category")
	c.Description = field.NewString(table, "description")
	c.CreateTime = field.NewTime(table, "create_time")
	c.UpdateTime = field.NewTime(table, "update_time")
	c.DeleteTime = field.NewTime(table, "delete_time")
	c.Sort = field.NewInt64(table, "sort")

	c.fillFieldMap()

	return c
}

func (c *careType) WithContext(ctx context.Context) *careTypeDo { return c.careTypeDo.WithContext(ctx) }

func (c careType) TableName() string { return c.careTypeDo.TableName() }

func (c careType) Alias() string { return c.careTypeDo.Alias() }

func (c careType) Columns(cols ...field.Expr) gen.Columns { return c.careTypeDo.Columns(cols...) }

func (c *careType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *careType) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 10)
	c.fieldMap["id"] = c.ID
	c.fieldMap["organization_type"] = c.OrganizationType
	c.fieldMap["organization_id"] = c.OrganizationID
	c.fieldMap["name"] = c.Name
	c.fieldMap["care_category"] = c.CareCategory
	c.fieldMap["description"] = c.Description
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["update_time"] = c.UpdateTime
	c.fieldMap["delete_time"] = c.DeleteTime
	c.fieldMap["sort"] = c.Sort
}

func (c careType) clone(db *gorm.DB) careType {
	c.careTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c careType) replaceDB(db *gorm.DB) careType {
	c.careTypeDo.ReplaceDB(db)
	return c
}

type careTypeDo struct{ gen.DO }

func (c careTypeDo) Debug() *careTypeDo {
	return c.withDO(c.DO.Debug())
}

func (c careTypeDo) WithContext(ctx context.Context) *careTypeDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c careTypeDo) ReadDB() *careTypeDo {
	return c.Clauses(dbresolver.Read)
}

func (c careTypeDo) WriteDB() *careTypeDo {
	return c.Clauses(dbresolver.Write)
}

func (c careTypeDo) Session(config *gorm.Session) *careTypeDo {
	return c.withDO(c.DO.Session(config))
}

func (c careTypeDo) Clauses(conds ...clause.Expression) *careTypeDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c careTypeDo) Returning(value interface{}, columns ...string) *careTypeDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c careTypeDo) Not(conds ...gen.Condition) *careTypeDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c careTypeDo) Or(conds ...gen.Condition) *careTypeDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c careTypeDo) Select(conds ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c careTypeDo) Where(conds ...gen.Condition) *careTypeDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c careTypeDo) Order(conds ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c careTypeDo) Distinct(cols ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c careTypeDo) Omit(cols ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c careTypeDo) Join(table schema.Tabler, on ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c careTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c careTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c careTypeDo) Group(cols ...field.Expr) *careTypeDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c careTypeDo) Having(conds ...gen.Condition) *careTypeDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c careTypeDo) Limit(limit int) *careTypeDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c careTypeDo) Offset(offset int) *careTypeDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c careTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *careTypeDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c careTypeDo) Unscoped() *careTypeDo {
	return c.withDO(c.DO.Unscoped())
}

func (c careTypeDo) Create(values ...*model.CareType) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c careTypeDo) CreateInBatches(values []*model.CareType, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c careTypeDo) Save(values ...*model.CareType) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c careTypeDo) First() (*model.CareType, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareType), nil
	}
}

func (c careTypeDo) Take() (*model.CareType, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareType), nil
	}
}

func (c careTypeDo) Last() (*model.CareType, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareType), nil
	}
}

func (c careTypeDo) Find() ([]*model.CareType, error) {
	result, err := c.DO.Find()
	return result.([]*model.CareType), err
}

func (c careTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CareType, err error) {
	buf := make([]*model.CareType, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c careTypeDo) FindInBatches(result *[]*model.CareType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c careTypeDo) Attrs(attrs ...field.AssignExpr) *careTypeDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c careTypeDo) Assign(attrs ...field.AssignExpr) *careTypeDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c careTypeDo) Joins(fields ...field.RelationField) *careTypeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c careTypeDo) Preload(fields ...field.RelationField) *careTypeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c careTypeDo) FirstOrInit() (*model.CareType, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareType), nil
	}
}

func (c careTypeDo) FirstOrCreate() (*model.CareType, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareType), nil
	}
}

func (c careTypeDo) FindByPage(offset int, limit int) (result []*model.CareType, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c careTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c careTypeDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c careTypeDo) Delete(models ...*model.CareType) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *careTypeDo) withDO(do gen.Dao) *careTypeDo {
	c.DO = *do.(*gen.DO)
	return c
}
