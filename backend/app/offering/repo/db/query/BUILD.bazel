load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "query",
    srcs = [
        "additional_service.gen.go",
        "care_type.gen.go",
        "care_type_attribute.gen.go",
        "gen.go",
        "lodging_type.gen.go",
        "lodging_unit.gen.go",
        "service.gen.go",
        "service_attribute.gen.go",
        "service_auto_rollover.gen.go",
        "service_business_override.gen.go",
        "service_business_scope.gen.go",
        "service_category.gen.go",
        "service_lodging_scope.gen.go",
        "service_ob_setting.gen.go",
        "service_ob_staff_binding.gen.go",
        "service_pet_availability_scope.gen.go",
        "service_pet_override.gen.go",
        "service_pet_weight_range.gen.go",
        "service_prerequisite_rule.gen.go",
        "service_staff_override.gen.go",
        "service_staff_scope.gen.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "@io_gorm_gen//:gen",
        "@io_gorm_gen//field",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@io_gorm_gorm//schema",
        "@io_gorm_plugin_dbresolver//:dbresolver",
    ],
)
