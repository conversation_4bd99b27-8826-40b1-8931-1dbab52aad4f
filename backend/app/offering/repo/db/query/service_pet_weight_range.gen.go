// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServicePetWeightRange(db *gorm.DB, opts ...gen.DOOption) servicePetWeightRange {
	_servicePetWeightRange := servicePetWeightRange{}

	_servicePetWeightRange.servicePetWeightRangeDo.UseDB(db, opts...)
	_servicePetWeightRange.servicePetWeightRangeDo.UseModel(&model.ServicePetWeightRange{})

	tableName := _servicePetWeightRange.servicePetWeightRangeDo.TableName()
	_servicePetWeightRange.ALL = field.NewAsterisk(tableName)
	_servicePetWeightRange.ID = field.NewInt64(tableName, "id")
	_servicePetWeightRange.ServiceID = field.NewInt64(tableName, "service_id")
	_servicePetWeightRange.MinWeight = field.NewFloat64(tableName, "min_weight")
	_servicePetWeightRange.MaxWeight = field.NewFloat64(tableName, "max_weight")
	_servicePetWeightRange.IsAllRange = field.NewBool(tableName, "is_all_range")
	_servicePetWeightRange.CreateTime = field.NewTime(tableName, "create_time")
	_servicePetWeightRange.UpdateTime = field.NewTime(tableName, "update_time")
	_servicePetWeightRange.OrganizationType = field.NewField(tableName, "organization_type")
	_servicePetWeightRange.OrganizationID = field.NewInt64(tableName, "organization_id")

	_servicePetWeightRange.fillFieldMap()

	return _servicePetWeightRange
}

type servicePetWeightRange struct {
	servicePetWeightRangeDo servicePetWeightRangeDo

	ALL              field.Asterisk
	ID               field.Int64
	ServiceID        field.Int64   // The ID of the service being configured
	MinWeight        field.Float64 // Minimum weight in pounds (inclusive)
	MaxWeight        field.Float64 // Maximum weight in pounds (inclusive)
	IsAllRange       field.Bool    // Whether this range represents all weights (used when is_all is true)
	CreateTime       field.Time
	UpdateTime       field.Time
	OrganizationType field.Field
	OrganizationID   field.Int64

	fieldMap map[string]field.Expr
}

func (s servicePetWeightRange) Table(newTableName string) *servicePetWeightRange {
	s.servicePetWeightRangeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s servicePetWeightRange) As(alias string) *servicePetWeightRange {
	s.servicePetWeightRangeDo.DO = *(s.servicePetWeightRangeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *servicePetWeightRange) updateTableName(table string) *servicePetWeightRange {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.MinWeight = field.NewFloat64(table, "min_weight")
	s.MaxWeight = field.NewFloat64(table, "max_weight")
	s.IsAllRange = field.NewBool(table, "is_all_range")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")

	s.fillFieldMap()

	return s
}

func (s *servicePetWeightRange) WithContext(ctx context.Context) *servicePetWeightRangeDo {
	return s.servicePetWeightRangeDo.WithContext(ctx)
}

func (s servicePetWeightRange) TableName() string { return s.servicePetWeightRangeDo.TableName() }

func (s servicePetWeightRange) Alias() string { return s.servicePetWeightRangeDo.Alias() }

func (s servicePetWeightRange) Columns(cols ...field.Expr) gen.Columns {
	return s.servicePetWeightRangeDo.Columns(cols...)
}

func (s *servicePetWeightRange) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *servicePetWeightRange) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["min_weight"] = s.MinWeight
	s.fieldMap["max_weight"] = s.MaxWeight
	s.fieldMap["is_all_range"] = s.IsAllRange
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
}

func (s servicePetWeightRange) clone(db *gorm.DB) servicePetWeightRange {
	s.servicePetWeightRangeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s servicePetWeightRange) replaceDB(db *gorm.DB) servicePetWeightRange {
	s.servicePetWeightRangeDo.ReplaceDB(db)
	return s
}

type servicePetWeightRangeDo struct{ gen.DO }

func (s servicePetWeightRangeDo) Debug() *servicePetWeightRangeDo {
	return s.withDO(s.DO.Debug())
}

func (s servicePetWeightRangeDo) WithContext(ctx context.Context) *servicePetWeightRangeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s servicePetWeightRangeDo) ReadDB() *servicePetWeightRangeDo {
	return s.Clauses(dbresolver.Read)
}

func (s servicePetWeightRangeDo) WriteDB() *servicePetWeightRangeDo {
	return s.Clauses(dbresolver.Write)
}

func (s servicePetWeightRangeDo) Session(config *gorm.Session) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Session(config))
}

func (s servicePetWeightRangeDo) Clauses(conds ...clause.Expression) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s servicePetWeightRangeDo) Returning(value interface{}, columns ...string) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s servicePetWeightRangeDo) Not(conds ...gen.Condition) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s servicePetWeightRangeDo) Or(conds ...gen.Condition) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s servicePetWeightRangeDo) Select(conds ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s servicePetWeightRangeDo) Where(conds ...gen.Condition) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s servicePetWeightRangeDo) Order(conds ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s servicePetWeightRangeDo) Distinct(cols ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s servicePetWeightRangeDo) Omit(cols ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s servicePetWeightRangeDo) Join(table schema.Tabler, on ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s servicePetWeightRangeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s servicePetWeightRangeDo) RightJoin(table schema.Tabler, on ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s servicePetWeightRangeDo) Group(cols ...field.Expr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s servicePetWeightRangeDo) Having(conds ...gen.Condition) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s servicePetWeightRangeDo) Limit(limit int) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s servicePetWeightRangeDo) Offset(offset int) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s servicePetWeightRangeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s servicePetWeightRangeDo) Unscoped() *servicePetWeightRangeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s servicePetWeightRangeDo) Create(values ...*model.ServicePetWeightRange) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s servicePetWeightRangeDo) CreateInBatches(values []*model.ServicePetWeightRange, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s servicePetWeightRangeDo) Save(values ...*model.ServicePetWeightRange) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s servicePetWeightRangeDo) First() (*model.ServicePetWeightRange, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetWeightRange), nil
	}
}

func (s servicePetWeightRangeDo) Take() (*model.ServicePetWeightRange, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetWeightRange), nil
	}
}

func (s servicePetWeightRangeDo) Last() (*model.ServicePetWeightRange, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetWeightRange), nil
	}
}

func (s servicePetWeightRangeDo) Find() ([]*model.ServicePetWeightRange, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServicePetWeightRange), err
}

func (s servicePetWeightRangeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServicePetWeightRange, err error) {
	buf := make([]*model.ServicePetWeightRange, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s servicePetWeightRangeDo) FindInBatches(result *[]*model.ServicePetWeightRange, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s servicePetWeightRangeDo) Attrs(attrs ...field.AssignExpr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s servicePetWeightRangeDo) Assign(attrs ...field.AssignExpr) *servicePetWeightRangeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s servicePetWeightRangeDo) Joins(fields ...field.RelationField) *servicePetWeightRangeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s servicePetWeightRangeDo) Preload(fields ...field.RelationField) *servicePetWeightRangeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s servicePetWeightRangeDo) FirstOrInit() (*model.ServicePetWeightRange, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetWeightRange), nil
	}
}

func (s servicePetWeightRangeDo) FirstOrCreate() (*model.ServicePetWeightRange, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetWeightRange), nil
	}
}

func (s servicePetWeightRangeDo) FindByPage(offset int, limit int) (result []*model.ServicePetWeightRange, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s servicePetWeightRangeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s servicePetWeightRangeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s servicePetWeightRangeDo) Delete(models ...*model.ServicePetWeightRange) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *servicePetWeightRangeDo) withDO(do gen.Dao) *servicePetWeightRangeDo {
	s.DO = *do.(*gen.DO)
	return s
}
