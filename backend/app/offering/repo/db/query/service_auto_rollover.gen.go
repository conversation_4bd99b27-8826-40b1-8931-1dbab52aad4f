// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceAutoRollover(db *gorm.DB, opts ...gen.DOOption) serviceAutoRollover {
	_serviceAutoRollover := serviceAutoRollover{}

	_serviceAutoRollover.serviceAutoRolloverDo.UseDB(db, opts...)
	_serviceAutoRollover.serviceAutoRolloverDo.UseModel(&model.ServiceAutoRollover{})

	tableName := _serviceAutoRollover.serviceAutoRolloverDo.TableName()
	_serviceAutoRollover.ALL = field.NewAsterisk(tableName)
	_serviceAutoRollover.ID = field.NewInt64(tableName, "id")
	_serviceAutoRollover.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceAutoRollover.Enabled = field.NewBool(tableName, "enabled")
	_serviceAutoRollover.TargetServiceID = field.NewInt64(tableName, "target_service_id")
	_serviceAutoRollover.AfterMinute = field.NewInt32(tableName, "after_minute")
	_serviceAutoRollover.CreateTime = field.NewTime(tableName, "create_time")
	_serviceAutoRollover.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceAutoRollover.DeleteTime = field.NewTime(tableName, "delete_time")

	_serviceAutoRollover.fillFieldMap()

	return _serviceAutoRollover
}

type serviceAutoRollover struct {
	serviceAutoRolloverDo serviceAutoRolloverDo

	ALL             field.Asterisk
	ID              field.Int64 // Primary key ID of the service auto rollover configuration
	ServiceID       field.Int64 // ID of the service to monitor for rollover
	Enabled         field.Bool  // Whether auto rollover is enabled
	TargetServiceID field.Int64 // Target service to switch to when rollover condition is met
	AfterMinute     field.Int32 // Number of minutes after the max duration to trigger auto rollover
	CreateTime      field.Time
	UpdateTime      field.Time
	DeleteTime      field.Time

	fieldMap map[string]field.Expr
}

func (s serviceAutoRollover) Table(newTableName string) *serviceAutoRollover {
	s.serviceAutoRolloverDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceAutoRollover) As(alias string) *serviceAutoRollover {
	s.serviceAutoRolloverDo.DO = *(s.serviceAutoRolloverDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceAutoRollover) updateTableName(table string) *serviceAutoRollover {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.Enabled = field.NewBool(table, "enabled")
	s.TargetServiceID = field.NewInt64(table, "target_service_id")
	s.AfterMinute = field.NewInt32(table, "after_minute")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *serviceAutoRollover) WithContext(ctx context.Context) *serviceAutoRolloverDo {
	return s.serviceAutoRolloverDo.WithContext(ctx)
}

func (s serviceAutoRollover) TableName() string { return s.serviceAutoRolloverDo.TableName() }

func (s serviceAutoRollover) Alias() string { return s.serviceAutoRolloverDo.Alias() }

func (s serviceAutoRollover) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceAutoRolloverDo.Columns(cols...)
}

func (s *serviceAutoRollover) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceAutoRollover) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["enabled"] = s.Enabled
	s.fieldMap["target_service_id"] = s.TargetServiceID
	s.fieldMap["after_minute"] = s.AfterMinute
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s serviceAutoRollover) clone(db *gorm.DB) serviceAutoRollover {
	s.serviceAutoRolloverDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceAutoRollover) replaceDB(db *gorm.DB) serviceAutoRollover {
	s.serviceAutoRolloverDo.ReplaceDB(db)
	return s
}

type serviceAutoRolloverDo struct{ gen.DO }

func (s serviceAutoRolloverDo) Debug() *serviceAutoRolloverDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceAutoRolloverDo) WithContext(ctx context.Context) *serviceAutoRolloverDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceAutoRolloverDo) ReadDB() *serviceAutoRolloverDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceAutoRolloverDo) WriteDB() *serviceAutoRolloverDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceAutoRolloverDo) Session(config *gorm.Session) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceAutoRolloverDo) Clauses(conds ...clause.Expression) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceAutoRolloverDo) Returning(value interface{}, columns ...string) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceAutoRolloverDo) Not(conds ...gen.Condition) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceAutoRolloverDo) Or(conds ...gen.Condition) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceAutoRolloverDo) Select(conds ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceAutoRolloverDo) Where(conds ...gen.Condition) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceAutoRolloverDo) Order(conds ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceAutoRolloverDo) Distinct(cols ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceAutoRolloverDo) Omit(cols ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceAutoRolloverDo) Join(table schema.Tabler, on ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceAutoRolloverDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceAutoRolloverDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceAutoRolloverDo) Group(cols ...field.Expr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceAutoRolloverDo) Having(conds ...gen.Condition) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceAutoRolloverDo) Limit(limit int) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceAutoRolloverDo) Offset(offset int) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceAutoRolloverDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceAutoRolloverDo) Unscoped() *serviceAutoRolloverDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceAutoRolloverDo) Create(values ...*model.ServiceAutoRollover) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceAutoRolloverDo) CreateInBatches(values []*model.ServiceAutoRollover, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceAutoRolloverDo) Save(values ...*model.ServiceAutoRollover) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceAutoRolloverDo) First() (*model.ServiceAutoRollover, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAutoRollover), nil
	}
}

func (s serviceAutoRolloverDo) Take() (*model.ServiceAutoRollover, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAutoRollover), nil
	}
}

func (s serviceAutoRolloverDo) Last() (*model.ServiceAutoRollover, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAutoRollover), nil
	}
}

func (s serviceAutoRolloverDo) Find() ([]*model.ServiceAutoRollover, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceAutoRollover), err
}

func (s serviceAutoRolloverDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceAutoRollover, err error) {
	buf := make([]*model.ServiceAutoRollover, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceAutoRolloverDo) FindInBatches(result *[]*model.ServiceAutoRollover, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceAutoRolloverDo) Attrs(attrs ...field.AssignExpr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceAutoRolloverDo) Assign(attrs ...field.AssignExpr) *serviceAutoRolloverDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceAutoRolloverDo) Joins(fields ...field.RelationField) *serviceAutoRolloverDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceAutoRolloverDo) Preload(fields ...field.RelationField) *serviceAutoRolloverDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceAutoRolloverDo) FirstOrInit() (*model.ServiceAutoRollover, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAutoRollover), nil
	}
}

func (s serviceAutoRolloverDo) FirstOrCreate() (*model.ServiceAutoRollover, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAutoRollover), nil
	}
}

func (s serviceAutoRolloverDo) FindByPage(offset int, limit int) (result []*model.ServiceAutoRollover, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceAutoRolloverDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceAutoRolloverDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceAutoRolloverDo) Delete(models ...*model.ServiceAutoRollover) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceAutoRolloverDo) withDO(do gen.Dao) *serviceAutoRolloverDo {
	s.DO = *do.(*gen.DO)
	return s
}
