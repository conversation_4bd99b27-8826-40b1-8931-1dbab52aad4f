// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newAdditionalService(db *gorm.DB, opts ...gen.DOOption) additionalService {
	_additionalService := additionalService{}

	_additionalService.additionalServiceDo.UseDB(db, opts...)
	_additionalService.additionalServiceDo.UseModel(&model.AdditionalService{})

	tableName := _additionalService.additionalServiceDo.TableName()
	_additionalService.ALL = field.NewAsterisk(tableName)
	_additionalService.ID = field.NewInt64(tableName, "id")
	_additionalService.OrganizationType = field.NewField(tableName, "organization_type")
	_additionalService.OrganizationID = field.NewInt64(tableName, "organization_id")
	_additionalService.ServiceID = field.NewInt64(tableName, "service_id")
	_additionalService.AdditionalServiceID = field.NewInt64(tableName, "additional_service_id")
	_additionalService.AdditionalCareTypeID = field.NewInt64(tableName, "additional_care_type_id")
	_additionalService.CreateTime = field.NewTime(tableName, "create_time")
	_additionalService.UpdateTime = field.NewTime(tableName, "update_time")
	_additionalService.DeleteTime = field.NewTime(tableName, "delete_time")

	_additionalService.fillFieldMap()

	return _additionalService
}

type additionalService struct {
	additionalServiceDo additionalServiceDo

	ALL                  field.Asterisk
	ID                   field.Int64
	OrganizationType     field.Field
	OrganizationID       field.Int64
	ServiceID            field.Int64 // The ID of the **Service** that the rule is configured FOR.
	AdditionalServiceID  field.Int64 // Rule Type 1: The ID of a specific service/addon that is being linked TO.
	AdditionalCareTypeID field.Int64 // Rule Type 2: The ID of a care type. Links to all services within this care type.
	CreateTime           field.Time
	UpdateTime           field.Time
	DeleteTime           field.Time

	fieldMap map[string]field.Expr
}

func (a additionalService) Table(newTableName string) *additionalService {
	a.additionalServiceDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a additionalService) As(alias string) *additionalService {
	a.additionalServiceDo.DO = *(a.additionalServiceDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *additionalService) updateTableName(table string) *additionalService {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.OrganizationType = field.NewField(table, "organization_type")
	a.OrganizationID = field.NewInt64(table, "organization_id")
	a.ServiceID = field.NewInt64(table, "service_id")
	a.AdditionalServiceID = field.NewInt64(table, "additional_service_id")
	a.AdditionalCareTypeID = field.NewInt64(table, "additional_care_type_id")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdateTime = field.NewTime(table, "update_time")
	a.DeleteTime = field.NewTime(table, "delete_time")

	a.fillFieldMap()

	return a
}

func (a *additionalService) WithContext(ctx context.Context) *additionalServiceDo {
	return a.additionalServiceDo.WithContext(ctx)
}

func (a additionalService) TableName() string { return a.additionalServiceDo.TableName() }

func (a additionalService) Alias() string { return a.additionalServiceDo.Alias() }

func (a additionalService) Columns(cols ...field.Expr) gen.Columns {
	return a.additionalServiceDo.Columns(cols...)
}

func (a *additionalService) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *additionalService) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["organization_type"] = a.OrganizationType
	a.fieldMap["organization_id"] = a.OrganizationID
	a.fieldMap["service_id"] = a.ServiceID
	a.fieldMap["additional_service_id"] = a.AdditionalServiceID
	a.fieldMap["additional_care_type_id"] = a.AdditionalCareTypeID
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["update_time"] = a.UpdateTime
	a.fieldMap["delete_time"] = a.DeleteTime
}

func (a additionalService) clone(db *gorm.DB) additionalService {
	a.additionalServiceDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a additionalService) replaceDB(db *gorm.DB) additionalService {
	a.additionalServiceDo.ReplaceDB(db)
	return a
}

type additionalServiceDo struct{ gen.DO }

func (a additionalServiceDo) Debug() *additionalServiceDo {
	return a.withDO(a.DO.Debug())
}

func (a additionalServiceDo) WithContext(ctx context.Context) *additionalServiceDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a additionalServiceDo) ReadDB() *additionalServiceDo {
	return a.Clauses(dbresolver.Read)
}

func (a additionalServiceDo) WriteDB() *additionalServiceDo {
	return a.Clauses(dbresolver.Write)
}

func (a additionalServiceDo) Session(config *gorm.Session) *additionalServiceDo {
	return a.withDO(a.DO.Session(config))
}

func (a additionalServiceDo) Clauses(conds ...clause.Expression) *additionalServiceDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a additionalServiceDo) Returning(value interface{}, columns ...string) *additionalServiceDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a additionalServiceDo) Not(conds ...gen.Condition) *additionalServiceDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a additionalServiceDo) Or(conds ...gen.Condition) *additionalServiceDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a additionalServiceDo) Select(conds ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a additionalServiceDo) Where(conds ...gen.Condition) *additionalServiceDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a additionalServiceDo) Order(conds ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a additionalServiceDo) Distinct(cols ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a additionalServiceDo) Omit(cols ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a additionalServiceDo) Join(table schema.Tabler, on ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a additionalServiceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a additionalServiceDo) RightJoin(table schema.Tabler, on ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a additionalServiceDo) Group(cols ...field.Expr) *additionalServiceDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a additionalServiceDo) Having(conds ...gen.Condition) *additionalServiceDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a additionalServiceDo) Limit(limit int) *additionalServiceDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a additionalServiceDo) Offset(offset int) *additionalServiceDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a additionalServiceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *additionalServiceDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a additionalServiceDo) Unscoped() *additionalServiceDo {
	return a.withDO(a.DO.Unscoped())
}

func (a additionalServiceDo) Create(values ...*model.AdditionalService) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a additionalServiceDo) CreateInBatches(values []*model.AdditionalService, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a additionalServiceDo) Save(values ...*model.AdditionalService) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a additionalServiceDo) First() (*model.AdditionalService, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdditionalService), nil
	}
}

func (a additionalServiceDo) Take() (*model.AdditionalService, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdditionalService), nil
	}
}

func (a additionalServiceDo) Last() (*model.AdditionalService, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdditionalService), nil
	}
}

func (a additionalServiceDo) Find() ([]*model.AdditionalService, error) {
	result, err := a.DO.Find()
	return result.([]*model.AdditionalService), err
}

func (a additionalServiceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AdditionalService, err error) {
	buf := make([]*model.AdditionalService, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a additionalServiceDo) FindInBatches(result *[]*model.AdditionalService, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a additionalServiceDo) Attrs(attrs ...field.AssignExpr) *additionalServiceDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a additionalServiceDo) Assign(attrs ...field.AssignExpr) *additionalServiceDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a additionalServiceDo) Joins(fields ...field.RelationField) *additionalServiceDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a additionalServiceDo) Preload(fields ...field.RelationField) *additionalServiceDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a additionalServiceDo) FirstOrInit() (*model.AdditionalService, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdditionalService), nil
	}
}

func (a additionalServiceDo) FirstOrCreate() (*model.AdditionalService, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdditionalService), nil
	}
}

func (a additionalServiceDo) FindByPage(offset int, limit int) (result []*model.AdditionalService, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a additionalServiceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a additionalServiceDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a additionalServiceDo) Delete(models ...*model.AdditionalService) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *additionalServiceDo) withDO(do gen.Dao) *additionalServiceDo {
	a.DO = *do.(*gen.DO)
	return a
}
