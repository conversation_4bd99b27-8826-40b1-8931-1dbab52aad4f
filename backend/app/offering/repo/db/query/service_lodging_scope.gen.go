// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceLodgingScope(db *gorm.DB, opts ...gen.DOOption) serviceLodgingScope {
	_serviceLodgingScope := serviceLodgingScope{}

	_serviceLodgingScope.serviceLodgingScopeDo.UseDB(db, opts...)
	_serviceLodgingScope.serviceLodgingScopeDo.UseModel(&model.ServiceLodgingScope{})

	tableName := _serviceLodgingScope.serviceLodgingScopeDo.TableName()
	_serviceLodgingScope.ALL = field.NewAsterisk(tableName)
	_serviceLodgingScope.ID = field.NewInt64(tableName, "id")
	_serviceLodgingScope.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceLodgingScope.IsAllLodging = field.NewBool(tableName, "is_all_lodging")
	_serviceLodgingScope.CreateTime = field.NewTime(tableName, "create_time")
	_serviceLodgingScope.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceLodgingScope.DeleteTime = field.NewTime(tableName, "delete_time")
	_serviceLodgingScope.OrganizationType = field.NewField(tableName, "organization_type")
	_serviceLodgingScope.OrganizationID = field.NewInt64(tableName, "organization_id")
	_serviceLodgingScope.AvailableLodgingTypeIds = field.NewField(tableName, "available_lodging_type_ids")

	_serviceLodgingScope.fillFieldMap()

	return _serviceLodgingScope
}

type serviceLodgingScope struct {
	serviceLodgingScopeDo serviceLodgingScopeDo

	ALL                     field.Asterisk
	ID                      field.Int64
	ServiceID               field.Int64
	IsAllLodging            field.Bool
	CreateTime              field.Time
	UpdateTime              field.Time
	DeleteTime              field.Time
	OrganizationType        field.Field
	OrganizationID          field.Int64
	AvailableLodgingTypeIds field.Field

	fieldMap map[string]field.Expr
}

func (s serviceLodgingScope) Table(newTableName string) *serviceLodgingScope {
	s.serviceLodgingScopeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceLodgingScope) As(alias string) *serviceLodgingScope {
	s.serviceLodgingScopeDo.DO = *(s.serviceLodgingScopeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceLodgingScope) updateTableName(table string) *serviceLodgingScope {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.IsAllLodging = field.NewBool(table, "is_all_lodging")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.AvailableLodgingTypeIds = field.NewField(table, "available_lodging_type_ids")

	s.fillFieldMap()

	return s
}

func (s *serviceLodgingScope) WithContext(ctx context.Context) *serviceLodgingScopeDo {
	return s.serviceLodgingScopeDo.WithContext(ctx)
}

func (s serviceLodgingScope) TableName() string { return s.serviceLodgingScopeDo.TableName() }

func (s serviceLodgingScope) Alias() string { return s.serviceLodgingScopeDo.Alias() }

func (s serviceLodgingScope) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceLodgingScopeDo.Columns(cols...)
}

func (s *serviceLodgingScope) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceLodgingScope) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["is_all_lodging"] = s.IsAllLodging
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["available_lodging_type_ids"] = s.AvailableLodgingTypeIds
}

func (s serviceLodgingScope) clone(db *gorm.DB) serviceLodgingScope {
	s.serviceLodgingScopeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceLodgingScope) replaceDB(db *gorm.DB) serviceLodgingScope {
	s.serviceLodgingScopeDo.ReplaceDB(db)
	return s
}

type serviceLodgingScopeDo struct{ gen.DO }

func (s serviceLodgingScopeDo) Debug() *serviceLodgingScopeDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceLodgingScopeDo) WithContext(ctx context.Context) *serviceLodgingScopeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceLodgingScopeDo) ReadDB() *serviceLodgingScopeDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceLodgingScopeDo) WriteDB() *serviceLodgingScopeDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceLodgingScopeDo) Session(config *gorm.Session) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceLodgingScopeDo) Clauses(conds ...clause.Expression) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceLodgingScopeDo) Returning(value interface{}, columns ...string) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceLodgingScopeDo) Not(conds ...gen.Condition) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceLodgingScopeDo) Or(conds ...gen.Condition) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceLodgingScopeDo) Select(conds ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceLodgingScopeDo) Where(conds ...gen.Condition) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceLodgingScopeDo) Order(conds ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceLodgingScopeDo) Distinct(cols ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceLodgingScopeDo) Omit(cols ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceLodgingScopeDo) Join(table schema.Tabler, on ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceLodgingScopeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceLodgingScopeDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceLodgingScopeDo) Group(cols ...field.Expr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceLodgingScopeDo) Having(conds ...gen.Condition) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceLodgingScopeDo) Limit(limit int) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceLodgingScopeDo) Offset(offset int) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceLodgingScopeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceLodgingScopeDo) Unscoped() *serviceLodgingScopeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceLodgingScopeDo) Create(values ...*model.ServiceLodgingScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceLodgingScopeDo) CreateInBatches(values []*model.ServiceLodgingScope, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceLodgingScopeDo) Save(values ...*model.ServiceLodgingScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceLodgingScopeDo) First() (*model.ServiceLodgingScope, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceLodgingScope), nil
	}
}

func (s serviceLodgingScopeDo) Take() (*model.ServiceLodgingScope, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceLodgingScope), nil
	}
}

func (s serviceLodgingScopeDo) Last() (*model.ServiceLodgingScope, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceLodgingScope), nil
	}
}

func (s serviceLodgingScopeDo) Find() ([]*model.ServiceLodgingScope, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceLodgingScope), err
}

func (s serviceLodgingScopeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceLodgingScope, err error) {
	buf := make([]*model.ServiceLodgingScope, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceLodgingScopeDo) FindInBatches(result *[]*model.ServiceLodgingScope, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceLodgingScopeDo) Attrs(attrs ...field.AssignExpr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceLodgingScopeDo) Assign(attrs ...field.AssignExpr) *serviceLodgingScopeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceLodgingScopeDo) Joins(fields ...field.RelationField) *serviceLodgingScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceLodgingScopeDo) Preload(fields ...field.RelationField) *serviceLodgingScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceLodgingScopeDo) FirstOrInit() (*model.ServiceLodgingScope, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceLodgingScope), nil
	}
}

func (s serviceLodgingScopeDo) FirstOrCreate() (*model.ServiceLodgingScope, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceLodgingScope), nil
	}
}

func (s serviceLodgingScopeDo) FindByPage(offset int, limit int) (result []*model.ServiceLodgingScope, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceLodgingScopeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceLodgingScopeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceLodgingScopeDo) Delete(models ...*model.ServiceLodgingScope) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceLodgingScopeDo) withDO(do gen.Dao) *serviceLodgingScopeDo {
	s.DO = *do.(*gen.DO)
	return s
}
