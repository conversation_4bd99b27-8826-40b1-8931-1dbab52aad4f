// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceObSetting(db *gorm.DB, opts ...gen.DOOption) serviceObSetting {
	_serviceObSetting := serviceObSetting{}

	_serviceObSetting.serviceObSettingDo.UseDB(db, opts...)
	_serviceObSetting.serviceObSettingDo.UseModel(&model.ServiceObSetting{})

	tableName := _serviceObSetting.serviceObSettingDo.TableName()
	_serviceObSetting.ALL = field.NewAsterisk(tableName)
	_serviceObSetting.ID = field.NewInt64(tableName, "id")
	_serviceObSetting.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceObSetting.IsAvailable = field.NewBool(tableName, "is_available")
	_serviceObSetting.ShowBasePrice = field.NewField(tableName, "show_base_price")
	_serviceObSetting.IsAllStaff = field.NewBool(tableName, "is_all_staff")
	_serviceObSetting.AllowGroupBooking = field.NewBool(tableName, "allow_group_booking")
	_serviceObSetting.CreateTime = field.NewTime(tableName, "create_time")
	_serviceObSetting.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceObSetting.BusinessID = field.NewInt64(tableName, "business_id")
	_serviceObSetting.ShowDuration = field.NewField(tableName, "show_duration")

	_serviceObSetting.fillFieldMap()

	return _serviceObSetting
}

type serviceObSetting struct {
	serviceObSettingDo serviceObSettingDo

	ALL               field.Asterisk
	ID                field.Int64 // Primary key ID of the service online booking setting
	ServiceID         field.Int64 // Reference to the service template
	IsAvailable       field.Bool  // Whether the service is available for online booking
	ShowBasePrice     field.Field // Display price mode
	IsAllStaff        field.Bool  // Whether all staff are available for this service when booking online
	AllowGroupBooking field.Bool  // Whether this service can be booked together with other care types in one appointment
	CreateTime        field.Time
	UpdateTime        field.Time
	BusinessID        field.Int64
	ShowDuration      field.Field // Display duration mode

	fieldMap map[string]field.Expr
}

func (s serviceObSetting) Table(newTableName string) *serviceObSetting {
	s.serviceObSettingDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceObSetting) As(alias string) *serviceObSetting {
	s.serviceObSettingDo.DO = *(s.serviceObSettingDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceObSetting) updateTableName(table string) *serviceObSetting {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.IsAvailable = field.NewBool(table, "is_available")
	s.ShowBasePrice = field.NewField(table, "show_base_price")
	s.IsAllStaff = field.NewBool(table, "is_all_staff")
	s.AllowGroupBooking = field.NewBool(table, "allow_group_booking")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.BusinessID = field.NewInt64(table, "business_id")
	s.ShowDuration = field.NewField(table, "show_duration")

	s.fillFieldMap()

	return s
}

func (s *serviceObSetting) WithContext(ctx context.Context) *serviceObSettingDo {
	return s.serviceObSettingDo.WithContext(ctx)
}

func (s serviceObSetting) TableName() string { return s.serviceObSettingDo.TableName() }

func (s serviceObSetting) Alias() string { return s.serviceObSettingDo.Alias() }

func (s serviceObSetting) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceObSettingDo.Columns(cols...)
}

func (s *serviceObSetting) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceObSetting) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["is_available"] = s.IsAvailable
	s.fieldMap["show_base_price"] = s.ShowBasePrice
	s.fieldMap["is_all_staff"] = s.IsAllStaff
	s.fieldMap["allow_group_booking"] = s.AllowGroupBooking
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["business_id"] = s.BusinessID
	s.fieldMap["show_duration"] = s.ShowDuration
}

func (s serviceObSetting) clone(db *gorm.DB) serviceObSetting {
	s.serviceObSettingDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceObSetting) replaceDB(db *gorm.DB) serviceObSetting {
	s.serviceObSettingDo.ReplaceDB(db)
	return s
}

type serviceObSettingDo struct{ gen.DO }

func (s serviceObSettingDo) Debug() *serviceObSettingDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceObSettingDo) WithContext(ctx context.Context) *serviceObSettingDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceObSettingDo) ReadDB() *serviceObSettingDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceObSettingDo) WriteDB() *serviceObSettingDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceObSettingDo) Session(config *gorm.Session) *serviceObSettingDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceObSettingDo) Clauses(conds ...clause.Expression) *serviceObSettingDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceObSettingDo) Returning(value interface{}, columns ...string) *serviceObSettingDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceObSettingDo) Not(conds ...gen.Condition) *serviceObSettingDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceObSettingDo) Or(conds ...gen.Condition) *serviceObSettingDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceObSettingDo) Select(conds ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceObSettingDo) Where(conds ...gen.Condition) *serviceObSettingDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceObSettingDo) Order(conds ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceObSettingDo) Distinct(cols ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceObSettingDo) Omit(cols ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceObSettingDo) Join(table schema.Tabler, on ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceObSettingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceObSettingDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceObSettingDo) Group(cols ...field.Expr) *serviceObSettingDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceObSettingDo) Having(conds ...gen.Condition) *serviceObSettingDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceObSettingDo) Limit(limit int) *serviceObSettingDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceObSettingDo) Offset(offset int) *serviceObSettingDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceObSettingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceObSettingDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceObSettingDo) Unscoped() *serviceObSettingDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceObSettingDo) Create(values ...*model.ServiceObSetting) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceObSettingDo) CreateInBatches(values []*model.ServiceObSetting, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceObSettingDo) Save(values ...*model.ServiceObSetting) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceObSettingDo) First() (*model.ServiceObSetting, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObSetting), nil
	}
}

func (s serviceObSettingDo) Take() (*model.ServiceObSetting, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObSetting), nil
	}
}

func (s serviceObSettingDo) Last() (*model.ServiceObSetting, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObSetting), nil
	}
}

func (s serviceObSettingDo) Find() ([]*model.ServiceObSetting, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceObSetting), err
}

func (s serviceObSettingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceObSetting, err error) {
	buf := make([]*model.ServiceObSetting, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceObSettingDo) FindInBatches(result *[]*model.ServiceObSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceObSettingDo) Attrs(attrs ...field.AssignExpr) *serviceObSettingDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceObSettingDo) Assign(attrs ...field.AssignExpr) *serviceObSettingDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceObSettingDo) Joins(fields ...field.RelationField) *serviceObSettingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceObSettingDo) Preload(fields ...field.RelationField) *serviceObSettingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceObSettingDo) FirstOrInit() (*model.ServiceObSetting, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObSetting), nil
	}
}

func (s serviceObSettingDo) FirstOrCreate() (*model.ServiceObSetting, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObSetting), nil
	}
}

func (s serviceObSettingDo) FindByPage(offset int, limit int) (result []*model.ServiceObSetting, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceObSettingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceObSettingDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceObSettingDo) Delete(models ...*model.ServiceObSetting) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceObSettingDo) withDO(do gen.Dao) *serviceObSettingDo {
	s.DO = *do.(*gen.DO)
	return s
}
