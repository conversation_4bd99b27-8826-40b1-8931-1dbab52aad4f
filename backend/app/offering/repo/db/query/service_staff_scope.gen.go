// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceStaffScope(db *gorm.DB, opts ...gen.DOOption) serviceStaffScope {
	_serviceStaffScope := serviceStaffScope{}

	_serviceStaffScope.serviceStaffScopeDo.UseDB(db, opts...)
	_serviceStaffScope.serviceStaffScopeDo.UseModel(&model.ServiceStaffScope{})

	tableName := _serviceStaffScope.serviceStaffScopeDo.TableName()
	_serviceStaffScope.ALL = field.NewAsterisk(tableName)
	_serviceStaffScope.ID = field.NewInt64(tableName, "id")
	_serviceStaffScope.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceStaffScope.IsAllStaff = field.NewBool(tableName, "is_all_staff")
	_serviceStaffScope.CreateTime = field.NewTime(tableName, "create_time")
	_serviceStaffScope.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceStaffScope.DeleteTime = field.NewTime(tableName, "delete_time")
	_serviceStaffScope.OrganizationType = field.NewField(tableName, "organization_type")
	_serviceStaffScope.OrganizationID = field.NewInt64(tableName, "organization_id")
	_serviceStaffScope.AvailableStaffIds = field.NewField(tableName, "available_staff_ids")

	_serviceStaffScope.fillFieldMap()

	return _serviceStaffScope
}

type serviceStaffScope struct {
	serviceStaffScopeDo serviceStaffScopeDo

	ALL               field.Asterisk
	ID                field.Int64
	ServiceID         field.Int64
	IsAllStaff        field.Bool
	CreateTime        field.Time
	UpdateTime        field.Time
	DeleteTime        field.Time
	OrganizationType  field.Field
	OrganizationID    field.Int64
	AvailableStaffIds field.Field

	fieldMap map[string]field.Expr
}

func (s serviceStaffScope) Table(newTableName string) *serviceStaffScope {
	s.serviceStaffScopeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceStaffScope) As(alias string) *serviceStaffScope {
	s.serviceStaffScopeDo.DO = *(s.serviceStaffScopeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceStaffScope) updateTableName(table string) *serviceStaffScope {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.IsAllStaff = field.NewBool(table, "is_all_staff")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.AvailableStaffIds = field.NewField(table, "available_staff_ids")

	s.fillFieldMap()

	return s
}

func (s *serviceStaffScope) WithContext(ctx context.Context) *serviceStaffScopeDo {
	return s.serviceStaffScopeDo.WithContext(ctx)
}

func (s serviceStaffScope) TableName() string { return s.serviceStaffScopeDo.TableName() }

func (s serviceStaffScope) Alias() string { return s.serviceStaffScopeDo.Alias() }

func (s serviceStaffScope) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceStaffScopeDo.Columns(cols...)
}

func (s *serviceStaffScope) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceStaffScope) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["is_all_staff"] = s.IsAllStaff
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["available_staff_ids"] = s.AvailableStaffIds
}

func (s serviceStaffScope) clone(db *gorm.DB) serviceStaffScope {
	s.serviceStaffScopeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceStaffScope) replaceDB(db *gorm.DB) serviceStaffScope {
	s.serviceStaffScopeDo.ReplaceDB(db)
	return s
}

type serviceStaffScopeDo struct{ gen.DO }

func (s serviceStaffScopeDo) Debug() *serviceStaffScopeDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceStaffScopeDo) WithContext(ctx context.Context) *serviceStaffScopeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceStaffScopeDo) ReadDB() *serviceStaffScopeDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceStaffScopeDo) WriteDB() *serviceStaffScopeDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceStaffScopeDo) Session(config *gorm.Session) *serviceStaffScopeDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceStaffScopeDo) Clauses(conds ...clause.Expression) *serviceStaffScopeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceStaffScopeDo) Returning(value interface{}, columns ...string) *serviceStaffScopeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceStaffScopeDo) Not(conds ...gen.Condition) *serviceStaffScopeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceStaffScopeDo) Or(conds ...gen.Condition) *serviceStaffScopeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceStaffScopeDo) Select(conds ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceStaffScopeDo) Where(conds ...gen.Condition) *serviceStaffScopeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceStaffScopeDo) Order(conds ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceStaffScopeDo) Distinct(cols ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceStaffScopeDo) Omit(cols ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceStaffScopeDo) Join(table schema.Tabler, on ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceStaffScopeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceStaffScopeDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceStaffScopeDo) Group(cols ...field.Expr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceStaffScopeDo) Having(conds ...gen.Condition) *serviceStaffScopeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceStaffScopeDo) Limit(limit int) *serviceStaffScopeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceStaffScopeDo) Offset(offset int) *serviceStaffScopeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceStaffScopeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceStaffScopeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceStaffScopeDo) Unscoped() *serviceStaffScopeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceStaffScopeDo) Create(values ...*model.ServiceStaffScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceStaffScopeDo) CreateInBatches(values []*model.ServiceStaffScope, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceStaffScopeDo) Save(values ...*model.ServiceStaffScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceStaffScopeDo) First() (*model.ServiceStaffScope, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceStaffScope), nil
	}
}

func (s serviceStaffScopeDo) Take() (*model.ServiceStaffScope, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceStaffScope), nil
	}
}

func (s serviceStaffScopeDo) Last() (*model.ServiceStaffScope, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceStaffScope), nil
	}
}

func (s serviceStaffScopeDo) Find() ([]*model.ServiceStaffScope, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceStaffScope), err
}

func (s serviceStaffScopeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceStaffScope, err error) {
	buf := make([]*model.ServiceStaffScope, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceStaffScopeDo) FindInBatches(result *[]*model.ServiceStaffScope, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceStaffScopeDo) Attrs(attrs ...field.AssignExpr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceStaffScopeDo) Assign(attrs ...field.AssignExpr) *serviceStaffScopeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceStaffScopeDo) Joins(fields ...field.RelationField) *serviceStaffScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceStaffScopeDo) Preload(fields ...field.RelationField) *serviceStaffScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceStaffScopeDo) FirstOrInit() (*model.ServiceStaffScope, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceStaffScope), nil
	}
}

func (s serviceStaffScopeDo) FirstOrCreate() (*model.ServiceStaffScope, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceStaffScope), nil
	}
}

func (s serviceStaffScopeDo) FindByPage(offset int, limit int) (result []*model.ServiceStaffScope, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceStaffScopeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceStaffScopeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceStaffScopeDo) Delete(models ...*model.ServiceStaffScope) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceStaffScopeDo) withDO(do gen.Dao) *serviceStaffScopeDo {
	s.DO = *do.(*gen.DO)
	return s
}
