// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServicePetAvailabilityScope(db *gorm.DB, opts ...gen.DOOption) servicePetAvailabilityScope {
	_servicePetAvailabilityScope := servicePetAvailabilityScope{}

	_servicePetAvailabilityScope.servicePetAvailabilityScopeDo.UseDB(db, opts...)
	_servicePetAvailabilityScope.servicePetAvailabilityScopeDo.UseModel(&model.ServicePetAvailabilityScope{})

	tableName := _servicePetAvailabilityScope.servicePetAvailabilityScopeDo.TableName()
	_servicePetAvailabilityScope.ALL = field.NewAsterisk(tableName)
	_servicePetAvailabilityScope.ID = field.NewInt64(tableName, "id")
	_servicePetAvailabilityScope.ServiceID = field.NewInt64(tableName, "service_id")
	_servicePetAvailabilityScope.ScopeType = field.NewField(tableName, "scope_type")
	_servicePetAvailabilityScope.TargetID = field.NewInt64(tableName, "target_id")
	_servicePetAvailabilityScope.CreateTime = field.NewTime(tableName, "create_time")
	_servicePetAvailabilityScope.UpdateTime = field.NewTime(tableName, "update_time")
	_servicePetAvailabilityScope.OrganizationType = field.NewField(tableName, "organization_type")
	_servicePetAvailabilityScope.OrganizationID = field.NewInt64(tableName, "organization_id")

	_servicePetAvailabilityScope.fillFieldMap()

	return _servicePetAvailabilityScope
}

type servicePetAvailabilityScope struct {
	servicePetAvailabilityScopeDo servicePetAvailabilityScopeDo

	ALL              field.Asterisk
	ID               field.Int64
	ServiceID        field.Int64 // The ID of the service being configured
	ScopeType        field.Field // The scope type enum. Includes PET_TYPE, PET_BREED, COAT_TYPE, PET_SIZE, PET_CODE
	TargetID         field.Int64 // The ID of the specific pet type, breed, coat type, size, or code
	CreateTime       field.Time
	UpdateTime       field.Time
	OrganizationType field.Field
	OrganizationID   field.Int64

	fieldMap map[string]field.Expr
}

func (s servicePetAvailabilityScope) Table(newTableName string) *servicePetAvailabilityScope {
	s.servicePetAvailabilityScopeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s servicePetAvailabilityScope) As(alias string) *servicePetAvailabilityScope {
	s.servicePetAvailabilityScopeDo.DO = *(s.servicePetAvailabilityScopeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *servicePetAvailabilityScope) updateTableName(table string) *servicePetAvailabilityScope {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.ScopeType = field.NewField(table, "scope_type")
	s.TargetID = field.NewInt64(table, "target_id")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")

	s.fillFieldMap()

	return s
}

func (s *servicePetAvailabilityScope) WithContext(ctx context.Context) *servicePetAvailabilityScopeDo {
	return s.servicePetAvailabilityScopeDo.WithContext(ctx)
}

func (s servicePetAvailabilityScope) TableName() string {
	return s.servicePetAvailabilityScopeDo.TableName()
}

func (s servicePetAvailabilityScope) Alias() string { return s.servicePetAvailabilityScopeDo.Alias() }

func (s servicePetAvailabilityScope) Columns(cols ...field.Expr) gen.Columns {
	return s.servicePetAvailabilityScopeDo.Columns(cols...)
}

func (s *servicePetAvailabilityScope) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *servicePetAvailabilityScope) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["scope_type"] = s.ScopeType
	s.fieldMap["target_id"] = s.TargetID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
}

func (s servicePetAvailabilityScope) clone(db *gorm.DB) servicePetAvailabilityScope {
	s.servicePetAvailabilityScopeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s servicePetAvailabilityScope) replaceDB(db *gorm.DB) servicePetAvailabilityScope {
	s.servicePetAvailabilityScopeDo.ReplaceDB(db)
	return s
}

type servicePetAvailabilityScopeDo struct{ gen.DO }

func (s servicePetAvailabilityScopeDo) Debug() *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Debug())
}

func (s servicePetAvailabilityScopeDo) WithContext(ctx context.Context) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s servicePetAvailabilityScopeDo) ReadDB() *servicePetAvailabilityScopeDo {
	return s.Clauses(dbresolver.Read)
}

func (s servicePetAvailabilityScopeDo) WriteDB() *servicePetAvailabilityScopeDo {
	return s.Clauses(dbresolver.Write)
}

func (s servicePetAvailabilityScopeDo) Session(config *gorm.Session) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Session(config))
}

func (s servicePetAvailabilityScopeDo) Clauses(conds ...clause.Expression) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s servicePetAvailabilityScopeDo) Returning(value interface{}, columns ...string) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s servicePetAvailabilityScopeDo) Not(conds ...gen.Condition) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s servicePetAvailabilityScopeDo) Or(conds ...gen.Condition) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s servicePetAvailabilityScopeDo) Select(conds ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s servicePetAvailabilityScopeDo) Where(conds ...gen.Condition) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s servicePetAvailabilityScopeDo) Order(conds ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s servicePetAvailabilityScopeDo) Distinct(cols ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s servicePetAvailabilityScopeDo) Omit(cols ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s servicePetAvailabilityScopeDo) Join(table schema.Tabler, on ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s servicePetAvailabilityScopeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s servicePetAvailabilityScopeDo) RightJoin(table schema.Tabler, on ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s servicePetAvailabilityScopeDo) Group(cols ...field.Expr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s servicePetAvailabilityScopeDo) Having(conds ...gen.Condition) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s servicePetAvailabilityScopeDo) Limit(limit int) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s servicePetAvailabilityScopeDo) Offset(offset int) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s servicePetAvailabilityScopeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s servicePetAvailabilityScopeDo) Unscoped() *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s servicePetAvailabilityScopeDo) Create(values ...*model.ServicePetAvailabilityScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s servicePetAvailabilityScopeDo) CreateInBatches(values []*model.ServicePetAvailabilityScope, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s servicePetAvailabilityScopeDo) Save(values ...*model.ServicePetAvailabilityScope) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s servicePetAvailabilityScopeDo) First() (*model.ServicePetAvailabilityScope, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetAvailabilityScope), nil
	}
}

func (s servicePetAvailabilityScopeDo) Take() (*model.ServicePetAvailabilityScope, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetAvailabilityScope), nil
	}
}

func (s servicePetAvailabilityScopeDo) Last() (*model.ServicePetAvailabilityScope, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetAvailabilityScope), nil
	}
}

func (s servicePetAvailabilityScopeDo) Find() ([]*model.ServicePetAvailabilityScope, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServicePetAvailabilityScope), err
}

func (s servicePetAvailabilityScopeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServicePetAvailabilityScope, err error) {
	buf := make([]*model.ServicePetAvailabilityScope, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s servicePetAvailabilityScopeDo) FindInBatches(result *[]*model.ServicePetAvailabilityScope, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s servicePetAvailabilityScopeDo) Attrs(attrs ...field.AssignExpr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s servicePetAvailabilityScopeDo) Assign(attrs ...field.AssignExpr) *servicePetAvailabilityScopeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s servicePetAvailabilityScopeDo) Joins(fields ...field.RelationField) *servicePetAvailabilityScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s servicePetAvailabilityScopeDo) Preload(fields ...field.RelationField) *servicePetAvailabilityScopeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s servicePetAvailabilityScopeDo) FirstOrInit() (*model.ServicePetAvailabilityScope, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetAvailabilityScope), nil
	}
}

func (s servicePetAvailabilityScopeDo) FirstOrCreate() (*model.ServicePetAvailabilityScope, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServicePetAvailabilityScope), nil
	}
}

func (s servicePetAvailabilityScopeDo) FindByPage(offset int, limit int) (result []*model.ServicePetAvailabilityScope, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s servicePetAvailabilityScopeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s servicePetAvailabilityScopeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s servicePetAvailabilityScopeDo) Delete(models ...*model.ServicePetAvailabilityScope) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *servicePetAvailabilityScopeDo) withDO(do gen.Dao) *servicePetAvailabilityScopeDo {
	s.DO = *do.(*gen.DO)
	return s
}
