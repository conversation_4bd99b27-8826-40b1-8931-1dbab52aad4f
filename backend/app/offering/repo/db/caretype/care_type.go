package caretype

import (
	"context"
	"database/sql/driver"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringutils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	defaultLimit  = 50
	defaultOffset = 0
)

//go:generate mockgen -package=mock -destination=mocks/mock_care_type_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	Create(ctx context.Context, careType *model.CareType) error
	Get(ctx context.Context, id int64) (*model.CareType, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.CareType, error)
	Update(ctx context.Context, careType *model.CareType) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, filter *ListCareTypeFilter) ([]*model.CareType, error)
}

// repository implements the data access logic for model.CareType.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// Create creates a new care type.
func (r *repository) Create(ctx context.Context, careType *model.CareType) error {
	return r.query.CareType.WithContext(ctx).Create(careType)
}

// Get gets a care type by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.CareType, error) {
	return r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(id)).First()
}

// BatchGet batch gets care types by IDs.
func (r *repository) BatchGet(ctx context.Context, ids []int64) ([]*model.CareType, error) {
	return r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.In(ids...)).Find()
}

// Update updates a care type.
func (r *repository) Update(ctx context.Context, careType *model.CareType) error {
	_, err := r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(careType.ID)).Updates(careType)
	if err != nil {
		return err
	}

	return nil
}

// Delete deletes a care type by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	result, err := r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}

	return result.Error
}

// List lists care types by filter.
func (r *repository) List(ctx context.Context, filter *ListCareTypeFilter) ([]*model.CareType, error) {
	if filter == nil {
		return []*model.CareType{}, nil
	}
	q := r.query.CareType.WithContext(ctx).
		Where(r.query.CareType.OrganizationType.Eq(offeringutils.ToValuer(filter.OrganizationType.String()))).
		Where(r.query.CareType.OrganizationID.Eq(filter.OrganizationID))
	if len(filter.CareCategories) > 0 {
		careCategories := lo.Map(filter.CareCategories,
			func(careCategory offeringpb.CareCategory, _ int) driver.Valuer {
				return offeringutils.ToValuer(careCategory.String())
			})
		q = q.Where(r.query.CareType.CareCategory.In(careCategories...))
	}
	if filter.Pagination != nil {
		q.Limit(int(filter.Pagination.Limit)).Offset(int(filter.Pagination.Offset))
	} else {
		q.Limit(defaultLimit).Offset(defaultOffset)
	}

	return q.Order(r.query.CareType.Sort.Desc()).Find()
}
