package serviceattribute

import (
	"context"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringutils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

//go:generate mockgen -package=mock -destination=mocks/mock_service_staff_scope_repo.go . StaffScopeRepository
type StaffScopeRepository interface {
	WithQuery(q *query.Query) StaffScopeRepository

	// 基础 CRUD 操作
	Create(ctx context.Context, m *model.ServiceStaffScope) error
	GetByServiceID(ctx context.Context, serviceID int64) (*model.ServiceStaffScope, error)
	ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceStaffScope, error)
	ListByOrganization(ctx context.Context,
		orgType organizationpb.OrganizationType, orgID int64) ([]*model.ServiceStaffScope, error)
	Update(ctx context.Context, m *model.ServiceStaffScope) error
	DeleteByServiceID(ctx context.Context, serviceID int64) (int64, error)
	DeleteByServiceIDs(ctx context.Context, serviceIDs []int64) (int64, error)
}

// staffScopeRepository implements the data access logic for ServiceStaffScope.
type staffScopeRepository struct {
	query *query.Query
}

// NewStaffScopeRepository creates a new repository.
func NewStaffScopeRepository() StaffScopeRepository {
	return &staffScopeRepository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *staffScopeRepository) WithQuery(q *query.Query) StaffScopeRepository {
	return &staffScopeRepository{query: q}
}

// Create creates a new service staff scope.
func (r *staffScopeRepository) Create(
	ctx context.Context, scope *model.ServiceStaffScope) error {
	return r.query.ServiceStaffScope.WithContext(ctx).Create(scope)
}

// GetByServiceID gets service staff scope by service ID.
func (r *staffScopeRepository) GetByServiceID(
	ctx context.Context, serviceID int64) (*model.ServiceStaffScope, error) {
	scopes, err := r.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	if len(scopes) == 0 {
		return nil, nil
	}

	return scopes[0], nil
}

// ListByServiceIDs list all service staff scope by multiple service IDs.
func (r *staffScopeRepository) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) ([]*model.ServiceStaffScope, error) {
	if len(serviceIDs) == 0 {
		return []*model.ServiceStaffScope{}, nil
	}

	return r.query.ServiceStaffScope.WithContext(ctx).
		Where(r.query.ServiceStaffScope.ServiceID.In(serviceIDs...)).
		Where(r.query.ServiceStaffScope.DeleteTime.IsNull()).Find()
}

// ListByOrganization list service staff scope by organization.
func (r *staffScopeRepository) ListByOrganization(
	ctx context.Context, orgType organizationpb.OrganizationType, orgID int64) ([]*model.ServiceStaffScope, error) {
	return r.query.ServiceStaffScope.WithContext(ctx).
		Where(r.query.ServiceStaffScope.OrganizationType.Eq(offeringutils.ToValuer(orgType.String()))).
		Where(r.query.ServiceStaffScope.OrganizationID.Eq(orgID)).Find()
}

// Update updates a service staff scope.
func (r *staffScopeRepository) Update(
	ctx context.Context, scope *model.ServiceStaffScope) error {
	// 更新 UpdateTime 字段
	now := time.Now()
	scope.UpdateTime = &now

	_, err := r.query.ServiceStaffScope.WithContext(ctx).
		Select(r.query.ServiceStaffScope.IsAllStaff,
			r.query.ServiceStaffScope.AvailableStaffIds,
			r.query.ServiceStaffScope.UpdateTime).
		Where(r.query.ServiceStaffScope.ServiceID.Eq(scope.ServiceID)).
		Updates(scope)

	return err
}

// DeleteByServiceID deletes service staff scope by service ID.
func (r *staffScopeRepository) DeleteByServiceID(
	ctx context.Context, serviceID int64) (int64, error) {
	return r.DeleteByServiceIDs(ctx, []int64{serviceID})
}

// DeleteByServiceIDs deletes service staff scope by multiple service IDs.
func (r *staffScopeRepository) DeleteByServiceIDs(
	ctx context.Context, serviceIDs []int64) (int64, error) {
	if len(serviceIDs) == 0 {
		return 0, nil
	}

	result, err := r.query.ServiceStaffScope.WithContext(ctx).
		Where(r.query.ServiceStaffScope.ServiceID.In(serviceIDs...)).
		UpdateColumn(r.query.ServiceStaffScope.DeleteTime, time.Now())
	if err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}
