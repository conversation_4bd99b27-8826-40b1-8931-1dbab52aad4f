package service

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

//go:generate mockgen -package=mock -destination=mocks/mock_pet_override_repo.go . PetOverrideRepository
type PetOverrideRepository interface {
	WithQuery(q *query.Query) PetOverrideRepository
	Save(ctx context.Context, override *model.ServicePetOverride) error
	Update(ctx context.Context, override *model.ServicePetOverride) error
	GetByID(ctx context.Context, id int64) (*model.ServicePetOverride, error)
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context,
		filter *ListPetOverrideFilter, pagination *offeringpb.PaginationRef) ([]*model.ServicePetOverride, int64, error)
}

// PetOverrideRepository implements the data access logic for ServicePetOverride.
type petOverrideRepository struct {
	query *query.Query
}

// NewPetOverrideRepository creates a new repository.
func NewPetOverrideRepository() PetOverrideRepository {
	return &petOverrideRepository{query: query.Use(db.GetDB())}
}

// NewPetOverrideRepositoryWithQuery creates a new repository with query.
func NewPetOverrideRepositoryWithQuery(q *query.Query) PetOverrideRepository {
	return &petOverrideRepository{query: q}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *petOverrideRepository) WithQuery(q *query.Query) PetOverrideRepository {
	return NewPetOverrideRepositoryWithQuery(q)
}

// Save saves a pet override.
func (r *petOverrideRepository) Save(ctx context.Context, override *model.ServicePetOverride) error {
	// 使用 ON CONFLICT 处理 (service_id, pet_id) 的唯一约束冲突
	// 当冲突时，更新 overrides 字段
	r.query.ServicePetOverride.ServiceID.ColumnName()

	return r.query.ServicePetOverride.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: r.query.ServicePetOverride.ServiceID.ColumnName().String()},
				{Name: r.query.ServicePetOverride.PetID.ColumnName().String()},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				r.query.ServicePetOverride.Overrides.ColumnName().String(),
				r.query.ServicePetOverride.UpdateTime.ColumnName().String(),
			}),
		}).
		Create(override)
}

// Update update a pet override.
func (r *petOverrideRepository) Update(ctx context.Context, override *model.ServicePetOverride) error {
	_, err := r.query.ServicePetOverride.WithContext(ctx).
		Where(r.query.ServicePetOverride.ID.Eq(override.ID)).
		Updates(override)

	return err
}

// GetByID gets a pet override by ID.
func (r *petOverrideRepository) GetByID(ctx context.Context, id int64) (*model.ServicePetOverride, error) {
	petOverride, err := r.query.ServicePetOverride.WithContext(ctx).Where(r.query.ServicePetOverride.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return petOverride, nil
}

// Delete deletes a pet override by ID.
func (r *petOverrideRepository) Delete(ctx context.Context, id int64) error {
	_, err := r.query.ServicePetOverride.WithContext(ctx).Where(r.query.ServicePetOverride.ID.Eq(id)).Delete()

	return err
}

// List lists pet overrides
func (r *petOverrideRepository) List(ctx context.Context,
	filter *ListPetOverrideFilter, pagination *offeringpb.PaginationRef,
) ([]*model.ServicePetOverride, int64, error) {
	if len(filter.PetIDs) == 0 && len(filter.ServiceIDs) == 0 {
		return []*model.ServicePetOverride{}, 0, nil
	}

	q := r.query.ServicePetOverride.WithContext(ctx)

	if len(filter.PetIDs) > 0 {
		q = q.Where(r.query.ServicePetOverride.PetID.In(filter.PetIDs...))
	}

	if len(filter.ServiceIDs) > 0 {
		q = q.Where(r.query.ServicePetOverride.ServiceID.In(filter.ServiceIDs...))
	}

	// Pagination
	if pagination != nil {
		q = q.Offset(int(pagination.Offset)).Limit(int(pagination.Limit))
	}

	overrides, err := q.Find()

	total := int64(len(overrides))
	if pagination != nil {
		total, err = q.Count()
		if err != nil {
			return nil, 0, err
		}
	}

	if err != nil {
		return nil, 0, err
	}

	return overrides, total, nil
}
