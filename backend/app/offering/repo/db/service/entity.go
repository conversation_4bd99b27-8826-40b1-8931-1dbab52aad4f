package service

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ListServiceFilter filter for listing services
type ListServiceFilter struct {
	// Required
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64

	// Optional
	Types          []offeringpb.Service_Type
	CareTypeIDs    []int64
	CategoriesIDs  []int64
	Statuses       []offeringpb.Service_Status
	Sources        []offeringpb.OfferingSource
	IDs            []int64
	IncludeDeleted *bool

	// 关键词搜索（服务名称）
	Keyword *string
}

// ListScopeFilter filter for listing pet availability scopes
type ListScopeFilter struct {
	// Required
	ServiceIDs []int64

	// Optional
	ScopeTypes []offeringpb.PetAvailabilityScopeType
}

// ListAvailableServiceIDsFilter filter for listing pet availability scopes by organization
type ListAvailableServiceIDsFilter struct {
	// Required
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64
	PetIdentity      PetIdentity
}

// PetIdentity 宠物身份信息
type PetIdentity struct {
	PetTypeID  *int64
	BreedID    *int64
	PetSizeID  *int64
	CoatTypeID *int64
	PetCodeID  []int64
	Weight     *float64
}

// ListPetOverrideFilter filter for listing pet overrides
type ListPetOverrideFilter struct {
	PetIDs     []int64
	ServiceIDs []int64
}
