package service

import (
	"context"
	"database/sql/driver"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringutils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

//go:generate mockgen -package=mock -destination=mocks/mock_service_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	Create(ctx context.Context, service *model.Service) error
	Get(ctx context.Context, id int64) (*model.Service, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.Service, error)
	Update(ctx context.Context, service *model.Service) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context,
		filter *ListServiceFilter,
		pagination *offeringpb.PaginationRef) ([]*model.Service, int64, error)
}

// repository implements the data access logic for Service.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// Create creates a new service template.
func (r *repository) Create(ctx context.Context, service *model.Service) error {
	return r.query.Service.WithContext(ctx).Create(service)
}

// Get gets a service template by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.Service, error) {
	return r.query.Service.WithContext(ctx).Where(r.query.Service.ID.Eq(id)).First()
}

// BatchGet gets a service template by IDs.
func (r *repository) BatchGet(ctx context.Context, ids []int64) ([]*model.Service, error) {
	if len(ids) == 0 {
		return []*model.Service{}, nil
	}

	return r.query.Service.WithContext(ctx).Where(r.query.Service.ID.In(ids...)).Find()
}

// Update updates a service template.
func (r *repository) Update(ctx context.Context, service *model.Service) error {
	_, err := r.query.Service.WithContext(ctx).
		Where(r.query.Service.ID.Eq(service.ID)).Updates(service)
	if err != nil {
		return err
	}

	return nil
}

// Delete deletes a service template by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	result, err := r.query.Service.WithContext(ctx).Where(r.query.Service.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}

	return result.Error
}

// List lists service templates.
func (r *repository) List(
	ctx context.Context,
	filter *ListServiceFilter,
	pagination *offeringpb.PaginationRef,
) ([]*model.Service, int64, error) {
	if filter == nil {
		return []*model.Service{}, 0, nil
	}

	q := r.query.Service.WithContext(ctx).
		Where(r.query.Service.OrganizationType.Eq(offeringutils.ToValuer(filter.OrganizationType.String()))).
		Where(r.query.Service.OrganizationID.Eq(filter.OrganizationID))

	if len(filter.Types) > 0 {
		types := lo.Map(filter.Types, func(t offeringpb.Service_Type, _ int) driver.Valuer {
			return offeringutils.ToValuer(t.String())
		})
		q = q.Where(r.query.Service.Type.In(types...))
	}
	if len(filter.CareTypeIDs) > 0 {
		q = q.Where(r.query.Service.CareTypeID.In(filter.CareTypeIDs...))
	}
	if len(filter.CategoriesIDs) > 0 {
		q = q.Where(r.query.Service.CategoryID.In(filter.CategoriesIDs...))
	}
	if len(filter.Statuses) > 0 {
		statuses := lo.Map(filter.Statuses, func(status offeringpb.Service_Status, _ int) driver.Valuer {
			return offeringutils.ToValuer(status.String())
		})
		q = q.Where(r.query.Service.Status.In(statuses...))
	}
	if len(filter.Sources) > 0 {
		sources := lo.Map(filter.Sources, func(source offeringpb.OfferingSource, _ int) driver.Valuer {
			return offeringutils.ToValuer(source.String())
		})
		q = q.Where(r.query.Service.Source.In(sources...))
	}
	if len(filter.IDs) > 0 {
		q = q.Where(r.query.Service.ID.In(filter.IDs...))
	}
	// 显式指定 true 查全量，否则查未删除的记录
	if filter.IncludeDeleted == nil || !*filter.IncludeDeleted {
		q = q.Where(r.query.Service.DeleteTime.IsNull())
	}
	if filter.Keyword != nil && *filter.Keyword != "" {
		keyword := "%" + *filter.Keyword + "%"
		q = q.Where(r.query.Service.Name.Like(keyword))
	}

	// Get total count
	total, err := q.Count()
	if err != nil {
		return nil, 0, err
	}

	// Pagination
	if pagination != nil {
		q.Offset(int(pagination.Offset)).Limit(int(pagination.Limit))
	}

	services, err := q.Order(r.query.Service.Sort.Desc()).Find()
	if err != nil {
		return nil, 0, err
	}

	return services, total, nil
}
