// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_serviceobsetting_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	model "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	query "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	serviceobsetting "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceobsetting"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, setting *model.ServiceObSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, setting any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, setting)
}

// CreateStaffBinding mocks base method.
func (m *MockRepository) CreateStaffBinding(ctx context.Context, serviceID int64, staffIDs []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStaffBinding", ctx, serviceID, staffIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateStaffBinding indicates an expected call of CreateStaffBinding.
func (mr *MockRepositoryMockRecorder) CreateStaffBinding(ctx, serviceID, staffIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStaffBinding", reflect.TypeOf((*MockRepository)(nil).CreateStaffBinding), ctx, serviceID, staffIDs)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// DeleteStaffBinding mocks base method.
func (m *MockRepository) DeleteStaffBinding(ctx context.Context, serviceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteStaffBinding", ctx, serviceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteStaffBinding indicates an expected call of DeleteStaffBinding.
func (mr *MockRepositoryMockRecorder) DeleteStaffBinding(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStaffBinding", reflect.TypeOf((*MockRepository)(nil).DeleteStaffBinding), ctx, serviceID)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*model.ServiceObSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*model.ServiceObSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// GetStaffBinding mocks base method.
func (m *MockRepository) GetStaffBinding(ctx context.Context, serviceID int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaffBinding", ctx, serviceID)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffBinding indicates an expected call of GetStaffBinding.
func (mr *MockRepositoryMockRecorder) GetStaffBinding(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffBinding", reflect.TypeOf((*MockRepository)(nil).GetStaffBinding), ctx, serviceID)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, filter *serviceobsetting.ListServiceOBSettingFilter) ([]*model.ServiceObSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, filter)
	ret0, _ := ret[0].([]*model.ServiceObSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, filter)
}

// ListWithPagination mocks base method.
func (m *MockRepository) ListWithPagination(ctx context.Context, filter *serviceobsetting.ListServiceOBSettingFilter, offset, limit int32) ([]*model.ServiceObSetting, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWithPagination", ctx, filter, offset, limit)
	ret0, _ := ret[0].([]*model.ServiceObSetting)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListWithPagination indicates an expected call of ListWithPagination.
func (mr *MockRepositoryMockRecorder) ListWithPagination(ctx, filter, offset, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWithPagination", reflect.TypeOf((*MockRepository)(nil).ListWithPagination), ctx, filter, offset, limit)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, setting *model.ServiceObSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, setting any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, setting)
}

// Upsert mocks base method.
func (m *MockRepository) Upsert(ctx context.Context, setting *model.ServiceObSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockRepositoryMockRecorder) Upsert(ctx, setting any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockRepository)(nil).Upsert), ctx, setting)
}

// WithQuery mocks base method.
func (m *MockRepository) WithQuery(arg0 *query.Query) serviceobsetting.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", arg0)
	ret0, _ := ret[0].(serviceobsetting.Repository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockRepositoryMockRecorder) WithQuery(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockRepository)(nil).WithQuery), arg0)
}
