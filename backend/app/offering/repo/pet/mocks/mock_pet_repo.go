// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/pet (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_pet_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchGetPetExtraInfo mocks base method.
func (m *MockRepository) BatchGetPetExtraInfo(ctx context.Context, petIDs []int64) ([]*businesscustomersvcpb.BatchGetPetExtraInfoResponse_PetExtraInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPetExtraInfo", ctx, petIDs)
	ret0, _ := ret[0].([]*businesscustomersvcpb.BatchGetPetExtraInfoResponse_PetExtraInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPetExtraInfo indicates an expected call of BatchGetPetExtraInfo.
func (mr *MockRepositoryMockRecorder) BatchGetPetExtraInfo(ctx, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPetExtraInfo", reflect.TypeOf((*MockRepository)(nil).BatchGetPetExtraInfo), ctx, petIDs)
}

// BatchGetPetInfo mocks base method.
func (m *MockRepository) BatchGetPetInfo(ctx context.Context, petIDs []int64) ([]*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPetInfo", ctx, petIDs)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessCustomerPetInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPetInfo indicates an expected call of BatchGetPetInfo.
func (mr *MockRepositoryMockRecorder) BatchGetPetInfo(ctx, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPetInfo", reflect.TypeOf((*MockRepository)(nil).BatchGetPetInfo), ctx, petIDs)
}

// ListPetBreeds mocks base method.
func (m *MockRepository) ListPetBreeds(ctx context.Context, companyID int64) ([]*businesscustomerpb.BusinessPetBreedModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetBreeds", ctx, companyID)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessPetBreedModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetBreeds indicates an expected call of ListPetBreeds.
func (mr *MockRepositoryMockRecorder) ListPetBreeds(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetBreeds", reflect.TypeOf((*MockRepository)(nil).ListPetBreeds), ctx, companyID)
}

// ListPetCoatTypes mocks base method.
func (m *MockRepository) ListPetCoatTypes(ctx context.Context, companyID int64) ([]*businesscustomerpb.BusinessPetCoatTypeModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetCoatTypes", ctx, companyID)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessPetCoatTypeModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetCoatTypes indicates an expected call of ListPetCoatTypes.
func (mr *MockRepositoryMockRecorder) ListPetCoatTypes(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetCoatTypes", reflect.TypeOf((*MockRepository)(nil).ListPetCoatTypes), ctx, companyID)
}

// ListPetCodes mocks base method.
func (m *MockRepository) ListPetCodes(ctx context.Context, companyID int64) ([]*businesscustomerpb.BusinessPetCodeModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetCodes", ctx, companyID)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessPetCodeModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetCodes indicates an expected call of ListPetCodes.
func (mr *MockRepositoryMockRecorder) ListPetCodes(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetCodes", reflect.TypeOf((*MockRepository)(nil).ListPetCodes), ctx, companyID)
}

// ListPetSizes mocks base method.
func (m *MockRepository) ListPetSizes(ctx context.Context, companyID int64) ([]*businesscustomerpb.BusinessPetSizeModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetSizes", ctx, companyID)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessPetSizeModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetSizes indicates an expected call of ListPetSizes.
func (mr *MockRepositoryMockRecorder) ListPetSizes(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetSizes", reflect.TypeOf((*MockRepository)(nil).ListPetSizes), ctx, companyID)
}

// ListPetTypes mocks base method.
func (m *MockRepository) ListPetTypes(ctx context.Context, companyID int64) ([]*businesscustomerpb.BusinessPetTypeModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetTypes", ctx, companyID)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessPetTypeModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetTypes indicates an expected call of ListPetTypes.
func (mr *MockRepositoryMockRecorder) ListPetTypes(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetTypes", reflect.TypeOf((*MockRepository)(nil).ListPetTypes), ctx, companyID)
}
