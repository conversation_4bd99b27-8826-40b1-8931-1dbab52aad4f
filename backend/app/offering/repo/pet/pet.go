package pet

import (
	"context"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

//go:generate mockgen -package=mock -destination=mocks/mock_pet_repo.go . Repository
type Repository interface {
	BatchGetPetInfo(ctx context.Context, petIDs []int64) ([]*customerpb.BusinessCustomerPetInfoModel, error)
	BatchGetPetExtraInfo(
		ctx context.Context, petIDs []int64) ([]*svcpb.BatchGetPetExtraInfoResponse_PetExtraInfo, error)
	ListPetTypes(ctx context.Context, companyID int64) ([]*customerpb.BusinessPetTypeModel, error)
	ListPetBreeds(ctx context.Context, companyID int64) ([]*customerpb.BusinessPetBreedModel, error)
	ListPetSizes(ctx context.Context, companyID int64) ([]*customerpb.BusinessPetSizeModel, error)
	ListPetCoatTypes(ctx context.Context, companyID int64) ([]*customerpb.BusinessPetCoatTypeModel, error)
	ListPetCodes(ctx context.Context, companyID int64) ([]*customerpb.BusinessPetCodeModel, error)
}

type repository struct {
	petClient         svcpb.BusinessCustomerPetServiceClient
	petTypeClient     svcpb.BusinessPetTypeServiceClient
	petBreedClient    svcpb.BusinessPetBreedServiceClient
	petSizeClient     svcpb.BusinessPetSizeServiceClient
	petCoatTypeClient svcpb.BusinessPetCoatTypeServiceClient
	petCodeClient     svcpb.BusinessPetCodeServiceClient
}

func NewRepository() Repository {
	return &repository{
		petClient: grpc.NewClient("moego-svc-business-customer", svcpb.NewBusinessCustomerPetServiceClient),
	}
}

func (i *repository) BatchGetPetInfo(
	ctx context.Context, petIDs []int64) ([]*customerpb.BusinessCustomerPetInfoModel, error) {
	response, err := i.petClient.BatchGetPetInfo(ctx, &svcpb.BatchGetPetInfoRequest{
		Ids: petIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetPetInfo err:%+v, petIDs:%v", err, petIDs)

		return nil, err
	}

	return response.GetPets(), nil
}

func (i *repository) BatchGetPetExtraInfo(
	ctx context.Context, petIDs []int64) ([]*svcpb.BatchGetPetExtraInfoResponse_PetExtraInfo, error) {

	response, err := i.petClient.BatchGetPetExtraInfo(ctx, &svcpb.BatchGetPetExtraInfoRequest{
		Ids: petIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetPetExtraInfo err:%+v, petIDs:%v", err, petIDs)

		return nil, err
	}

	return response.GetExtraInfos(), nil
}

func (i *repository) ListPetTypes(
	ctx context.Context, companyID int64) ([]*customerpb.BusinessPetTypeModel, error) {
	response, err := i.petTypeClient.ListPetType(ctx, &svcpb.ListPetTypeRequest{
		CompanyId: companyID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetTypes err:%+v", err)

		return nil, err
	}

	return response.GetTypes(), nil
}

func (i *repository) ListPetBreeds(
	ctx context.Context, companyID int64) ([]*customerpb.BusinessPetBreedModel, error) {
	response, err := i.petBreedClient.ListPetBreed(ctx, &svcpb.ListPetBreedRequest{
		CompanyId: companyID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetBreeds err:%+v", err)

		return nil, err
	}

	return response.GetBreeds(), nil
}

func (i *repository) ListPetSizes(
	ctx context.Context, companyID int64) ([]*customerpb.BusinessPetSizeModel, error) {
	response, err := i.petSizeClient.ListPetSize(ctx, &svcpb.ListPetSizeRequest{
		CompanyId: companyID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetSizes err:%+v", err)

		return nil, err
	}

	return response.GetSizes(), nil
}

func (i *repository) ListPetCoatTypes(
	ctx context.Context, companyID int64) ([]*customerpb.BusinessPetCoatTypeModel, error) {
	response, err := i.petCoatTypeClient.ListPetCoatType(ctx, &svcpb.ListPetCoatTypeRequest{
		CompanyId: companyID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetCoatTypes err:%+v", err)

		return nil, err
	}

	return response.GetCoatTypes(), nil
}

func (i *repository) ListPetCodes(
	ctx context.Context, companyID int64) ([]*customerpb.BusinessPetCodeModel, error) {
	response, err := i.petCodeClient.ListPetCode(ctx, &svcpb.ListPetCodeRequest{
		CompanyId: companyID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetCodes err:%+v", err)

		return nil, err
	}

	return response.GetPetCodes(), nil
}
