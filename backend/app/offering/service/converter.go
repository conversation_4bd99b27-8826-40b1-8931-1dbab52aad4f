package service

import (
	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"
	"github.com/MoeGolibrary/moego/backend/common/utils/money"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// LegacyServiceConverter 封装了 Service 新到旧的转换逻辑
type LegacyServiceConverter struct {
	careTypeMap     map[int64]*caretype.CareType
	idToPetType     map[int64]int64
	idToBreedName   map[int64]string
	relatedDataMask offeringpb.ServiceRelatedDataMask
}

func NewServiceConverter(
	careTypeMap map[int64]*caretype.CareType,
	idToPetType map[int64]int64,
	idToBreedName map[int64]string,
	relatedDataMask offeringpb.ServiceRelatedDataMask,
) *LegacyServiceConverter {
	return &LegacyServiceConverter{
		careTypeMap:     careTypeMap,
		idToPetType:     idToPetType,
		idToBreedName:   idToBreedName,
		relatedDataMask: relatedDataMask,
	}
}

// ToServiceModels 批量转换
func (c *LegacyServiceConverter) ToServiceModels(
	services []*offeringpb.Service) []*offeringinnerpb.ServiceModel {
	return lo.Map(services, func(service *offeringpb.Service, _ int) *offeringinnerpb.ServiceModel {
		return c.ToServiceModel(service)
	})
}

// ToServiceModel 单个转换
func (c *LegacyServiceConverter) ToServiceModel(
	service *offeringpb.Service) *offeringinnerpb.ServiceModel {

	s := handleService(service, c.careTypeMap)

	handleAttributes(s, service.GetAttributes())
	handleAvailableBusiness(s, service.GetAvailableBusiness())
	if hasMask(c.relatedDataMask, offeringpb.ServiceRelatedDataMask_PET_AVAILABILITY) {
		handleAvailableTypeBreed(s, service.GetAvailableTypeBreed(), c.idToPetType, c.idToBreedName)
		handleAvailablePetSize(s, service.GetAvailablePetSize())
		handleAvailableCoatType(s, service.GetAvailableCoatType())
		handleAvailablePetCode(s, service.GetAvailablePetCode())
		handleAvailablePetWeight(s, service.GetAvailablePetWeight())
	}
	handleBusinessStaffOverrides(s, service.GetBusinessStaffOverrides())
	// TODO addon 类型的 ServiceFilterList 字段需要处理

	return s
}

func handleService(
	service *offeringpb.Service,
	careTypeMap map[int64]*caretype.CareType,
) *offeringinnerpb.ServiceModel {
	s := &offeringinnerpb.ServiceModel{
		Id:          service.GetId(),
		Name:        service.GetName(),
		Description: service.GetDescription(),
		Inactive:    service.GetStatus() == offeringpb.Service_INACTIVE,
		Images:      service.GetImages(),
		CategoryId:  service.GetCategoryId(),
		ColorCode:   service.GetColorCode(),
		Price:       money.ToFloat(service.GetPrice()),
		TaxId:       service.GetTaxId(),
		Type:        offeringinnerpb.ServiceType(service.GetType()),
		CreateTime:  service.GetCreateTime(),
		UpdateTime:  service.GetUpdateTime(),
		IsDeleted:   service.GetIsDeleted(),
		CompanyId:   getCompanyID(service),
		Source:      offeringinnerpb.Source(service.GetSource()),
	}

	careType := careTypeMap[service.GetCareTypeId()]
	if careType != nil {
		s.ServiceItemType = offeringinnerpb.ServiceItemType(careType.CareCategory)
	}

	return s
}

func handleBusinessStaffOverrides(
	serviceModel *offeringinnerpb.ServiceModel,
	businessStaffOverrides []*offeringpb.BusinessStaffOverride) {
	if len(businessStaffOverrides) == 0 {
		return
	}

	serviceModel.LocationStaffOverrideList = lo.Map(businessStaffOverrides,
		func(override *offeringpb.BusinessStaffOverride, _ int) *offeringinnerpb.LocationStaffOverrideRule {
			loc := &offeringinnerpb.LocationOverrideRule{}
			if override.GetBusinessOverride() != nil {
				bo := override.GetBusinessOverride()
				loc.BusinessId = bo.GetBusinessId()
				if bo.GetPrice() != nil {
					p := money.ToFloat(bo.GetPrice())
					loc.Price = lo.ToPtr(p)
				}
				if bo.TaxId != nil {
					loc.TaxId = lo.ToPtr(bo.GetTaxId())
				}
				if bo.Duration != nil {
					d := bo.GetDuration()
					loc.Duration = lo.ToPtr(d)
				}
				if bo.MaxDuration != nil {
					md := bo.GetMaxDuration()
					loc.MaxDuration = lo.ToPtr(md)
				}
			}

			staffOverrides := lo.Map(override.GetStaffOverrides(),
				func(so *offeringpb.StaffOverride, _ int) *offeringinnerpb.StaffOverrideRule {
					r := &offeringinnerpb.StaffOverrideRule{StaffId: so.GetStaffId()}
					if so.GetPrice() != nil {
						sp := money.ToFloat(so.GetPrice())
						r.Price = lo.ToPtr(sp)
					}
					if so.Duration != nil {
						d := so.GetDuration()
						r.Duration = lo.ToPtr(d)
					}

					return r
				})

			return &offeringinnerpb.LocationStaffOverrideRule{
				LocationOverride:  loc,
				StaffOverrideList: staffOverrides,
			}
		})
}

func hasMask(
	mask offeringpb.ServiceRelatedDataMask,
	maskType offeringpb.ServiceRelatedDataMask) bool {
	if mask == offeringpb.ServiceRelatedDataMask_ALL {
		return true
	}

	return mask&maskType == maskType
}

func handleAvailableTypeBreed(
	serviceModel *offeringinnerpb.ServiceModel,
	availableTypeBreed *offeringpb.AvailablePetTypeBreed,
	idToPetType map[int64]int64,
	idToBreedName map[int64]string,
) {
	serviceModel.BreedFilter = !availableTypeBreed.GetIsAll()
	customizedBreeds := lo.Map(availableTypeBreed.GetAvailablePetTypes(),
		func(petType *offeringpb.AvailablePetType, _ int) *offeringinnerpb.CustomizedBreed {
			return &offeringinnerpb.CustomizedBreed{
				PetTypeId: idToPetType[petType.GetPetTypeId()],
				Breeds: lo.Map(petType.GetBreedIds(), func(breedId int64, _ int) string {
					return idToBreedName[breedId]
				}),
			}
		})
	serviceModel.CustomizedBreed = customizedBreeds
}

func handleAvailablePetSize(
	serviceModel *offeringinnerpb.ServiceModel, availablePetSize *offeringpb.AvailablePetSize) {
	serviceModel.PetSizeFilter = !availablePetSize.GetIsAll()
	serviceModel.CustomizedPetSizes = availablePetSize.GetPetSizeIds()
}

func handleAvailableCoatType(
	serviceModel *offeringinnerpb.ServiceModel, availableCoatType *offeringpb.AvailableCoatType) {
	serviceModel.CoatFilter = !availableCoatType.GetIsAll()
	serviceModel.CustomizedCoat = availableCoatType.GetCoatTypeIds()
}

func handleAvailablePetCode(
	serviceModel *offeringinnerpb.ServiceModel, availablePetCode *offeringpb.AvailablePetCode) {
	serviceModel.PetCodeFilter = &offeringinnerpb.PetCodeFilter{
		IsWhiteList:  availablePetCode.GetRuleType() == offeringpb.AvailabilityRuleType_INCLUDE,
		IsAllPetCode: availablePetCode.GetRuleType() == offeringpb.AvailabilityRuleType_NO_RESTRICTION,
		PetCodeIds:   availablePetCode.GetPetCodeIds(),
	}
}

func handleAvailablePetWeight(
	serviceModel *offeringinnerpb.ServiceModel, availablePetWeight *offeringpb.AvailablePetWeight) {
	serviceModel.WeightFilter = !availablePetWeight.GetIsAll()
	// legacy service 只支持一个范围
	weightRange := availablePetWeight.PetWeightRanges[0]
	serviceModel.WeightRange = []float64{weightRange.GetMinWeight(), weightRange.GetMaxWeight()}
}

func handleAvailableBusiness(
	serviceModel *offeringinnerpb.ServiceModel, availableBusiness *offeringpb.AvailableBusiness) {
	serviceModel.IsAllLocation = availableBusiness.GetIsAll()
	serviceModel.AvailableBusinessIdList = availableBusiness.GetBusinessIds()
}

func handleAttributes(
	serviceModel *offeringinnerpb.ServiceModel, attributes *offeringpb.ServiceAttributes) {
	if attributes == nil {
		return
	}
	serviceModel.RequireDedicatedStaff = attributes.GetIsRequiredStaff()
	serviceModel.PriceUnit = getPriceUnit(attributes)
	serviceModel.Duration = getDuration(attributes)
	serviceModel.MaxDuration = getMaxDuration(attributes)

	if attributes.AvailableLodgingType != nil {
		serviceModel.LodgingFilter = !attributes.AvailableLodgingType.GetIsAll()
		serviceModel.CustomizedLodgings = attributes.AvailableLodgingType.GetLodgingTypeIds()
	}

	if attributes.AvailableStaff != nil {
		serviceModel.AvailableForAllStaff = attributes.GetAvailableStaff().GetIsAll()
		serviceModel.AvailableStaffs = &offeringinnerpb.AvailableStaffs{
			IsAllAvailable: attributes.GetAvailableStaff().GetIsAll(),
			Ids:            attributes.GetAvailableStaff().GetStaffIds(),
		}
	}

	if attributes.AutoRollover != nil {
		serviceModel.AutoRolloverRule = &offeringinnerpb.AutoRolloverRule{
			Enabled:         attributes.GetAutoRollover().GetEnabled(),
			TargetServiceId: attributes.GetAutoRollover().GetTargetServiceId(),
			AfterMinute:     attributes.GetAutoRollover().GetAfterMinute(),
		}
	}

	if attributes.PrerequisiteRule != nil {
		serviceModel.IsEvaluationRequired = attributes.GetPrerequisiteRule().GetEnabled()
		serviceModel.IsEvaluationRequiredForOb = attributes.GetPrerequisiteRule().GetObEnabled()
		// legacy service 仅 BD 支持一个 evaluation service
		serviceModel.EvaluationId = attributes.GetPrerequisiteRule().GetServiceIds()[0]
	}

	if attributes.DefaultService != nil {
		serviceModel.BundleServiceIds = attributes.GetDefaultService().GetServiceIds()
	}

	handleAdditionalServiceRule(serviceModel, attributes.ConditionalDefaultService)
}

func handleAdditionalServiceRule(
	serviceModel *offeringinnerpb.ServiceModel,
	defaultService *offeringpb.ConditionalDefaultService) {
	if defaultService == nil {
		return
	}
	serviceModel.AdditionalServiceRule = &offeringinnerpb.AdditionalServiceRule{
		Enable: defaultService.GetEnabled(),
		// legacy service 里 unit 与 boarding service 的 unit 一致
		MinStayLength: defaultService.GetCondition().GetMinStayLength(),
		ApplyRules: lo.Map(defaultService.GetApplyRules(),
			func(applyRule *offeringpb.ConditionalDefaultService_ApplyRule,
				_ int) *offeringinnerpb.AdditionalServiceRule_ApplyRule {
				return &offeringinnerpb.AdditionalServiceRule_ApplyRule{
					ServiceId:      applyRule.GetServiceId(),
					DateType:       offeringinnerpb.DateType(applyRule.GetDateType()),
					QuantityPerDay: applyRule.GetQuantityPerDay(),
				}
			}),
	}
}

func getMaxDuration(attributes *offeringpb.ServiceAttributes) int32 {
	if attributes.MaxDuration != nil {
		return int32(*attributes.MaxDuration)
	}

	return 0
}

func getPriceUnit(attributes *offeringpb.ServiceAttributes) offeringinnerpb.ServicePriceUnit {
	if attributes.PriceUnit != nil {
		return offeringinnerpb.ServicePriceUnit(attributes.GetPriceUnit())
	}

	return offeringinnerpb.ServicePriceUnit_PER_SESSION
}

func getDuration(attributes *offeringpb.ServiceAttributes) int32 {
	if attributes.Duration != nil {
		return int32(*attributes.Duration)
	}

	return 0
}

func getCompanyID(service *offeringpb.Service) int64 {
	if service.GetOrganizationType() == organizationpb.OrganizationType_COMPANY {
		return service.GetOrganizationId()
	}

	return 0
}
