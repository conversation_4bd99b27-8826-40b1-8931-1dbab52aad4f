load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "addon.go",
        "business_consumer.go",
        "care_type.go",
        "lodging.go",
        "offering_inner.go",
        "service.go",
    ],
    cgo = True,
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/addon",
        "//backend/app/offering/logic/addon/category",
        "//backend/app/offering/logic/caretype",
        "//backend/app/offering/logic/caretype/attribute",
        "//backend/app/offering/logic/inner",
        "//backend/app/offering/logic/lodging",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/category",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/query",
        "//backend/common/rpc/framework/log",
        "//backend/proto/offering/inner",
        "//backend/proto/offering/v1:offering",
        "@com_github_ibm_sarama//:sarama",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/event_bus/v1:event_bus",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
