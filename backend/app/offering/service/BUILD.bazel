load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "addon.go",
        "business_consumer.go",
        "care_type.go",
        "converter.go",
        "inner_service.go",
        "lodging.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/addon",
        "//backend/app/offering/logic/addon/category",
        "//backend/app/offering/logic/caretype",
        "//backend/app/offering/logic/caretype/attribute",
        "//backend/app/offering/logic/lodging",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/category",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/pet",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/money",
        "//backend/proto/offering/inner",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_ibm_sarama//:sarama",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/event_bus/v1:event_bus",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_x_sync//errgroup",
    ],
)
