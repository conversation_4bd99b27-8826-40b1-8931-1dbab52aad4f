package service

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type BusinessEventConsumer struct {
	obSettingLogic *obsetting.Logic
}

func NewBusinessEventConsumer() *BusinessEventConsumer {
	return &BusinessEventConsumer{
		obSettingLogic: obsetting.New(),
	}
}

func (c *BusinessEventConsumer) BusinessEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "BusinessEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, eventType, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "BusinessEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "BusinessEventHandle parseEventData, eventData: %+v", eventData)

	// check
	if !c.validateEvent(ctx, eventData, eventType) {
		return nil // Invalid data, stop processing
	}

	// handle business created event
	businessCreatedEvent := eventData.GetBusinessCreatedEvent()
	companyID := businessCreatedEvent.GetCompanyId()
	businessID := businessCreatedEvent.GetBusinessId()

	log.InfoContextf(ctx, "BusinessEventHandle received business created event, companyID:%d, businessID:%d",
		companyID, businessID)

	// 为新创建的 business 初始化在线预约设置
	query := query.Use(db.GetDB())
	if err := c.obSettingLogic.InitByBusinessID(ctx, query, companyID, businessID); err != nil {
		log.ErrorContextf(ctx, "BusinessEventHandle InitByBusinessID err, companyID:%d, businessID:%d, err: %v",
			companyID, businessID, err)

		return err
	}

	log.InfoContextf(ctx, "BusinessEventHandle InitByBusinessID success, companyID:%d, businessID:%d",
		companyID, businessID)

	return nil
}

func (c *BusinessEventConsumer) validateEvent(ctx context.Context,
	eventData *eventbuspb.EventData, eventType eventbuspb.EventType) bool {
	// only consume business created event
	if eventType != eventbuspb.EventType_BUSINESS_CREATED {
		return false
	}

	// check event data
	businessCreatedEvent := eventData.GetBusinessCreatedEvent()
	if businessCreatedEvent == nil ||
		businessCreatedEvent.GetCompanyId() == 0 ||
		businessCreatedEvent.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "invalid business created event, event: %+v", businessCreatedEvent)

		return false
	}

	return true
}

func parseEventData(ctx context.Context, msg *sarama.ConsumerMessage) (
	*eventbuspb.EventData, eventbuspb.EventType, error) {
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true, // new fields should not break old consumer.
	}
	var event eventbuspb.Event
	if err := unmarshaler.Unmarshal(msg.Value, &event); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal event err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var detail proto.Message
	var err error
	if detail, err = event.Detail.UnmarshalNew(); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal detail err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var eventData *eventbuspb.EventData
	if ed, ok := detail.(*eventbuspb.EventData); ok {
		eventData = ed
	} else {
		log.ErrorContextf(ctx, "parseEventData invalid detail type, expected *eventbuspb.EventData, got %T", detail)

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, fmt.Errorf("invalid detail type")
	}

	return eventData, event.EventType, nil
}
