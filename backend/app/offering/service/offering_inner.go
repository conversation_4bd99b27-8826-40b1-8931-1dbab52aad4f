package service

import "C"

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/inner"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
)

type OfferingInnerService struct {
	inner *inner.Logic
	offeringinnerpb.UnimplementedGroomingServiceServiceServer
}

func NewOfferingInnerService() *OfferingInnerService {
	return &OfferingInnerService{
		inner: inner.New(),
	}
}

func (s *OfferingInnerService) GetApplicableServiceList(
	ctx context.Context, req *offeringinnerpb.GetApplicableServiceListRequest) (
	*offeringinnerpb.GetApplicableServiceListResponse, error) {
	return s.inner.GetApplicableServiceList(ctx, req)
}
