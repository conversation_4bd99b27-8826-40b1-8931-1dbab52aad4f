package service

import (
	"context"
	"fmt"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/addon"
	"strconv"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	addoncategory "github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/category"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	servicecategory "github.com/MoeGolibrary/moego/backend/app/offering/logic/service/category"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/pet"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

type InnerService struct {
	offeringinnerpb.UnimplementedGroomingServiceServiceServer
	serviceLogic         *service.Logic
	addonLogic           *addon.Logic
	careTypeLogic        *caretype.Logic
	petRepo              pet.Repository
	serviceCategoryLogic *servicecategory.Logic
	addonCategoryLogic   *addoncategory.Logic
	availabilityManager  *availability.Manager
}

func NewInnerService() *InnerService {
	return &InnerService{
		serviceLogic:         service.NewLogic(),
		addonLogic:           addon.NewLogic(),
		careTypeLogic:        caretype.NewLogic(),
		petRepo:              pet.NewRepository(),
		serviceCategoryLogic: servicecategory.NewLogic(),
		addonCategoryLogic:   addoncategory.NewLogic(),
		availabilityManager:  availability.NewManager(),
	}
}

func (s *InnerService) BatchGetServices(
	ctx context.Context,
	req *offeringinnerpb.BatchGetServicesRequest,
) (*offeringinnerpb.BatchGetServicesResponse, error) {
	res, err := s.serviceLogic.BatchGetServices(ctx, &offeringpb.BatchGetServicesRequest{
		Ids:             req.GetIds(),
		RelatedDataMask: req.RelatedDataMask,
	})
	if err != nil {
		return nil, err
	}
	if len(res.GetServices()) == 0 {
		return &offeringinnerpb.BatchGetServicesResponse{}, nil
	}

	var companyID int64 = 101005 // TODO 待替换

	// 并行拉取转换所需要的原始数据
	careTypeMap, idToPetType, idToBreedName, err := s.loadServiceMetadata(
		ctx, res.GetServices(), req.GetRelatedDataMask(), companyID)
	if err != nil {
		return nil, err
	}

	converter := NewServiceConverter(
		careTypeMap,
		idToPetType,
		idToBreedName,
		req.GetRelatedDataMask(),
	)
	serviceModels := converter.ToServiceModels(res.GetServices())

	return &offeringinnerpb.BatchGetServicesResponse{
		Services: serviceModels,
	}, nil
}

// loadServiceMetadata 并发加载服务转换所需的元数据
func (s *InnerService) loadServiceMetadata(ctx context.Context, services []*offeringpb.Service,
	relatedDataMask offeringpb.ServiceRelatedDataMask, companyID int64) (
	careTypeMap map[int64]*caretype.CareType,
	idToPetType map[int64]int64,
	idToBreedName map[int64]string,
	err error) {

	// 初始化返回值
	careTypeMap = make(map[int64]*caretype.CareType)
	idToPetType = make(map[int64]int64)
	idToBreedName = make(map[int64]string)

	// 并行拉取转换所需要的原始数据
	eg, egCtx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		careTypeIDs := lo.Map(services, func(service *offeringpb.Service, _ int) int64 {
			return service.GetCareTypeId()
		})
		careTypes, err := s.careTypeLogic.BatchGetCareTypes(egCtx, careTypeIDs)
		if err != nil {
			return err
		}
		careTypeMap = lo.KeyBy(careTypes, func(careType *caretype.CareType) int64 {
			return careType.ID
		})

		return nil
	})

	// 掩码包含 PET_AVAILABILITY
	needPetAvailability := hasMask(relatedDataMask, offeringpb.ServiceRelatedDataMask_PET_AVAILABILITY)
	if needPetAvailability {
		eg.Go(func() error {
			petTypes, err := s.petRepo.ListPetTypes(egCtx, companyID)
			if err != nil {
				return err
			}
			idToPetType = lo.SliceToMap(petTypes, func(pt *customerpb.BusinessPetTypeModel) (int64, int64) {
				return pt.GetId(), int64(pt.PetTypeId)
			})

			return nil
		})

		eg.Go(func() error {
			petBreeds, err := s.petRepo.ListPetBreeds(egCtx, companyID)
			if err != nil {
				return err
			}
			idToBreedName = lo.SliceToMap(petBreeds, func(pb *customerpb.BusinessPetBreedModel) (int64, string) {
				return pb.GetId(), strconv.FormatInt(pb.GetId(), 10)
			})

			return nil
		})
	}

	// wait
	if err := eg.Wait(); err != nil {
		return nil, nil, nil, err
	}

	return careTypeMap, idToPetType, idToBreedName, nil
}

func (s *InnerService) GetNewApplicableServiceList(
	ctx context.Context, req *offeringinnerpb.GetNewApplicableServiceListRequest) (
	*offeringinnerpb.GetNewApplicableServiceListResponse, error) {
	// 调用 listAvailableServices 获取服务列表
	switch req.ServiceType {
	case offeringinnerpb.ServiceType_SERVICE:
		// 构建ListAvailableServicesRequest
		request, err := s.buildListAvailableServicesRequest(ctx, req)
		if err != nil {
			return nil, err
		}
		response, err := s.serviceLogic.ListAvailableServices(ctx, request)
		if err != nil {
			return nil, err
		}
		availableServices := response.Services
		if len(availableServices) == 0 {
			return &offeringinnerpb.GetNewApplicableServiceListResponse{}, nil
		}

		// 获取 service
		servicesResp, err := s.serviceLogic.BatchGetServices(ctx, &offeringpb.BatchGetServicesRequest{
			Ids: lo.Map(availableServices, func(service *offeringpb.AvailableService, _ int) int64 {
				return service.Id
			}),
			RelatedDataMask: offeringpb.ServiceRelatedDataMask_ALL.Enum(),
		})
		if err != nil {
			return nil, err
		}
		services := servicesResp.Services
		if len(services) == 0 {
			return &offeringinnerpb.GetNewApplicableServiceListResponse{}, nil
		}

		// 并行拉取转换所需要的原始数据
		careTypeMap, idToPetType, idToBreedName, err := s.loadServiceMetadata(
			ctx, services, req.GetRelatedDataMask(), req.GetCompanyId())
		if err != nil {
			return nil, err
		}

		converter := NewServiceConverter(
			careTypeMap,
			idToPetType,
			idToBreedName,
			req.GetRelatedDataMask(),
		)

		// 转换为 proto 格式
		serviceModels := converter.ToServiceModels(services)

		return &offeringinnerpb.GetNewApplicableServiceListResponse{
			ServiceList: serviceModels,
		}, nil
	// TODO: addon
	case offeringinnerpb.ServiceType_ADDON:
		// 构建ListAvailableAddOnsRequest
		request, err := s.buildListAvailableAddOnsRequest(ctx, req)
		if err != nil {
			return nil, err
		}
		response, err := s.addonLogic.ListAvailableAddOns(ctx, request)
		if err != nil {
			return nil, err
		}
		availableAddOns := response.AddOns
		if len(availableAddOns) == 0 {
			return &offeringinnerpb.GetNewApplicableServiceListResponse{}, nil
		}
		// 获取 addon service
		addOnResp, err := s.serviceLogic.BatchGetServices(ctx, &offeringpb.BatchGetServicesRequest{
			Ids: lo.Map(availableAddOns, func(addOn *offeringpb.AvailableAddOn, _ int) int64 {
				return addOn.Id
			}),
			RelatedDataMask: offeringpb.ServiceRelatedDataMask_ALL.Enum(),
		})
		if err != nil {
			return nil, err
		}
		services := addOnResp.Services
		if len(services) == 0 {
			return &offeringinnerpb.GetNewApplicableServiceListResponse{}, nil
		}

		// 并行拉取转换所需要的原始数据
		careTypeMap, idToPetType, idToBreedName, err := s.loadServiceMetadata(
			ctx, services, req.GetRelatedDataMask(), req.GetCompanyId())
		if err != nil {
			return nil, err
		}

		converter := NewServiceConverter(
			careTypeMap,
			idToPetType,
			idToBreedName,
			req.GetRelatedDataMask(),
		)

		// 转换为 proto 格式
		serviceModels := converter.ToServiceModels(services)

		return &offeringinnerpb.GetNewApplicableServiceListResponse{
			ServiceList: serviceModels,
		}, nil
	}
	return &offeringinnerpb.GetNewApplicableServiceListResponse{}, nil
}

// GetNewApplicableServiceListRequest 转换成ListAvailableServicesRequest
func (s *InnerService) buildListAvailableServicesRequest(ctx context.Context,
	req *offeringinnerpb.GetNewApplicableServiceListRequest) (
	*offeringpb.ListAvailableServicesRequest, error) {
	request := &offeringpb.ListAvailableServicesRequest{}
	availabilityContext := &offeringpb.ListAvailableServicesRequest_AvailabilityContext{}
	filter := &offeringpb.ListAvailableServicesRequest_Filter{}

	if req == nil {
		return &offeringpb.ListAvailableServicesRequest{}, nil
	}

	if req.BusinessId != nil {
		request.OrganizationType = organizationpb.OrganizationType_BUSINESS
		request.OrganizationId = *req.BusinessId
	} else if req.CompanyId != 0 {
		request.OrganizationType = organizationpb.OrganizationType_COMPANY
		request.OrganizationId = req.CompanyId
	} else {
		return nil, fmt.Errorf("business_id or company_id must be specified")
	}
	if req.ServiceItemType != nil {
		// 根据ServiceItemType 映射成 CareCategory
		careCategory := offeringpb.CareCategory(req.ServiceItemType.Number())

		// 查询 careType
		careTypes, err := s.careTypeLogic.ListCareTypes(ctx, &offeringpb.ListCareTypesRequest{
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationId:   req.CompanyId,
		})
		if err != nil {
			return nil, err
		}
		// 根据 careCategory 筛选出符合的 careTypeId
		careTypePtr, _ := lo.Find(careTypes, func(careType *caretype.CareType) bool {
			return careType.CareCategory == careCategory
		})
		if careTypePtr != nil {
			careTypeID := careTypePtr.ID
			filter.CareTypeIds = append(filter.CareTypeIds, careTypeID)
		}
	}

	if req.PetId != nil {
		availabilityContext.PetIds = append(availabilityContext.PetIds, *req.PetId)
	}
	if req.Keyword != nil {
		filter.Keyword = req.Keyword
	}
	if req.Inactive != nil {
		if !*req.Inactive {
			filter.Statuses = append(filter.Statuses, offeringpb.Service_ACTIVE)
		} else {
			filter.Statuses = append(filter.Statuses, offeringpb.Service_INACTIVE)
		}
	}

	if req.Filter != nil {
		if req.Filter.FilterByService != nil {
			availabilityContext.ServiceIds = req.Filter.FilterByService.ServiceIds
		}
	}
	request.Context = availabilityContext
	request.Filter = filter

	return request, nil
}

// GetNewApplicableServiceListRequest 转换成ListAvailableAddOnsRequest
func (s *InnerService) buildListAvailableAddOnsRequest(ctx context.Context,
	req *offeringinnerpb.GetNewApplicableServiceListRequest) (
	*offeringpb.ListAvailableAddOnsRequest, error) {
	request := &offeringpb.ListAvailableAddOnsRequest{}
	availabilityContext := &offeringpb.ListAvailableAddOnsRequest_AvailabilityContext{}
	filter := &offeringpb.ListAvailableAddOnsRequest_Filter{}
	if req == nil {
		return &offeringpb.ListAvailableAddOnsRequest{}, nil
	}
	if req.BusinessId != nil {
		request.OrganizationType = organizationpb.OrganizationType_BUSINESS
		request.OrganizationId = *req.BusinessId
	} else if req.CompanyId != 0 {
		request.OrganizationType = organizationpb.OrganizationType_COMPANY
		request.OrganizationId = req.CompanyId
	}
	if req.PetId != nil {
		availabilityContext.PetIds = append(availabilityContext.PetIds, *req.PetId)
	}
	if req.Keyword != nil {
		filter.Keyword = req.Keyword
	}
	if req.Inactive != nil {
		if !*req.Inactive {
			filter.Statuses = append(filter.Statuses, offeringpb.AddOn_ACTIVE)
		} else {
			filter.Statuses = append(filter.Statuses, offeringpb.AddOn_INACTIVE)
		}
	}
	if req.Filter != nil {
		if req.Filter.FilterByService != nil {
			availabilityContext.ServiceIds = req.Filter.FilterByService.ServiceIds
			if req.Filter.FilterByService.ServiceItemType != nil {
				// 根据ServiceItemType 映射成 CareCategory
				careCategory := offeringpb.CareCategory(req.ServiceItemType.Number())

				// 查询 careType
				careTypes, err := s.careTypeLogic.ListCareTypes(ctx, &offeringpb.ListCareTypesRequest{
					OrganizationType: organizationpb.OrganizationType_COMPANY,
					OrganizationId:   req.CompanyId,
				})
				if err != nil {
					return nil, err
				}
				// 根据 careCategory 筛选出符合的 careTypeId
				careTypePtr, _ := lo.Find(careTypes, func(careType *caretype.CareType) bool {
					return careType.CareCategory == careCategory
				})
				if careTypePtr != nil {
					careTypeID := careTypePtr.ID
					availabilityContext.CareTypeId = &careTypeID
				}
			}
		}
	}
	request.Context = availabilityContext
	request.Filter = filter

	return request, nil
}
