package service

import (
	"context"
	"strconv"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/pet"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type InnerService struct {
	offeringinnerpb.UnimplementedGroomingServiceServiceServer
	serviceLogic  *service.Logic
	careTypeLogic *caretype.Logic
	petRepo       pet.Repository
}

func NewInnerService() *InnerService {
	return &InnerService{
		serviceLogic:  service.NewLogic(),
		careTypeLogic: caretype.NewLogic(),
		petRepo:       pet.NewRepository(),
	}
}

func (s *InnerService) BatchGetServices(
	ctx context.Context,
	req *offeringinnerpb.BatchGetServicesRequest,
) (*offeringinnerpb.BatchGetServicesResponse, error) {
	res, err := s.serviceLogic.BatchGetServices(ctx, &offeringpb.BatchGetServicesRequest{
		Ids:             req.GetIds(),
		RelatedDataMask: req.RelatedDataMask,
	})
	if err != nil {
		return nil, err
	}
	if len(res.GetServices()) == 0 {
		return &offeringinnerpb.BatchGetServicesResponse{}, nil
	}

	var companyID int64 = 101005 // TODO 待替换

	// 并行拉取转换所需要的原始数据
	var careTypeMap map[int64]*caretype.CareType
	var idToPetType map[int64]int64
	var idToBreedName map[int64]string

	eg, egCtx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		careTypeIDs := lo.Map(res.GetServices(), func(service *offeringpb.Service, _ int) int64 {
			return service.GetCareTypeId()
		})
		careTypes, err := s.careTypeLogic.BatchGetCareTypes(egCtx, careTypeIDs)
		if err != nil {
			return err
		}
		careTypeMap = lo.KeyBy(careTypes, func(careType *caretype.CareType) int64 {
			return careType.ID
		})

		return nil
	})

	// 掩码包含 PET_AVAILABILITY
	needPetAvailability := hasMask(req.GetRelatedDataMask(), offeringpb.ServiceRelatedDataMask_PET_AVAILABILITY)
	if needPetAvailability {
		eg.Go(func() error {
			petTypes, err := s.petRepo.ListPetTypes(egCtx, companyID)
			if err != nil {
				return err
			}
			idToPetType = lo.SliceToMap(petTypes, func(pt *customerpb.BusinessPetTypeModel) (int64, int64) {
				return pt.GetId(), int64(pt.PetTypeId)
			})

			return nil
		})

		eg.Go(func() error {
			petBreeds, err := s.petRepo.ListPetBreeds(egCtx, companyID)
			if err != nil {
				return err
			}
			idToBreedName = lo.SliceToMap(petBreeds, func(pb *customerpb.BusinessPetBreedModel) (int64, string) {
				return pb.GetId(), strconv.FormatInt(pb.GetId(), 10)
			})

			return nil
		})
	}

	// wait
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	converter := NewServiceConverter(
		careTypeMap,
		idToPetType,
		idToBreedName,
		req.GetRelatedDataMask(),
	)
	serviceModels := converter.ToServiceModels(res.GetServices())

	return &offeringinnerpb.BatchGetServicesResponse{
		Services: serviceModels,
	}, nil
}
