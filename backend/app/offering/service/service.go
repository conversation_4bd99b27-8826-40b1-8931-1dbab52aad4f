package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/category"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Service struct {
	logic         *service.Logic
	categoryLogic *category.Logic
	offeringpb.UnimplementedServiceServiceServer
}

func NewServiceService() *Service {
	return &Service{
		logic:         service.NewLogic(),
		categoryLogic: category.NewLogic(),
	}
}

func (t *Service) CreateService(ctx context.Context,
	req *offeringpb.CreateServiceRequest) (*offeringpb.CreateServiceResponse, error) {

	id, err := t.logic.CreateService(ctx, req.GetService())
	if err != nil {
		return nil, err
	}

	return &offeringpb.CreateServiceResponse{
		ServiceId: id,
	}, nil
}

func (t *Service) GetService(ctx context.Context,
	req *offeringpb.GetServiceRequest) (*offeringpb.GetServiceResponse, error) {
	service, err := t.logic.GetService(ctx, req.GetServiceId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.GetServiceResponse{
		Service: service,
	}, nil
}

func (t *Service) UpdateService(ctx context.Context,
	req *offeringpb.UpdateServiceRequest) (*offeringpb.UpdateServiceResponse, error) {

	err := t.logic.UpdateService(ctx, req.GetService())
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpdateServiceResponse{}, nil
}

func (t *Service) DeleteService(ctx context.Context,
	req *offeringpb.DeleteServiceRequest) (*offeringpb.DeleteServiceResponse, error) {
	err := t.logic.DeleteService(ctx, req.GetServiceId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.DeleteServiceResponse{}, nil
}

func (t *Service) ListServices(ctx context.Context,
	req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	return t.logic.ListServices(ctx, req)
}

func (t *Service) ListOBServices(ctx context.Context,
	req *offeringpb.ListOBServicesRequest) (*offeringpb.ListOBServicesResponse, error) {
	return t.logic.ListOBServices(ctx, req)
}

func (t *Service) UpdateOBService(ctx context.Context,
	req *offeringpb.UpdateOBServiceRequest) (*offeringpb.UpdateOBServiceResponse, error) {
	err := t.logic.UpdateOBService(ctx, req)
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpdateOBServiceResponse{}, nil
}

func (t *Service) ListAvailableServices(ctx context.Context,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {
	return t.logic.ListAvailableServices(ctx, req)
}

func (t *Service) BatchUpdateServices(ctx context.Context,
	req *offeringpb.BatchUpdateServicesRequest) (*offeringpb.BatchUpdateServicesResponse, error) {
	return t.logic.BatchUpdateServices(ctx, req)
}

func (t *Service) BatchGetServices(ctx context.Context,
	req *offeringpb.BatchGetServicesRequest) (*offeringpb.BatchGetServicesResponse, error) {
	return t.logic.BatchGetServices(ctx, req)
}

func (t *Service) ListPetOverride(ctx context.Context,
	req *offeringpb.ListPetOverrideRequest) (*offeringpb.ListPetOverrideResponse, error) {
	return t.logic.ListPetOverride(ctx, req)
}

func (t *Service) UpsertPetOverride(ctx context.Context,
	req *offeringpb.UpsertPetOverrideRequest) (*offeringpb.UpsertPetOverrideResponse, error) {
	petOverride, err := t.logic.UpsertPetOverride(ctx, req)
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpsertPetOverrideResponse{
		PetOverride: petOverride,
	}, nil
}

func (t *Service) UpdatePetOverride(ctx context.Context,
	req *offeringpb.UpdatePetOverrideRequest) (*offeringpb.UpdatePetOverrideResponse, error) {
	petOverride, err := t.logic.UpdatePetOverride(ctx, req)
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpdatePetOverrideResponse{
		PetOverride: petOverride,
	}, nil
}

func (t *Service) DeletePetOverride(ctx context.Context,
	req *offeringpb.DeletePetOverrideRequest) (*offeringpb.DeletePetOverrideResponse, error) {
	err := t.logic.DeletePetOverride(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.DeletePetOverrideResponse{}, nil
}

func (t *Service) ListServiceCategories(ctx context.Context,
	req *offeringpb.ListServiceCategoriesRequest) (*offeringpb.ListServiceCategoriesResponse, error) {
	return t.categoryLogic.ListServiceCategories(ctx, req)
}

func (t *Service) SaveServiceCategories(ctx context.Context,
	req *offeringpb.SaveServiceCategoriesRequest) (*offeringpb.SaveServiceCategoriesResponse, error) {
	return t.categoryLogic.SaveServiceCategories(ctx, req)
}

func (t *Service) BatchGetCustomizedServices(ctx context.Context,
	req *offeringpb.BatchGetCustomizedServicesRequest) (*offeringpb.BatchGetCustomizedServicesResponse, error) {
	customizedServices, err := t.logic.BatchGetCustomizedServices(ctx, req)
	if err != nil {
		return nil, err
	}

	return &offeringpb.BatchGetCustomizedServicesResponse{CustomizedServices: customizedServices}, nil
}
