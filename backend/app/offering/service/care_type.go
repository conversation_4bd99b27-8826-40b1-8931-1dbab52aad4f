package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype/attribute"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// CareTypeService implements the CareTypeServiceServer interface.
type CareTypeService struct {
	pb.UnimplementedCareTypeServiceServer
	logic          *caretype.Logic
	attributeLogic *attribute.Logic
}

// NewCareTypeService creates a new CareTypeService.
func NewCareTypeService() *CareTypeService {
	return &CareTypeService{
		logic:          caretype.NewLogic(),
		attributeLogic: attribute.NewLogic(),
	}
}

// CreateCareType creates a new care type.
func (s *CareTypeService) CreateCareType(
	ctx context.Context, req *pb.CreateCareTypeRequest) (*pb.CreateCareTypeResponse, error) {
	entity := caretype.ProtoToEntity(req.GetCareType())
	createdEntity, err := s.logic.CreateCareType(ctx, entity)
	if err != nil {
		return nil, err
	}

	return &pb.CreateCareTypeResponse{
		CareType: caretype.EntityToProto(createdEntity),
	}, nil
}

// GetCareType gets a care type by ID.
func (s *CareTypeService) GetCareType(
	ctx context.Context, req *pb.GetCareTypeRequest) (*pb.GetCareTypeResponse, error) {
	entity, err := s.logic.GetCareType(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &pb.GetCareTypeResponse{
		CareType: caretype.EntityToProto(entity),
	}, nil
}

// UpdateCareType updates a care type.
func (s *CareTypeService) UpdateCareType(
	ctx context.Context, req *pb.UpdateCareTypeRequest) (*pb.UpdateCareTypeResponse, error) {
	entity := caretype.ProtoToEntity(req.GetCareType())
	updatedEntity, err := s.logic.UpdateCareType(ctx, entity)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateCareTypeResponse{
		CareType: caretype.EntityToProto(updatedEntity),
	}, nil
}

// DeleteCareType deletes a care type.
func (s *CareTypeService) DeleteCareType(
	ctx context.Context, req *pb.DeleteCareTypeRequest) (*pb.DeleteCareTypeResponse, error) {
	err := s.logic.DeleteCareType(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &pb.DeleteCareTypeResponse{}, nil
}

// ListCareTypes lists care types.
func (s *CareTypeService) ListCareTypes(
	ctx context.Context, req *pb.ListCareTypesRequest) (*pb.ListCareTypesResponse, error) {
	entities, err := s.logic.ListCareTypes(ctx, req)
	if err != nil {
		return nil, err
	}

	return &pb.ListCareTypesResponse{
		CareTypes: caretype.EntityToProtoList(entities),
	}, nil
}

// CreateCareTypeAttribute creates a new care type attribute.
func (s *CareTypeService) CreateCareTypeAttribute(
	ctx context.Context, req *pb.CreateCareTypeAttributeRequest) (*pb.CreateCareTypeAttributeResponse, error) {
	entity := attribute.ProtoToEntity(req.GetCareTypeAttribute())
	createdEntity, err := s.attributeLogic.CreateCareTypeAttribute(ctx, entity)
	if err != nil {
		return nil, err
	}

	return &pb.CreateCareTypeAttributeResponse{
		CareTypeAttribute: attribute.EntityToProto(createdEntity),
	}, nil
}

// ListCareTypeAttributes lists care type attributes.
func (s *CareTypeService) ListCareTypeAttributes(
	ctx context.Context, req *pb.ListCareTypeAttributesRequest) (*pb.ListCareTypeAttributesResponse, error) {
	entities, err := s.attributeLogic.ListCareTypeAttributes(ctx, req.GetCareTypeId())
	if err != nil {
		return nil, err
	}

	return &pb.ListCareTypeAttributesResponse{
		CareTypeAttributes: attribute.EntityToProtoList(entities),
	}, nil
}

// DeleteCareTypeAttribute deletes a care type attribute.
func (s *CareTypeService) DeleteCareTypeAttribute(
	ctx context.Context, req *pb.DeleteCareTypeAttributeRequest) (*pb.DeleteCareTypeAttributeResponse, error) {
	if err := s.attributeLogic.DeleteCareTypeAttribute(ctx, req.GetId()); err != nil {
		return nil, err
	}

	return &pb.DeleteCareTypeAttributeResponse{}, nil
}
