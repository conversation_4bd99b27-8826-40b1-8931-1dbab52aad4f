package service

import (
	"context"
	"testing"
	"time"

	authv3 "github.com/envoyproxy/go-control-plane/envoy/service/auth/v3"
	jwt "github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/app/devops-auth/configinit"
)

func TestExtractTokenFromRequest(t *testing.T) {
	tests := []struct {
		name        string
		request     *authv3.CheckRequest
		expected    string
		expectError bool
	}{
		{
			name: "Valid token in cookie",
			request: &authv3.CheckRequest{
				Attributes: &authv3.AttributeContext{
					Request: &authv3.AttributeContext_Request{
						Http: &authv3.AttributeContext_HttpRequest{
							Headers: map[string]string{"cookie": "other=abc; auth_token=my-secret-token"},
						},
					},
				},
			},
			expected:    "my-secret-token",
			expectError: false,
		},
		{
			name: "No cookie header",
			request: &authv3.CheckRequest{
				Attributes: &authv3.AttributeContext{
					Request: &authv3.AttributeContext_Request{
						Http: &authv3.AttributeContext_HttpRequest{
							Headers: map[string]string{},
						},
					},
				},
			},
			expectError: true,
		},
		{
			name: "No auth_token in cookie",
			request: &authv3.CheckRequest{
				Attributes: &authv3.AttributeContext{
					Request: &authv3.AttributeContext_Request{
						Http: &authv3.AttributeContext_HttpRequest{
							Headers: map[string]string{"cookie": "other=abc"},
						},
					},
				},
			},
			expectError: true,
		},
		{
			name: "Empty auth_token in cookie",
			request: &authv3.CheckRequest{
				Attributes: &authv3.AttributeContext{
					Request: &authv3.AttributeContext_Request{
						Http: &authv3.AttributeContext_HttpRequest{
							Headers: map[string]string{"cookie": "auth_token="},
						},
					},
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := extractTokenFromRequest(tt.request)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, token)
			}
		})
	}
}

func TestValidateAndParseToken(t *testing.T) {
	secret := "my-super-secret-key"

	// Create a valid token
	validClaims := jwt.MapClaims{
		"email": "<EMAIL>",
		"sub":   "1234567890",
		"exp":   time.Now().Add(time.Hour).Unix(),
	}
	validToken, _ := jwt.NewWithClaims(jwt.SigningMethodHS256, validClaims).SignedString([]byte(secret))

	// Create an expired token
	expiredClaims := jwt.MapClaims{
		"email": "<EMAIL>",
		"sub":   "1234567890",
		"exp":   time.Now().Add(-time.Hour).Unix(),
	}
	expiredToken, _ := jwt.NewWithClaims(jwt.SigningMethodHS256, expiredClaims).SignedString([]byte(secret))

	tests := []struct {
		name        string
		tokenString string
		secret      string
		expected    jwt.MapClaims
		expectError bool
	}{
		{
			name:        "Valid token",
			tokenString: validToken,
			secret:      secret,
			expected:    validClaims,
			expectError: false,
		},
		{
			name:        "Invalid signature",
			tokenString: validToken,
			secret:      "wrong-secret",
			expectError: true,
		},
		{
			name:        "Expired token",
			tokenString: expiredToken,
			secret:      secret,
			expectError: true,
		},
		{
			name:        "Malformed token",
			tokenString: "not-a-real-token",
			secret:      secret,
			expectError: true,
		},
		{
			name: "Invalid signing method",
			tokenString: func() string {
				token, _ := jwt.NewWithClaims(jwt.SigningMethodRS256, validClaims).SignedString(nil)
				return token
			}(),
			secret:      secret,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := validateAndParseToken(tt.tokenString, tt.secret)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected["email"], claims["email"])
				assert.Equal(t, tt.expected["sub"], claims["sub"])
			}
		})
	}
}

func TestCheck(t *testing.T) {
	cfg := &configinit.Config{
		JWT: configinit.JWTConfig{
			Secret: "test-secret",
		},
	}
	s := &AuthService{cfg: cfg}

	// Create a valid token
	claims := jwt.MapClaims{
		"email": "<EMAIL>",
		"sub":   "1234567890",
		"exp":   time.Now().Add(time.Hour).Unix(),
	}
	token, _ := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(cfg.JWT.Secret))

	req := &authv3.CheckRequest{
		Attributes: &authv3.AttributeContext{
			Request: &authv3.AttributeContext_Request{
				Http: &authv3.AttributeContext_HttpRequest{
					Headers: map[string]string{"cookie": "auth_token=" + token},
				},
			},
		},
	}

	resp, err := s.Check(context.Background(), req)
	assert.NoError(t, err)
	assert.Equal(t, int32(codes.OK), resp.Status.Code)

	// Check for the email header
	emailHeader := resp.GetOkResponse().Headers[0].Header
	assert.Equal(t, "X-MOE-USER-EMAIL", emailHeader.Key)
	assert.Equal(t, "<EMAIL>", emailHeader.Value)

	// Check for the sub header
	subHeader := resp.GetOkResponse().Headers[1].Header
	assert.Equal(t, "X-MOE-USER-SUB", subHeader.Key)
	assert.Equal(t, "1234567890", subHeader.Value)
}
