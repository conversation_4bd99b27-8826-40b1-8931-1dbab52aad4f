package aistudio

import (
	"encoding/json"
	"testing"
)

func TestMCPConfig_String(t *testing.T) {
	config := NewMCPConfig(
		"mcp-atlassian",
		"uvx",
		"stdio",
		[]string{
			"mcp-atlassian",
			"--jira-url={{jira-url}}",
			"--jira-username={{jira-username}}",
			"--jira-token={{jira-token}}",
		},
		map[string]string{},
		map[string]string{
			"jira-url":      "https://moego.atlassian.net",
			"jira-username": "<EMAIL>",
			"jira-token":    "ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_vp2MoDFQevHEhTBdq1t4SgmE9yoIim6pZdC2CFqzpgq-mA9kQHuLipoblU2cy_yGmmNkG7K2bpI-UgfPQaoJPfLB50NX4DgmSiTkSQImTJ56y8QO48339kHl50vCV6ygMQQBWXwQaCo53vJks=E96C7984",
		},
	)

	expected := `{"timeout":60,"command":"uvx","args":["mcp-atlassian","--jira-url=https://moego.atlassian.net","--jira-username=<EMAIL>","--jira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_vp2MoDFQevHEhTBdq1t4SgmE9yoIim6pZdC2CFqzpgq-mA9kQHuLipoblU2cy_yGmmNkG7K2bpI-UgfPQaoJPfLB50NX4DgmSiTkSQImTJ56y8QO48339kHl50vCV6ygMQQBWXwQaCo53vJks=E96C7984"],"env":{},"transportType":"stdio"}`
	actual := config.String()

	if actual != expected {
		t.Errorf("String() = %s, want %s", actual, expected)
	}

	jsData, _ := json.Marshal(config)
	if string(jsData) != expected {
		t.Errorf("String() = %s, want %s", actual, expected)
	}
}
