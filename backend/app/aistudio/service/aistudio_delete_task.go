package service

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// DeleteAiStudioTask deletes an AI Studio task.
func (s *AiStudioService) DeleteAiStudioTask(ctx context.Context,
	req *toolspb.DeleteAiStudioTaskRequest) (*emptypb.Empty, error) {
	repo := aistudio.New()
	task, err := repo.GetTask(ctx, req.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if task == nil {
		return nil, fmt.Errorf("task not found with id: %d", req.Id)
	}

	err = repo.DeleteTask(ctx, req.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to delete task: %w", err)
	}

	s.crontab.Remove(fmt.Sprint(req.Id))

	return &emptypb.Empty{}, nil
}
