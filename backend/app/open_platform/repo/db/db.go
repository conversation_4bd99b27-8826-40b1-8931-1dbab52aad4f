package db

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	database *gorm.DB
	once     sync.Once
)

func SetDB(db *gorm.DB) {
	database = db
}

func GetDB() *gorm.DB {
	if database == nil {
		NewDB()
	}

	return database
}

func NewDB() {
	once.Do(func() {
		db, err := igorm.NewClientProxy("postgres.moego_open_platform")
		if err != nil {
			panic(err)
		}
		database = db
	})
}
