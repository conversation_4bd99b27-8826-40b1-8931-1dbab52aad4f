package configloader

import (
	"fmt"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
)

type Config struct {
	CsPageWatcher struct {
		JiraToken string `yaml:"jira_token"`
		JiraEmail string `yaml:"jira_email"`

		SlackToken              string `yaml:"slack_token"`
		ProductUpdateSlackToken string `yaml:"product_update_slack_token"`

		T1SlackChannelID string `yaml:"t1_slack_channel_id"`

		DatadogAPIKey        string `yaml:"datadog_api_key"`
		DatadogAPPKey        string `yaml:"datadog_app_key"`
		SLAReminderChannelID string `yaml:"sla_reminder_channel_id"`

		GoogleCredentialJSON   string `yaml:"google_credential_json"`
		ProductUpdateSheetID   string `yaml:"product_update_sheet_id"`
		ProductUpdateSheetName string `yaml:"product_update_sheet_name"`
	} `yaml:"cs_page_watcher"`
}

var initCfg sync.Once

func Init(dir string) *Config {
	var cfg *Config
	initCfg.Do(func() {
		cfg = InitLocalConfig(dir)
	})

	return cfg
}

func InitLocalConfig(dir string) *Config {
	var cfg *Config
	c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/cs_page_watcher.yaml", dir, env.GetEnv()))
	if err != nil {
		panic(err)
	}

	cfg = &Config{}
	err = c.Unmarshal(cfg)
	if err != nil {
		panic(err)
	}

	return cfg
}
