package pkg

import (
	"context"
	"time"
)

// DetachedContext 是一个特殊的context实现，它保留了原始context的所有值，
// 但不会因为原始context的取消而被取消。这对于在HTTP请求结束后仍需要
// 继续执行的goroutine非常有用。
type DetachedContext struct {
	valueCtx context.Context // 用于存储原始context的值
}

// NewDetachedContext 创建一个新的DetachedContext，它保留原始context的所有值
// 但不会被取消。
func NewDetachedContext(original context.Context) context.Context {
	return &DetachedContext{
		valueCtx: original,
	}
}

// NewDetachedContextWithTimeout 创建一个新的DetachedContext，它保留原始context的所有值
// 但不会因为原始context被取消而被取消，同时设置一个新的超时时间。
func NewDetachedContextWithTimeout(
	original context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	detached := &DetachedContext{
		valueCtx: original,
	}

	return context.WithTimeout(detached, timeout)
}

// Deadline 返回零时间和false，表示这个context没有截止时间
func (d *DetachedContext) Deadline() (deadline time.Time, ok bool) {
	return time.Time{}, false
}

// Done 返回nil，表示这个context永远不会被取消
func (d *DetachedContext) Done() <-chan struct{} {
	return nil
}

// Err 总是返回nil，因为这个context永远不会被取消
func (d *DetachedContext) Err() error {
	return nil
}

// Value 从原始context中获取值，保持所有原始的键值对
func (d *DetachedContext) Value(key interface{}) interface{} {
	return d.valueCtx.Value(key)
}
