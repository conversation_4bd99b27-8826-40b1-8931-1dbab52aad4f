package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1"
)

// runProductUpdateTask handles the ProductUpdate task
// It parses usergroups from params, gets all user emails, and sends message with OK button to each user
func (p *PageWatcherIns) runProductUpdateTask(
	ctx context.Context,
	req *pb.RunTaskRequest,
) (*pb.DefaultResponse, error) {
	log.InfoContextf(ctx, "runProductUpdateTask: starting product update notification task")

	// Extract parameters from req.Params
	text, exists := req.Params["text"]
	if !exists || text == "" {
		return &pb.DefaultResponse{
			Message:   "product update task: text parameter is required",
			ErrorCode: 1,
		}, nil
	}

	// Extract usergroups from params (it's a comma-separated string)
	usergroupsStr, exists := req.Params["usergroups"]
	if !exists || usergroupsStr == "" {
		return &pb.DefaultResponse{
			Message:   "product update task: usergroups parameter is required",
			ErrorCode: 1,
		}, nil
	}

	// Parse usergroups string (comma-separated) to array
	usergroupIDs := strings.Split(usergroupsStr, ",")

	// Trim whitespace from each ID
	for i, id := range usergroupIDs {
		usergroupIDs[i] = strings.TrimSpace(id)
	}

	// Get all user emails from usergroups
	userEmails, err := p.productSlackClient.GetUserEmailsByUsergroups(usergroupIDs)
	if err != nil {
		log.ErrorContextf(ctx, "runProductUpdateTask: failed to get user emails by usergroups: %v", err)

		return &pb.DefaultResponse{
			Message:   fmt.Sprintf("product update task: failed to get user emails: %v", err),
			ErrorCode: 1,
		}, nil
	}

	// Extract origin_ts from params to use as button value
	originTS, exists := req.Params["origin_ts"]
	if !exists || originTS == "" {
		originTS = "" // Default to empty if not provided
	}

	// Create button value in the format "ok_button-$origin_ts"
	buttonValue := "ok_button" // Default value if origin_ts is empty
	if originTS != "" {
		buttonValue = fmt.Sprintf("ok_button-%s", originTS)
	}

	// Extract image URLs from params (file0, file1, etc.)
	var imageUrls []string
	for i := 0; ; i++ {
		fileKey := fmt.Sprintf("file%d", i)
		if i == 0 {
			// For backward compatibility, also check "file0"
			fileKey = "file0"
		}

		fileURL, exists := req.Params[fileKey]
		if !exists || fileURL == "" {
			// If we're checking the first file and "file0" doesn't exist,
			// also try just "file" key
			if i == 0 {
				fileURL, exists = req.Params["file"]
				if !exists || fileURL == "" {
					break // No more files
				}
			} else {
				break // No more files
			}
		}

		imageUrls = append(imageUrls, fileURL)
	}

	// Send message to each user with OK button
	for _, email := range userEmails {
		log.InfoContextf(ctx, "runProductUpdateTask: sending product update message to user: %s", email)

		var err error
		if len(imageUrls) > 0 {
			// Send message with images and OK button
			err = p.productSlackClient.SendMessageToPersonWithOKButton(email, text, buttonValue, imageUrls...)
		} else {
			// Send message with OK button only
			err = p.productSlackClient.SendMessageToPersonWithOKButton(email, text, buttonValue)
		}

		if err != nil {
			log.ErrorContextf(ctx, "runProductUpdateTask: failed to send message to user %s: %v", email, err)
			// Continue with other users even if one fails
		}
	}

	log.InfoContextf(
		ctx,
		"runProductUpdateTask: successfully sent product update notifications to %d users",
		len(userEmails),
	)

	return &pb.DefaultResponse{
		Message:   fmt.Sprintf("product update task completed successfully, sent to %d users", len(userEmails)),
		ErrorCode: 0,
	}, nil
}
