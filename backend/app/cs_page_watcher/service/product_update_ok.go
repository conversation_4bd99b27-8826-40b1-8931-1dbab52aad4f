package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1"
)

// runProductUpdateOKTask handles the ProductUpdateOK task
// When user clicks the OK button, it records the data to Google Sheet
func (p *PageWatcherIns) runProductUpdateOKTask(
	ctx context.Context,
	req *pb.RunTaskRequest,
) (*pb.DefaultResponse, error) {
	log.InfoContextf(ctx, "runProductUpdateOKTask: processing product update OK confirmation")

	// Extract required parameters from req.Params
	username, exists := req.Params["username"]
	if !exists || username == "" {
		return &pb.DefaultResponse{
			Message:   "product update OK task: username parameter is required",
			ErrorCode: 1,
		}, nil
	}

	messageText, exists := req.Params["message.text"]
	if !exists || messageText == "" {
		return &pb.DefaultResponse{
			Message:   "product update OK task: message.text parameter is required",
			ErrorCode: 1,
		}, nil
	}

	okActionValue, exists := req.Params["ok_action_block.value"]
	if !exists || okActionValue == "" {
		return &pb.DefaultResponse{
			Message:   "product update OK task: ok_action_block.value parameter is required",
			ErrorCode: 1,
		}, nil
	}

	// Extract origin_ts from ok_action_block.value by taking the part after the last '-'
	originTS := okActionValue
	if idx := strings.LastIndex(okActionValue, "-"); idx != -1 {
		originTS = okActionValue[idx+1:]
	}

	// Extract image URL if exists
	imageURL := ""
	if file0, exists := req.Params["file0"]; exists && file0 != "" {
		imageURL = file0
	}

	// Get current time for insert_time
	insertTime := time.Now().Format("2006-01-02 15:04:05")

	// Insert data to Google Sheet
	// Note: These should be configured in your config
	sheetID := p.cfg.CsPageWatcher.ProductUpdateSheetID
	sheetName := p.cfg.CsPageWatcher.ProductUpdateSheetName

	if sheetID == "" || sheetName == "" {
		log.ErrorContextf(ctx, "runProductUpdateOKTask: sheet ID or sheet name not configured")

		return &pb.DefaultResponse{
			Message:   "sheet configuration not found",
			ErrorCode: 1,
		}, nil
	}

	// Prepare row data: origin_ts, username, message.text, image, insert_time
	rowData := []interface{}{
		originTS,
		username,
		messageText,
		imageURL,
		insertTime,
	}

	err := p.sheetClient.InsertRow(
		sheetID,
		sheetName,
		rowData,
	)

	if err != nil {
		log.ErrorContextf(ctx, "runProductUpdateOKTask: failed to insert row to sheet: %v", err)

		return &pb.DefaultResponse{
			Message:   fmt.Sprintf("failed to record data to sheet: %v", err),
			ErrorCode: 1,
		}, nil
	}

	log.InfoContextf(
		ctx,
		"runProductUpdateOKTask: successfully recorded product update confirmation for user: %s",
		username,
	)

	return &pb.DefaultResponse{
		Message:   fmt.Sprintf("product update OK task completed for user %s", username),
		ErrorCode: 0,
	}, nil
}
