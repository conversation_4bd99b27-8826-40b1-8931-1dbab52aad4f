package slack

import (
	"errors"
	"testing"

	"github.com/slack-go/slack"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAPI is a mock implementation of the API interface
type MockAPI struct {
	mock.Mock
}

func (m *MockAPI) GetUserByEmail(email string) (*slack.User, error) {
	args := m.Called(email)
	if user, ok := args.Get(0).(*slack.User); ok {
		return user, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockAPI) InviteUsersToConversation(channelID string, users ...string) (*slack.Channel, error) {
	args := m.Called(channelID, users)
	if channel, ok := args.Get(0).(*slack.Channel); ok {
		return channel, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockAPI) AddReaction(name string, item slack.ItemRef) error {
	args := m.Called(name, item)
	return args.Error(0)
}

func (m *MockAPI) JoinConversation(channelID string) (*slack.Channel, string, []string, error) {
	args := m.Called(channelID)
	if channel, ok := args.Get(0).(*slack.Channel); ok {
		return channel, args.String(1), args.Get(2).([]string), args.Error(3)
	}
	return nil, "", nil, args.Error(3)
}

func (m *MockAPI) PostMessage(channelID string, options ...slack.MsgOption) (string, string, error) {
	args := m.Called(channelID, options)
	return args.String(0), args.String(1), args.Error(2)
}

func (m *MockAPI) OpenConversation(params *slack.OpenConversationParameters) (*slack.Channel, bool, bool, error) {
	args := m.Called(params)
	if channel, ok := args.Get(0).(*slack.Channel); ok {
		return channel, args.Bool(1), args.Bool(2), args.Error(3)
	}
	return nil, false, false, args.Error(3)
}

func (m *MockAPI) CreateConversation(params slack.CreateConversationParams) (*slack.Channel, error) {
	args := m.Called(params)
	if channel, ok := args.Get(0).(*slack.Channel); ok {
		return channel, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockAPI) ArchiveConversation(channelID string) error {
	args := m.Called(channelID)
	return args.Error(0)
}

func (m *MockAPI) GetUserGroupMembers(userGroup string) ([]string, error) {
	args := m.Called(userGroup)
	if userIDs, ok := args.Get(0).([]string); ok {
		return userIDs, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockAPI) GetUserInfo(userID string) (*slack.User, error) {
	args := m.Called(userID)
	if user, ok := args.Get(0).(*slack.User); ok {
		return user, args.Error(1)
	}
	return nil, args.Error(1)
}

func TestAddMembersToChannel_Filter(t *testing.T) {
	// Create a mock API
	mockAPI := new(MockAPI)

	// Create a slackClient with the mock API
	client := &slackClient{
		api: mockAPI,
	}

	// Set up expectations for the mock
	// Valid user
	mockAPI.On("GetUserByEmail", "<EMAIL>").Return(&slack.User{ID: "U123456"}, nil)
	// Invalid user that will return an error
	mockAPI.On("GetUserByEmail", "<EMAIL>").Return((*slack.User)(nil), errors.New("user not found"))
	// Empty email should not trigger an API call since LookUpByEmail handles it directly

	// Expect InviteUsersToConversation to be called with only valid users
	mockAPI.On("InviteUsersToConversation", "C123456", []string{"U123456"}).Return(&slack.Channel{}, nil)

	// Test the AddMembersToChannel function with a mix of valid, invalid, and empty emails
	err := client.AddMembersToChannel("C123456", []string{"<EMAIL>", "<EMAIL>", ""})

	// Assertions
	assert.NoError(t, err)

	// Verify the mock expectations
	mockAPI.AssertExpectations(t)

	// Verify that GetUserByEmail was called for valid and invalid emails, but not for empty email
	mockAPI.AssertCalled(t, "GetUserByEmail", "<EMAIL>")
	mockAPI.AssertCalled(t, "GetUserByEmail", "<EMAIL>")
	mockAPI.AssertNotCalled(t, "GetUserByEmail", "") // Empty email doesn't trigger API call

	// Verify that InviteUsersToConversation was called with only the valid user
	mockAPI.AssertCalled(t, "InviteUsersToConversation", "C123456", []string{"U123456"})
}

func TestAddMembersToChannel_NoValidUsers(t *testing.T) {
	// Create a mock API
	mockAPI := new(MockAPI)

	// Create a slackClient with the mock API
	client := &slackClient{
		api: mockAPI,
	}

	// Set up expectations for the mock - all users are invalid
	mockAPI.On("GetUserByEmail", "<EMAIL>").Return((*slack.User)(nil), errors.New("user not found"))
	mockAPI.On("GetUserByEmail", "<EMAIL>").Return((*slack.User)(nil), errors.New("user not found"))

	// Test the AddMembersToChannel function with only invalid emails
	err := client.AddMembersToChannel("C123456", []string{"<EMAIL>", "<EMAIL>"})

	// Assertions - should not return an error when no valid users found
	assert.NoError(t, err)

	// Verify the mock expectations
	mockAPI.AssertExpectations(t)

	// Verify that GetUserByEmail was called for all emails
	mockAPI.AssertCalled(t, "GetUserByEmail", "<EMAIL>")
	mockAPI.AssertCalled(t, "GetUserByEmail", "<EMAIL>")

	// Verify that InviteUsersToConversation was never called
	mockAPI.AssertNotCalled(t, "InviteUsersToConversation")
}
