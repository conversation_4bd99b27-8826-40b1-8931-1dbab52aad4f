package google

import (
	"context"
	"fmt"

	"google.golang.org/api/option"
	sheets "google.golang.org/api/sheets/v4"
)

// SheetClient 定义Google Sheets客户端接口
type SheetClient interface {
	InsertRow(sheetID, sheetName string, rowData []interface{}) error
}

// sheetClient Google Sheets客户端实现
type sheetClient struct {
	service *sheets.Service
}

// NewSheetClient 创建并返回一个新的SheetClient实例
func NewSheetClient(credentialJSON []byte) (SheetClient, error) {
	ctx := context.Background()

	service, err := sheets.NewService(ctx, option.WithCredentialsJSON(credentialJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create sheets service: %w", err)
	}

	return &sheetClient{
		service: service,
	}, nil
}

// InsertRow 在指定的工作表中插入一行数据
func (s *sheetClient) InsertRow(sheetID, sheetName string, rowData []interface{}) error {
	// 准备值范围
	valueRange := &sheets.ValueRange{
		Values: [][]interface{}{rowData},
	}

	// 指定范围 (工作表名称和范围)
	rangeName := fmt.Sprintf("%s!A1:Z1", sheetName)

	// 调用Google Sheets API插入数据
	_, err := s.service.Spreadsheets.Values.Append(sheetID, rangeName, valueRange).
		ValueInputOption("RAW").
		InsertDataOption("INSERT_ROWS").
		Do()

	if err != nil {
		return fmt.Errorf("failed to insert row to sheet: %w", err)
	}

	return nil
}
