load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "google",
    srcs = ["sheet.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/google",
    visibility = ["//visibility:public"],
    deps = [
        "@org_golang_google_api//option",
        "@org_golang_google_api//sheets/v4:sheets",
    ],
)

go_test(
    name = "google_test",
    srcs = ["sheet_test.go"],
    embed = [":google"],
    deps = ["//backend/app/cs_page_watcher/configloader"],
)
