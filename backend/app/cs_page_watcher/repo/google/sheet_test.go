package google

import (
	"testing"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
)

// Manual test for sheet functionality
func TestInsertRow(t *testing.T) {
	t.Skip("manual test - requires Google service account and spreadsheet access")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	// Example usage - credential JSON string
	credentialJSON := cfg.CsPageWatcher.GoogleCredentialJSON // Update with your credential JSON
	sheetID := cfg.CsPageWatcher.ProductUpdateSheetID        // Update with your Google Sheet ID
	sheetName := cfg.CsPageWatcher.ProductUpdateSheetName    // Update with your sheet name

	client, err := NewSheetClient([]byte(credentialJSON))
	if err != nil {
		t.Fatalf("Failed to create sheet client: %v", err)
	}

	rowData := []interface{}{
		"**********.488979",             // origin_ts
		"test_user",                     // username
		"Test message text",             // message.text
		"https://example.com/image.png", // image
		"2025-01-01 12:00:00",           // insert_time
	}

	err = client.InsertRow(sheetID, sheetName, rowData)
	if err != nil {
		t.Fatalf("Failed to insert row: %v", err)
	}

	t.Logf("Successfully inserted row to sheet")
}
