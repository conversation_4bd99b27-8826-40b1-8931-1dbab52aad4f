//nolint:errcheck // Mock file, error checking not required
package mocks

import (
	"github.com/slack-go/slack"
	"github.com/stretchr/testify/mock"
)

// MockSlackClient is a mock implementation of the slack.Client interface
type MockSlackClient struct {
	mock.Mock
}

func (m *MockSlackClient) AddMembersToChannel(channelID string, memberEmails []string) error {
	args := m.Called(channelID, memberEmails)

	return args.Error(0)
}

func (m *MockSlackClient) AddEmojiToMessage(channelID string, messageTS string, emoji string) error {
	args := m.Called(channelID, messageTS, emoji)

	return args.Error(0)
}

func (m *MockSlackClient) JoinChannel(channelID string) error {
	args := m.Called(channelID)

	return args.Error(0)
}

func (m *MockSlackClient) CreateChannel(channelName string) (*slack.Channel, error) {
	args := m.Called(channelName)

	return args.Get(0).(*slack.Channel), args.Error(1)
}

func (m *MockSlackClient) ArchiveChannel(channelID string) error {
	args := m.Called(channelID)

	return args.Error(0)
}

func (m *MockSlackClient) SendMessage(channelID string, message string) (string, error) {
	args := m.Called(channelID, message)

	return args.String(0), args.Error(1)
}

func (m *MockSlackClient) SendMessageToThread(channelID string, messageTS string, message string) error {
	args := m.Called(channelID, messageTS, message)

	return args.Error(0)
}

func (m *MockSlackClient) SendMessageToPerson(memberEmail string, message string, picUrls ...string) error {
	args := m.Called(memberEmail, message, picUrls)

	return args.Error(0)
}

func (m *MockSlackClient) SendMessageToPersonWithOKButton(
	memberEmail string,
	message string,
	buttonValue string,
	picUrls ...string,
) error {
	args := m.Called(memberEmail, message, buttonValue, picUrls)

	return args.Error(0)
}

func (m *MockSlackClient) GetUserEmailsByUsergroups(usergroupIDs []string) ([]string, error) {
	args := m.Called(usergroupIDs)

	return args.Get(0).([]string), args.Error(1)
}

func (m *MockSlackClient) LookUpByEmail(emailList []string) []string {
	args := m.Called(emailList)

	return args.Get(0).([]string)
}
