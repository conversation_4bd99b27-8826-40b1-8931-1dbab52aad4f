package contact

//go:generate mockgen -source=./contact.go -destination=./mock/contact_mock.go -package=mock

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_relation"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, contact *Contact) (*Contact, error)
	Save(ctx context.Context, contact *Contact) (*Contact, error)
	Get(ctx context.Context, id int64) (*Contact, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, contact *Contact, isInTx bool) (*Contact, error)
	Delete(ctx context.Context, id int64) error
	WithTx(tx *gorm.DB) Repository

	// Query methods for CustomerQueryService
	// SearchContactCustomerIDs 根据关键词搜索联系方式关联的客户ID
	SearchContactCustomerIDs(
		ctx context.Context, companyID int64, businessIDs []int64, keyword, nameKeyword string,
	) ([]int64, error)
	// FilterContactCustomerIDs 根据过滤条件查询联系方式关联的客户ID
	FilterContactCustomerIDs(ctx context.Context, filter *FilterConditions) ([]int64, error)
	// CountFilterContactCustomerIDs 根据计数条件过滤联系方式关联的客户ID
	CountFilterContactCustomerIDs(
		ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
		havingConditions []HavingCondition,
	) ([]int64, error)
	// GetPrimaryPhones 获取指定客户的主要电话号码
	GetPrimaryPhones(
		ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
	) ([]CustomerPhone, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, contact *Contact) (*Contact, error) {
	err := i.db.WithContext(ctx).Create(contact).Error
	if err != nil {
		return nil, err
	}

	return contact, nil
}

func (i *impl) Save(ctx context.Context, contact *Contact) (*Contact, error) {
	err := i.db.WithContext(ctx).Save(contact).Error
	if err != nil {
		return nil, err
	}

	return contact, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*Contact, error) {
	var contact Contact

	if err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state = ?", customerpb.Contact_ACTIVE.String()).
		First(&contact).Error; err != nil {
		return nil, err
	}

	return &contact, nil
}

// ListWithCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Contact{})
	query = i.applyFilter(query, filter)

	// 如果使用offset分页，则不应用cursor
	if pagination.Offset != nil && *pagination.Offset > 0 {
		query = query.Offset(int(*pagination.Offset))
	} else {
		query = i.applyCursor(query, pagination.Cursor, orderBy)
	}

	query = i.applyOrderBy(query, orderBy)

	// 当 PageSize=0 时返回所有数据，否则多查一条用于判断是否有下一页
	if pagination.PageSize > 0 {
		query = query.Limit(int(pagination.PageSize + 1))
	}

	var contacts []*Contact
	if err := query.Find(&contacts).Error; err != nil {
		return nil, err
	}

	var hasNext bool
	if pagination.PageSize > 0 {
		hasNext = len(contacts) > int(pagination.PageSize)
		if hasNext {
			contacts = contacts[:pagination.PageSize]
		}
	}

	return &CursorResult{
		Data:       contacts,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if len(filter.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", filter.CustomerIDs)
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	if len(filter.Phones) > 0 {
		query = query.Where("phone IN (?)", filter.Phones)
	}

	if len(filter.Emails) > 0 {
		query = query.Where("email IN (?)", filter.Emails)
	}

	if filter.OrganizationRef != nil {
		query = query.Where("organization_type = ?", filter.OrganizationRef.GetType().String())
		query = query.Where("organization_id = ?", filter.OrganizationRef.GetId())
	}

	if len(filter.TagTypes) > 0 {
		tagTypeStrings := make([]string, 0, len(filter.TagTypes))
		for _, tagType := range filter.TagTypes {
			tagTypeStrings = append(tagTypeStrings, tagType.String())
		}
		query = query.Joins("JOIN contact_tag_relation ctr "+
			"ON ctr.contact_id = contact.id AND ctr.deleted_time IS NULL").
			Joins("JOIN contact_tag ct ON ct.id = ctr.tag_id AND ct.deleted_time IS NULL").
			Where("ct.type IN (?)", tagTypeStrings).
			Distinct()
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.Direction_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
				orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Contact{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, contact *Contact, isInTx bool) (*Contact, error) {
	var tx *gorm.DB
	if !isInTx {
		tx = i.db.WithContext(ctx).Begin()
		defer tx.Rollback()
	} else {
		tx = i.db
	}

	if err := tx.Clauses(clause.Returning{}).Updates(contact).Error; err != nil {
		return nil, err
	}

	// 2. 更新联系人标签，并绑定关系
	// 2.1 查询数据库中已绑定的 tag id
	var oldTagIDs []int64
	if err := tx.Model(&contacttagrel.ContactTagRelation{}).
		Where("contact_id = ? and deleted_time is null", contact.ID).
		Pluck("tag_id", &oldTagIDs).Error; err != nil {
		return nil, err
	}

	// 2.2 提取新传入的 tag id
	newTagIDs := make([]int64, 0, len(contact.Tags))
	for _, tag := range contact.Tags {
		newTagIDs = append(newTagIDs, tag.ID)
	}

	// 2.3 计算需要新增和删除的 tag id
	toAdd := difference(newTagIDs, oldTagIDs)
	toDelete := difference(oldTagIDs, newTagIDs)

	// 2.4 新增绑定关系
	for _, tagID := range toAdd {
		var tag *contacttag.ContactTag
		for _, t := range contact.Tags {
			if t.ID == tagID {
				tag = t

				break
			}
		}
		if tag == nil {
			continue
		}
		rel := &contacttagrel.ContactTagRelation{
			OrganizationType: tag.OrganizationType,
			OrganizationID:   tag.OrganizationID,
			ContactID:        contact.ID,
			TagID:            tagID,
		}
		if err := tx.Create(rel).Error; err != nil {
			return nil, err
		}
	}

	// 2.5 删除多余的绑定关系
	if len(toDelete) > 0 {
		now := time.Now()
		if err := tx.Model(&contacttagrel.ContactTagRelation{}).
			Where("contact_id = ? AND tag_id IN (?)", contact.ID, toDelete).
			Update("deleted_time", now).Error; err != nil {
			return nil, err
		}
	}

	if !isInTx {
		if err := tx.Commit().Error; err != nil {
			return nil, err
		}
	}

	return contact, nil
}

// difference 返回 a 中有但 b 中没有的元素
func difference(a, b []int64) []int64 {
	m := make(map[int64]struct{}, len(b))
	for _, v := range b {
		m[v] = struct{}{}
	}
	var diff []int64
	for _, v := range a {
		if _, found := m[v]; !found {
			diff = append(diff, v)
		}
	}

	return diff
}

// Delete 删除联系人
func (i *impl) Delete(ctx context.Context, id int64) error {
	// 逻辑删除
	now := time.Now()

	return i.db.WithContext(ctx).Model(&Contact{}).
		Where("id = ?", id).
		Updates(&Contact{
			DeletedTime: &now,
			State:       customerpb.Contact_DELETED,
		}).Error
}

func (i *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

// SearchContactCustomerIDs 根据关键词搜索联系方式关联的客户ID
func (i *impl) SearchContactCustomerIDs(
	ctx context.Context, companyID int64, businessIDs []int64, keyword, nameKeyword string,
) ([]int64, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	var customerIDs []int64

	// 基础查询：从 contact 表查询，关联 customer_related_data 获取作用域信息
	query := i.db.WithContext(ctx).
		Table("contact c").
		Joins("JOIN customer_related_data crd ON crd.customer_id = c.customer_id").
		Joins("JOIN customer cu ON cu.id = c.customer_id").
		Where("c.deleted_time IS NULL").
		Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("c.organization_id = ?", companyID).
		Where("crd.state != ? AND crd.company_id = ?", customerpb.CustomerRelatedData_DELETED.String(), companyID).
		Where("cu.state != ?", customerpb.Customer_DELETED.String()).
		Where("cu.convert_to_customer_id = 0").
		Where("cu.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("cu.organization_id = ?", companyID)

	// 应用 business_ids 过滤
	if len(businessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", businessIDs)
	}

	// 构建关键词搜索条件
	var conditions []string
	var args []interface{}

	if keyword != "" {
		// 搜索邮箱和电话
		conditions = append(conditions, "(c.email ILIKE ? OR c.phone ILIKE ?)")
		likeKeyword := "%" + keyword + "%"
		args = append(args, likeKeyword, likeKeyword)
	}

	if nameKeyword != "" {
		// 搜索姓名
		conditions = append(conditions,
			"(c.given_name ILIKE ? OR c.family_name ILIKE ? OR CONCAT(c.given_name, ' ', c.family_name) ILIKE ?)")
		likeNameKeyword := "%" + nameKeyword + "%"
		args = append(args, likeNameKeyword, likeNameKeyword, likeNameKeyword)
	}

	if len(conditions) > 0 {
		query = query.Where(strings.Join(conditions, " OR "), args...)
	}

	if err := query.Distinct("c.customer_id").Pluck("c.customer_id", &customerIDs).Error; err != nil {
		return nil, fmt.Errorf("search contact customer ids: %w", err)
	}

	return customerIDs, nil
}

// FilterContactCustomerIDs 根据过滤条件查询联系方式关联的客户ID
func (i *impl) FilterContactCustomerIDs(ctx context.Context, filter *FilterConditions) ([]int64, error) {
	if filter == nil || filter.CompanyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	var customerIDs []int64

	// 基础查询
	query := i.db.WithContext(ctx).
		Table("contact c").
		Joins("JOIN customer_related_data crd ON crd.customer_id = c.customer_id").
		Joins("JOIN customer cu ON cu.id = c.customer_id").
		Where("c.deleted_time IS NULL").
		Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("c.organization_id = ?", filter.CompanyID).
		Where("crd.state != ? AND crd.company_id = ?",
			customerpb.CustomerRelatedData_DELETED.String(), filter.CompanyID).
		Where("cu.state != ?", customerpb.Customer_DELETED.String()).
		Where("cu.convert_to_customer_id = 0").
		Where("cu.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("cu.organization_id = ?", filter.CompanyID)

	// 应用 Scope 约束
	if len(filter.BusinessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", filter.BusinessIDs)
	}
	if len(filter.CustomerIDs) > 0 {
		query = query.Where("c.customer_id IN ?", filter.CustomerIDs)
	}

	// 构建过滤条件
	if len(filter.Conditions) > 0 {
		conditionSQL, args, err := i.buildContactFilterConditions(filter.Conditions, filter.Combiner)
		if err != nil {
			return nil, fmt.Errorf("build filter conditions: %w", err)
		}
		if conditionSQL != "" {
			query = query.Where(conditionSQL, args...)
		}
	}

	if err := query.Distinct("c.customer_id").Pluck("c.customer_id", &customerIDs).Error; err != nil {
		return nil, fmt.Errorf("filter contact customer ids: %w", err)
	}

	return customerIDs, nil
}

// CountFilterContactCustomerIDs 根据计数条件过滤联系方式关联的客户ID
func (i *impl) CountFilterContactCustomerIDs(
	ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64, havingConditions []HavingCondition,
) ([]int64, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	// 构建聚合查询
	query := i.db.WithContext(ctx).
		Table("contact c").
		Joins("JOIN customer_related_data crd ON crd.customer_id = c.customer_id").
		Joins("JOIN customer cu ON cu.id = c.customer_id").
		Where("c.deleted_time IS NULL").
		Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("c.organization_id = ?", companyID).
		Where("crd.state != ? AND crd.company_id = ?", customerpb.CustomerRelatedData_DELETED.String(), companyID).
		Where("cu.state != ?", customerpb.Customer_DELETED.String()).
		Where("cu.convert_to_customer_id = 0").
		Where("cu.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("cu.organization_id = ?", companyID)

	// 应用 Scope 约束
	if len(businessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", businessIDs)
	}
	if len(customerIDs) > 0 {
		query = query.Where("c.customer_id IN ?", customerIDs)
	}

	// 选择聚合字段
	query = query.Select(`
		c.customer_id,
		COUNT(*) as contact_count,
		COUNT(CASE WHEN c.email != '' AND c.email IS NOT NULL THEN 1 END) as email_count,
		COUNT(CASE WHEN c.phone != '' AND c.phone IS NOT NULL THEN 1 END) as phone_count
	`).Group("c.customer_id")

	// 构建 HAVING 条件
	if len(havingConditions) > 0 {
		havingSQL, args, err := i.buildHavingConditions(havingConditions)
		if err != nil {
			return nil, fmt.Errorf("build having conditions: %w", err)
		}
		if havingSQL != "" {
			query = query.Having(havingSQL, args...)
		}
	}

	// 定义结果结构体来接收聚合查询结果
	type AggregateResult struct {
		CustomerID   int64 `gorm:"column:customer_id"`
		ContactCount int64 `gorm:"column:contact_count"`
		EmailCount   int64 `gorm:"column:email_count"`
		PhoneCount   int64 `gorm:"column:phone_count"`
	}

	var results []AggregateResult
	if err := query.Find(&results).Error; err != nil {
		return nil, fmt.Errorf("count filter contact customer ids: %w", err)
	}

	// 提取 customer_id
	var resultIDs []int64
	for _, result := range results {
		resultIDs = append(resultIDs, result.CustomerID)
	}

	return resultIDs, nil
}

// GetPrimaryPhones 获取指定客户的主要电话号码
func (i *impl) GetPrimaryPhones(
	ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
) ([]CustomerPhone, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	// 查询每个客户的主要电话（优先选择 is_self=true 的联系方式）
	query := i.db.WithContext(ctx).
		Table("contact c").
		Joins("JOIN customer_related_data crd ON crd.customer_id = c.customer_id").
		Joins("JOIN customer cu ON cu.id = c.customer_id").
		Where("c.deleted_time IS NULL").
		Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("c.organization_id = ?", companyID).
		Where("c.phone != '' AND c.phone IS NOT NULL").
		Where("crd.state != ? AND crd.company_id = ?", customerpb.CustomerRelatedData_DELETED.String(), companyID).
		Where("cu.state != ?", customerpb.Customer_DELETED.String()).
		Where("cu.convert_to_customer_id = 0").
		Where("cu.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("cu.organization_id = ?", companyID)

	// 应用 Scope 约束
	if len(businessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", businessIDs)
	}
	if len(customerIDs) > 0 {
		query = query.Where("c.customer_id IN ?", customerIDs)
	}

	// 使用窗口函数选择每个客户的主要电话（优先 is_self=true）
	subQuery := query.Select(`
		c.customer_id,
		c.phone as phone_number,
		ROW_NUMBER() OVER (PARTITION BY c.customer_id ORDER BY c.is_self DESC, c.created_time ASC) as rn
	`)

	var phones []CustomerPhone
	if err := i.db.WithContext(ctx).
		Table("(?) as ranked_contacts", subQuery).
		Where("rn = 1").
		Select("customer_id, phone_number").
		Find(&phones).Error; err != nil {
		return nil, fmt.Errorf("get primary phones: %w", err)
	}

	return phones, nil
}

// buildContactFilterConditions 构建联系方式过滤条件SQL
func (i *impl) buildContactFilterConditions(
	conditions []FilterCondition, combiner string,
) (string, []interface{}, error) {
	if len(conditions) == 0 {
		return "", nil, nil
	}

	var sqlParts []string
	var args []interface{}

	for _, condition := range conditions {
		conditionSQL, conditionArgs, err := i.buildSingleContactCondition(condition)
		if err != nil {
			return "", nil, err
		}
		if conditionSQL != "" {
			sqlParts = append(sqlParts, conditionSQL)
			args = append(args, conditionArgs...)
		}
	}

	if len(sqlParts) == 0 {
		return "", nil, nil
	}

	// 默认使用 AND 连接
	if combiner != "OR" {
		combiner = "AND"
	}

	return "(" + strings.Join(sqlParts, " "+combiner+" ") + ")", args, nil
}

// buildSingleContactCondition 构建单个联系方式条件
func (i *impl) buildSingleContactCondition(condition FilterCondition) (string, []interface{}, error) {
	column, err := i.mapContactFieldToColumn(condition.Field)
	if err != nil {
		return "", nil, err
	}

	switch condition.Operator {
	case "EQUAL":
		return column + " = ?", []interface{}{condition.Value}, nil
	case "NOT_EQUAL":
		return column + " != ?", []interface{}{condition.Value}, nil
	case "LESS_THAN":
		return column + " < ?", []interface{}{condition.Value}, nil
	case "LESS_THAN_OR_EQUAL":
		return column + " <= ?", []interface{}{condition.Value}, nil
	case "GREATER_THAN":
		return column + " > ?", []interface{}{condition.Value}, nil
	case "GREATER_THAN_OR_EQUAL":
		return column + " >= ?", []interface{}{condition.Value}, nil
	case "FILTER_IN":
		if len(condition.Values) == 0 {
			return "", nil, fmt.Errorf("IN operator requires values")
		}
		placeholders := make([]string, len(condition.Values))
		args := make([]interface{}, len(condition.Values))
		for i, v := range condition.Values {
			placeholders[i] = "?"
			args[i] = v
		}

		return column + " IN (" + strings.Join(placeholders, ",") + ")", args, nil
	case "FILTER_NOT_IN":
		if len(condition.Values) == 0 {
			return "", nil, fmt.Errorf("NOT_IN operator requires values")
		}
		placeholders := make([]string, len(condition.Values))
		args := make([]interface{}, len(condition.Values))
		for i, v := range condition.Values {
			placeholders[i] = "?"
			args[i] = v
		}

		return column + " NOT IN (" + strings.Join(placeholders, ",") + ")", args, nil
	case "LIKE":
		value := strings.Trim(condition.Value, "'\"")

		return column + " ILIKE CONCAT('%', ?::text, '%')", []interface{}{value}, nil
	case "IS_NULL":
		return column + " IS NULL", nil, nil
	case "IS_NOT_NULL":
		return column + " IS NOT NULL", nil, nil
	case "BETWEEN":
		if condition.Start == "" || condition.End == "" {
			return "", nil, fmt.Errorf("BETWEEN operator requires both start and end values")
		}

		return column + " BETWEEN ? AND ?", []interface{}{condition.Start, condition.End}, nil
	default:
		return "", nil, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// mapContactFieldToColumn 将联系方式字段名映射到数据库列名
func (i *impl) mapContactFieldToColumn(field string) (string, error) {
	fieldMap := map[string]string{
		"contact_id":   "c.id",
		"customer_id":  "c.customer_id",
		"given_name":   "c.given_name",
		"family_name":  "c.family_name",
		"email":        "c.email",
		"phone":        "c.phone",
		"phone_number": "c.phone",
		"is_self":      "c.is_self",
		"note":         "c.note",
		"title":        "c.title",
		"state":        "c.state",
		"created_time": "c.created_time",
		"updated_time": "c.updated_time",
	}

	column, exists := fieldMap[field]
	if !exists {
		return "", fmt.Errorf("unsupported contact field: %s", field)
	}

	return column, nil
}

// buildHavingConditions 构建 HAVING 条件SQL
func (i *impl) buildHavingConditions(conditions []HavingCondition) (string, []interface{}, error) {
	if len(conditions) == 0 {
		return "", nil, nil
	}

	var sqlParts []string
	var args []interface{}

	for _, condition := range conditions {
		conditionSQL, conditionArgs, err := i.buildSingleHavingCondition(condition)
		if err != nil {
			return "", nil, err
		}
		if conditionSQL != "" {
			sqlParts = append(sqlParts, conditionSQL)
			args = append(args, conditionArgs...)
		}
	}

	if len(sqlParts) == 0 {
		return "", nil, nil
	}

	return strings.Join(sqlParts, " AND "), args, nil
}

// buildSingleHavingCondition 构建单个 HAVING 条件
func (i *impl) buildSingleHavingCondition(condition HavingCondition) (string, []interface{}, error) {
	// 映射字段名到对应的聚合表达式
	fieldExpressionMap := map[string]string{
		"contact_count": "COUNT(*)",
		"email_count":   "COUNT(CASE WHEN c.email != '' AND c.email IS NOT NULL THEN 1 END)",
		"phone_count":   "COUNT(CASE WHEN c.phone != '' AND c.phone IS NOT NULL THEN 1 END)",
	}

	fieldExpression, exists := fieldExpressionMap[condition.Field]
	if !exists {
		return "", nil, fmt.Errorf("unsupported having field: %s", condition.Field)
	}

	// 将枚举操作符映射到 SQL 操作符
	operatorMap := map[string]string{
		"EQUAL":                 "=",
		"NOT_EQUAL":             "!=",
		"LESS_THAN":             "<",
		"LESS_THAN_OR_EQUAL":    "<=",
		"GREATER_THAN":          ">",
		"GREATER_THAN_OR_EQUAL": ">=",
	}

	sqlOperator, exists := operatorMap[condition.Operator]
	if !exists {
		return "", nil, fmt.Errorf("unsupported having operator: %s", condition.Operator)
	}

	return fieldExpression + " " + sqlOperator + " ?", []interface{}{condition.Value}, nil
}
