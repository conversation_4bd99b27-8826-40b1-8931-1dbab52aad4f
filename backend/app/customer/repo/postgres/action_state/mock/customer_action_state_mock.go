// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/action_state/customer_action_state.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/action_state/customer_action_state.go -destination=./postgres/action_state/mock/customer_action_state_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/action_state"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, actionState *actionstate.CustomerActionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, actionState)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, actionState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, actionState)
}

// CreateBatch mocks base method.
func (m *MockReadWriter) CreateBatch(ctx context.Context, actionStates []*actionstate.CustomerActionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBatch", ctx, actionStates)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBatch indicates an expected call of CreateBatch.
func (mr *MockReadWriterMockRecorder) CreateBatch(ctx, actionStates any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBatch", reflect.TypeOf((*MockReadWriter)(nil).CreateBatch), ctx, actionStates)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id, staffID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id, staffID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id, staffID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id, staffID)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, datum *actionstate.ListActionStatesDatum) ([]*actionstate.CustomerActionState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, datum)
	ret0, _ := ret[0].([]*actionstate.CustomerActionState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, datum)
}

// Save mocks base method.
func (m *MockReadWriter) Save(ctx context.Context, actionState *actionstate.CustomerActionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, actionState)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockReadWriterMockRecorder) Save(ctx, actionState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockReadWriter)(nil).Save), ctx, actionState)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, actionState *actionstate.CustomerActionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, actionState)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, actionState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, actionState)
}

// WithTransaction mocks base method.
func (m *MockReadWriter) WithTransaction(arg0 context.Context, arg1 func(actionstate.ReadWriter) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTransaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTransaction indicates an expected call of WithTransaction.
func (mr *MockReadWriterMockRecorder) WithTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTransaction", reflect.TypeOf((*MockReadWriter)(nil).WithTransaction), arg0, arg1)
}
