// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/tx_manager.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/tx_manager.go -destination=./postgres/mock/tx_manager_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockTransactionManager is a mock of TransactionManager interface.
type MockTransactionManager struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionManagerMockRecorder
	isgomock struct{}
}

// MockTransactionManagerMockRecorder is the mock recorder for MockTransactionManager.
type MockTransactionManagerMockRecorder struct {
	mock *MockTransactionManager
}

// NewMockTransactionManager creates a new mock instance.
func NewMockTransactionManager(ctrl *gomock.Controller) *MockTransactionManager {
	mock := &MockTransactionManager{ctrl: ctrl}
	mock.recorder = &MockTransactionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionManager) EXPECT() *MockTransactionManagerMockRecorder {
	return m.recorder
}

// ExecuteInTransaction mocks base method.
func (m *MockTransactionManager) ExecuteInTransaction(ctx context.Context, ops []func(context.Context, *gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteInTransaction", ctx, ops)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteInTransaction indicates an expected call of ExecuteInTransaction.
func (mr *MockTransactionManagerMockRecorder) ExecuteInTransaction(ctx, ops any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteInTransaction", reflect.TypeOf((*MockTransactionManager)(nil).ExecuteInTransaction), ctx, ops)
}

// Tx mocks base method.
func (m *MockTransactionManager) Tx(ctx context.Context, op func(context.Context, *gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tx", ctx, op)
	ret0, _ := ret[0].(error)
	return ret0
}

// Tx indicates an expected call of Tx.
func (mr *MockTransactionManagerMockRecorder) Tx(ctx, op any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tx", reflect.TypeOf((*MockTransactionManager)(nil).Tx), ctx, op)
}
