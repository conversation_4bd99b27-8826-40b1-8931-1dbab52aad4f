load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["preferred_tip_config_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/preferred_tip_config/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/preferred_tip_config",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
