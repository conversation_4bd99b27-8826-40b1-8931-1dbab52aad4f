package customerproducer

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type API interface {
	SendCustomerCreateMessage(ctx context.Context, datum *CustomerCreateMessageDatum) error
}

type impl struct {
	customerProducer kafka.Client
}

var (
	initOnce         sync.Once
	customerProducer kafka.Client
)

func NewCustomerProducer() API {
	initOnce.Do(func() {
		customerProducer = kafka.NewClientProxy("moego.crm.customer.producer")
	})

	return &impl{customerProducer: customerProducer}
}

type CustomerCreateMessageDatum struct {
	CustomerID int64
	CompanyID  int64
	BusinessID int64
}

func (i *impl) SendCustomerCreateMessage(ctx context.Context, datum *CustomerCreateMessageDatum) error {
	// get key id
	id, err := uuid.NewV7()
	if err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage new uuid err, err:%+v", err)

		return err
	}

	// get event detail
	eventData := &eventbuspb.EventData{
		Tenant: &organizationpb.Tenant{
			CompanyId:  datum.CompanyID,
			BusinessId: &datum.BusinessID,
		},
		Event: &eventbuspb.EventData_CustomerCreatedEvent{CustomerCreatedEvent: &eventbuspb.
			CustomerCreatedEvent{Id: datum.CustomerID}},
	}
	detail, err := convertEventDataToAny(eventData)
	if err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage convertEventDataToAny err, err:%+v, eventData:%+v",
			err, eventData)

		return err
	}

	// get event value
	event := &eventbuspb.Event{
		Id: id.String(),
		Time: &timestamppb.Timestamp{
			Seconds: time.Now().Unix(),
		},
		Detail:    detail,
		EventType: eventbuspb.EventType_CUSTOMER_CREATED,
	}
	value, err := protojson.Marshal(event)
	if err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage Marshal event err, err:%+v", err)

		return err
	}

	// send message
	if err := i.customerProducer.Produce(ctx, []byte(id.String()), value); err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage SendMessage err, err:%+v, value:%s", err, value)

		return err
	}

	log.InfoContextf(ctx, "SendCustomerCreateMessage SendMessage success, value:%s", event)

	return nil
}

func convertEventDataToAny(eventData *eventbuspb.EventData) (*anypb.Any, error) {
	anyProto, err := anypb.New(eventData)
	if err != nil {
		return nil, err
	}

	return anyProto, nil
}
