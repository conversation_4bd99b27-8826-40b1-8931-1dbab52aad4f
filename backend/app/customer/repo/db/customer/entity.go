package customer

import (
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// BusinessCustomer 商家客户表结构
type BusinessCustomer struct {
	ID                     int64                           `gorm:"column:id;primaryKey;autoIncrement"`
	BusinessID             int                             `gorm:"column:business_id"`
	AvatarPath             string                          `gorm:"column:avatar_path"`
	Email                  *string                         `gorm:"column:email"`
	FirstName              string                          `gorm:"column:first_name"`
	LastName               string                          `gorm:"column:last_name"`
	Status                 int8                            `gorm:"column:status"`
	Inactive               int8                            `gorm:"column:inactive"`
	ClientColor            string                          `gorm:"column:client_color;default:'#000000"`
	IsBlockMessage         int8                            `gorm:"column:is_block_message"`
	IsBlockOnlineBooking   int8                            `gorm:"column:is_block_online_booking"`
	LoginEmail             string                          `gorm:"column:login_email"`
	ReferralSourceID       int                             `gorm:"column:referral_source_id"`
	ReferralSourceDesc     string                          `gorm:"column:referral_source_desc"`
	SendAutoEmail          int8                            `gorm:"column:send_auto_email"`
	SendAutoMessage        int8                            `gorm:"column:send_auto_message"`
	SendAppAutoMessage     int8                            `gorm:"column:send_app_auto_message"`
	UnconfirmedReminderBy  int8                            `gorm:"column:unconfirmed_reminder_by"`
	PreferredGroomerID     int                             `gorm:"column:preferred_groomer_id"`
	PreferredFrequencyDay  int                             `gorm:"column:preferred_frequency_day"`
	PreferredFrequencyType int8                            `gorm:"column:preferred_frequency_type"`
	LastServiceTime        string                          `gorm:"column:last_service_time"`
	Source                 string                          `gorm:"column:source"`
	ExternalID             string                          `gorm:"column:external_id"`
	CreateTime             int64                           `gorm:"column:create_time"`
	UpdateTime             int64                           `gorm:"column:update_time"`
	CreateBy               uint                            `gorm:"column:create_by"`
	UpdateBy               uint                            `gorm:"column:update_by"`
	IsRecurring            *int8                           `gorm:"column:is_recurring"`
	ShareApptStatus        int8                            `gorm:"column:share_appt_status"`
	ShareRangeType         int8                            `gorm:"column:share_range_type"`
	ShareRangeValue        int                             `gorm:"column:share_range_value"`
	ShareApptJSON          string                          `gorm:"column:share_appt_json"`
	PreferredDay           string                          `gorm:"column:preferred_day"`
	PreferredTime          string                          `gorm:"column:preferred_time"`
	AccountID              int64                           `gorm:"column:account_id"`
	CustomerCode           string                          `gorm:"column:customer_code;uniqueIndex:uk_customer_code"`
	IsUnsubscribed         int8                            `gorm:"column:is_unsubscribed"`
	CompanyID              int64                           `gorm:"column:company_id"`
	Birthday               *time.Time                      `gorm:"column:birthday"`
	Type                   customerpb.Customer_Type        `gorm:"column:type;serializer:proto_enum"`
	LifeCycle              customerpb.Customer_LifeCycle   `gorm:"column:life_cycle;serializer:proto_enum"`
	ActionState            customerpb.Customer_ActionState `gorm:"column:action_state;serializer:proto_enum"`
	AllocateStaffID        int64                           `gorm:"column:allocate_staff_id"`
	CustomizeLifeCycleID   *int64                          `gorm:"column:customize_life_cycle_id"`
	CustomizeActionStateID *int64                          `gorm:"column:customize_action_state_id"`
	// 新增聚合关系字段 - 不映射到数据库
	Addresses    []*Address    `gorm:"-"`
	Contacts     []*Contact    `gorm:"-"`
	CustomFields *CustomFields `gorm:"-"`
}

// TableName 设置表名
func (b *BusinessCustomer) TableName() string {
	return "moe_business_customer"
}

// Address 服务常用地址表结构
type Address struct {
	ID         int    `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerID int    `gorm:"column:customer_id"`
	BusinessID int    `gorm:"column:business_id"`
	Address1   string `gorm:"column:address1"`
	Address2   string `gorm:"column:address2"`
	City       string `gorm:"column:city"`
	State      string `gorm:"column:state"`
	Zipcode    string `gorm:"column:zipcode"`
	Country    string `gorm:"column:country"`
	Lat        string `gorm:"column:lat"`
	Lng        string `gorm:"column:lng"`
	Status     int8   `gorm:"column:status"`
	IsPrimary  *int8  `gorm:"column:is_primary"`
	CreateTime int64  `gorm:"column:create_time"`
	UpdateTime int64  `gorm:"column:update_time"`
	CompanyID  int64  `gorm:"column:company_id"`
}

// TableName 设置表名
func (m *Address) TableName() string {
	return "moe_customer_address"
}

type CustomFields struct {
	ID         int64            `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerID int64            `gorm:"column:customer_id"`
	CompanyID  int64            `gorm:"column:company_id"`
	Fields     *structpb.Struct `gorm:"column:fields;serializer:proto_json"`
}

func (m *CustomFields) TableName() string {
	return "moe_customer_custom_fields"
}

// CustomerContact 客户服务常用联系人表结构
type Contact struct {
	ID              int    `gorm:"column:id;primaryKey;autoIncrement"`
	BusinessID      int    `gorm:"column:business_id"`
	CustomerID      int    `gorm:"column:customer_id"`
	FirstName       string `gorm:"column:first_name"`
	LastName        string `gorm:"column:last_name"`
	PhoneNumber     string `gorm:"column:phone_number"`
	Email           string `gorm:"column:email"`
	Title           string `gorm:"column:title"`
	Type            int8   `gorm:"column:type"` // main 1, additional 2,
	IsPrimary       int8   `gorm:"column:is_primary"`
	Status          int8   `gorm:"column:status"`
	CreateTime      int64  `gorm:"column:create_time"`
	UpdateTime      int64  `gorm:"column:update_time"`
	CompanyID       int64  `gorm:"column:company_id"`
	E164PhoneNumber string `gorm:"column:e164_phone_number"`
}

// TableName 设置表名
func (m *Contact) TableName() string {
	return "moe_customer_contact"
}

// CustomerQuery 客户查询参数
type Query struct {
	CompanyID   int64
	CustomerIDs []int64
	PhoneNumber string
}

// CustomerFilter 客户过滤条件
type Filter struct {
	ActionState            *customerpb.Customer_ActionState
	Type                   *customerpb.Customer_Type
	Status                 *customerpb.Customer_State
	LifeCycle              *customerpb.Customer_LifeCycle
	CustomizeLifeCycleID   *int64
	CustomizeActionStateID *int64
	Emails                 []string
}

// GetCustomerParams 获取客户参数
type GetCustomerParams struct {
	ID           int64
	PhoneNumber  string
	CustomerCode string
}

// AddressQuery 地址查询参数
type AddressQuery struct {
	CustomerIDs []int64
}

func (p *AddressQuery) Validate() error {
	if len(p.CustomerIDs) == 0 {
		return status.Errorf(codes.InvalidArgument, "query must specify customer ids")
	}

	return nil
}

// AddressFilter 地址过滤条件
type AddressFilter struct {
	Status    *int8
	IsPrimary int8
}

// GetAddressParams 获取地址参数
type GetAddressParams struct {
	ID         int64
	CustomerID int64
	IsPrimary  bool
}

// ContactQuery 联系人查询参数
type ContactQuery struct {
	CustomerIDs []int64
}

func (p *ContactQuery) Validate() error {
	if len(p.CustomerIDs) == 0 {
		return status.Errorf(codes.InvalidArgument, "query must specify customer ids")
	}

	return nil
}

// ContactFilter 联系人过滤条件
type ContactFilter struct {
	Status    customerpb.CustomerContact_State
	IsPrimary *int8
	Type      customerpb.CustomerContact_Type
}

// GetContactParams 获取联系人参数
type GetContactParams struct {
	ID          int64
	CustomerID  int64
	CompanyID   int64
	PhoneNumber string
}
