load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_life_cycle",
    srcs = [
        "customer_life_cycle.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_life_cycle",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
