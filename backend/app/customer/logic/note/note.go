package note

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/metadata"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/note"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 逻辑层
type Logic struct {
	noteRepo         note.ReadWriter
	activityLogLogic *activitylog.Logic
	metadataLogic    *metadata.Logic
	contactLogic     *contact.Logic
}

// New 构造函数
func New() *Logic {
	return &Logic{
		noteRepo:         note.New(),
		activityLogLogic: activitylog.New(),
		metadataLogic:    metadata.New(),
		contactLogic:     contact.New(),
	}
}

// Create 创建 Note
func (l *Logic) Create(ctx context.Context, datum *CreateNoteDatum) (*customerpb.Note, error) {
	// check
	if datum == nil || datum.CustomerID <= 0 ||
		datum.Note == "" || datum.CreateSource == nil {
		log.ErrorContextf(ctx, "Create Note params is invalid: %+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	dbNote := &note.Note{
		CompanyID:    datum.CompanyID,
		BusinessID:   datum.BusinessID,
		CustomerID:   datum.CustomerID,
		Note:         datum.Note,
		CreateSource: datum.CreateSource,
		UpdateSource: datum.CreateSource,
		CreateBy:     datum.CreateSource.SourceId,
		UpdateBy:     datum.CreateSource.SourceId,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}

	// db insert
	if err := l.noteRepo.Create(ctx, dbNote); err != nil {
		return nil, err
	}

	// save log
	go func() {
		l.saveNoteLog(context.Background(), datum)
	}()

	return convNotePB(dbNote), nil
}

// Update 更新 Note
func (l *Logic) Update(ctx context.Context, datum *UpdateNoteDatum) (*customerpb.Note, error) {
	// 参数校验
	if datum == nil || datum.NoteID <= 0 || datum.UpdateSource == nil || datum.Note == nil {
		log.ErrorContextf(ctx, "Update Note params is invalid: %+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	dbNote := &note.Note{
		ID:           datum.NoteID,
		UpdateBy:     datum.UpdateSource.SourceId,
		UpdateTime:   time.Now(),
		UpdateSource: datum.UpdateSource,
	}
	if datum.Note != nil {
		if *datum.Note == "" {
			log.ErrorContextf(ctx, "Update Note content is empty")

			return nil, status.Error(codes.InvalidArgument, "note is empty")
		}
		dbNote.Note = *datum.Note
	}

	// update
	if err := l.noteRepo.Update(ctx, dbNote); err != nil {
		return nil, err
	}

	return convNotePB(dbNote), nil
}

// List 查询 Note 列表
func (l *Logic) List(ctx context.Context, datum *ListNotesDatum) ([]*customerpb.Note, error) {
	// 参数校验
	if datum == nil || (len(datum.CustomerIDs) == 0 && len(datum.IDs) == 0) {
		log.ErrorContextf(ctx, "List Note params is invalid: %+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}
	notes, err := l.noteRepo.List(ctx, &note.ListNotesDatum{CustomerIDs: datum.CustomerIDs, IDs: datum.IDs})
	if err != nil {
		return nil, err
	}

	return convNotesPB(notes), nil
}

// Delete 删除 Note
func (l *Logic) Delete(ctx context.Context, datum *DeleteNoteDatum) error {
	// 参数校验
	if datum == nil || datum.NoteID <= 0 || datum.StaffID <= 0 {
		log.ErrorContextf(ctx, "Delete Note params is invalid: %+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}
	if err := l.noteRepo.Delete(ctx, datum.NoteID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

func (l *Logic) saveNoteLog(ctx context.Context, datum *CreateNoteDatum) {
	// load meta
	metadata, err := l.metadataLogic.GetMetadata(ctx, datum.CustomerID)
	if err != nil {
		log.ErrorContextf(ctx, "saveNoteLog GetMetadata err, customerID:%d, err:%v", datum.CustomerID, err.Error())
	}

	// load contact
	contactsResp, err := l.contactLogic.List(ctx, &contact.ListContactsRequest{
		Filter: &contact.ListContactsFilter{
			CustomerIDs: []int64{datum.CustomerID},
		},
		Pagination: &contact.ListContactsPagination{
			PageSize:        0,
			ReturnTotalSize: false,
		},
		OrderBy: &contact.ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_ID,
			Direction: customerpb.Direction_ASC,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "saveNoteLog List Contacts err, customerID:%d, err:%v", datum.CustomerID, err.Error())
	}

	// save log
	_, err = l.activityLogLogic.Create(ctx, &activitylog.CreateActivityLogDatum{
		CustomerID:          datum.CustomerID,
		CustomerName:        getMetadataName(metadata),
		CustomerPhoneNumber: getContactFirstPhoneNumber(contactsResp.GetContacts()),
		BusinessID:          datum.BusinessID,
		CompanyID:           datum.CompanyID,
		StaffID:             datum.CreateSource.SourceId,
		Action: &customerpb.ActivityLog_Action{
			Action: &customerpb.ActivityLog_Action_Note{
				Note: &customerpb.ActivityLog_Note{Text: datum.Note},
			},
		},
		Source: datum.CreateSource,
	})
	if err != nil {
		log.ErrorContextf(ctx, "saveNoteLog create history log error, customerID:%d, err: %v",
			datum.CustomerID, err.Error())
	}
}

func getMetadataName(metadata *metadata.Metadata) string {
	if metadata == nil {
		return ""
	}

	return customerutils.ConvCustomerName(metadata.GivenName, metadata.FamilyName)
}

// convNotesPB repo Note -> pb Note
func convNotesPB(notes []*note.Note) []*customerpb.Note {
	res := make([]*customerpb.Note, 0, len(notes))
	for _, n := range notes {
		if n == nil {
			continue
		}
		res = append(res, convNotePB(n))
	}

	return res
}

func convNotePB(n *note.Note) *customerpb.Note {
	return &customerpb.Note{
		Id:           n.ID,
		Text:         n.Note,
		CreateSource: n.CreateSource,
		UpdateSource: n.UpdateSource,
		CreateTime:   timestamppb.New(n.CreateTime),
		UpdateTime:   timestamppb.New(n.UpdateTime),
		CustomerId:   n.CustomerID,
	}
}

func getContactFirstPhoneNumber(contacts []*contact.Contact) string {
	if len(contacts) == 0 || contacts[0] == nil {
		return ""
	}

	return contacts[0].Phone
}
