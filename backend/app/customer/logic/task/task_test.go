package task

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	ggorm "gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customertask"
	mock_customertask "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customertask/mock"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

func TestLogic_New(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db.SetDB(&ggorm.DB{})
		logic := New()
		assert.NotNil(t, logic)
	})
}

func TestLogic_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customertask.NewMockReadWriter(ctrl)
	logic := &Logic{taskRepo: mockRepo}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, task *customertask.CustomerTask) error {
			require.Equal(t, int64(1), task.CompanyID)
			require.Equal(t, int64(1), task.BusinessID)
			require.Equal(t, int64(1), task.CustomerID)
			require.Equal(t, int64(1), task.CreateBy)
			require.Equal(t, "Test Task", task.Name)
			require.Equal(t, customerpb.Task_NEW, task.State)
			require.NotNil(t, task.CompleteTime)
			require.Equal(t, int64(12), *task.AllocateStaffID)
			task.ID = 12345
			return nil
		})
		id, err := logic.Create(ctx, &CreateTaskDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Name:       "Test Task",
			State:      customerpb.Task_NEW,
			CompleteTime: &timestamppb.Timestamp{
				Seconds: 123,
			},
			AllocateStaffID: customerutils.ToPointer(int64(12)),
		})
		assert.NoError(t, err)
		assert.Equal(t, int64(12345), id)
	})

	t.Run("invalid params", func(t *testing.T) {
		_, err := logic.Create(ctx, &CreateTaskDatum{
			CompanyID: 0,
			Name:      "",
			State:     customerpb.Task_STATE_UNSPECIFIED,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params", func(t *testing.T) {
		_, err := logic.Create(ctx, &CreateTaskDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Name:       "",
			State:      customerpb.Task_STATE_UNSPECIFIED,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("db error", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, task *customertask.CustomerTask) error {
			require.Equal(t, int64(1), task.CompanyID)
			require.Equal(t, int64(1), task.BusinessID)
			require.Equal(t, int64(1), task.CustomerID)
			require.Equal(t, int64(1), task.CreateBy)
			require.Equal(t, "Test Task", task.Name)
			require.Equal(t, customerpb.Task_NEW, task.State)
			return fmt.Errorf("db error")
		})
		_, err := logic.Create(ctx, &CreateTaskDatum{
			CompanyID:  1,
			BusinessID: 1,
			CustomerID: 1,
			StaffID:    1,
			Name:       "Test Task",
			State:      customerpb.Task_NEW,
			CompleteTime: &timestamppb.Timestamp{
				Seconds: 123,
			},
			AllocateStaffID: customerutils.ToPointer(int64(12)),
		})
		assert.Error(t, err)
	})
}

func TestLogic_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	t.Run("success", func(t *testing.T) {
		mockRepo := mock_customertask.NewMockReadWriter(ctrl)
		logic := &Logic{taskRepo: mockRepo}
		mockRepo.EXPECT().Update(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, task *customertask.CustomerTask) error {
			require.Equal(t, int64(1), task.ID)
			require.Equal(t, int64(1), task.UpdateBy)
			require.Equal(t, "Updated Task", task.Name)
			require.Equal(t, customerpb.Task_NEW, task.State)
			require.NotNil(t, task.CompleteTime)
			require.Equal(t, int64(12), *task.AllocateStaffID)
			return nil
		})
		err := logic.Update(ctx, &UpdateTaskDatum{
			TaskID:  1,
			StaffID: 1,
			Name:    customerutils.ToPointer("Updated Task"),
			State:   customerpb.Task_NEW.Enum(),
			CompleteTime: &timestamppb.Timestamp{
				Seconds: 123,
			},
			AllocateStaffID: customerutils.ToPointer(int64(12)),
		})
		assert.NoError(t, err)
	})

	t.Run("invalid params", func(t *testing.T) {
		mockRepo := mock_customertask.NewMockReadWriter(ctrl)
		logic := &Logic{taskRepo: mockRepo}
		err := logic.Update(ctx, &UpdateTaskDatum{
			TaskID: 0,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("update name empty err", func(t *testing.T) {
		mockRepo := mock_customertask.NewMockReadWriter(ctrl)
		logic := &Logic{taskRepo: mockRepo}
		err := logic.Update(ctx, &UpdateTaskDatum{
			TaskID:  1,
			StaffID: 1,
			Name:    customerutils.ToPointer(""),
		})
		assert.Error(t, err)
	})

	t.Run("update state err", func(t *testing.T) {
		mockRepo := mock_customertask.NewMockReadWriter(ctrl)
		logic := &Logic{taskRepo: mockRepo}
		err := logic.Update(ctx, &UpdateTaskDatum{
			TaskID:  1,
			StaffID: 1,
			State:   customerpb.Task_STATE_UNSPECIFIED.Enum(),
		})
		assert.Error(t, err)
	})

	t.Run("db error", func(t *testing.T) {
		mockRepo := mock_customertask.NewMockReadWriter(ctrl)
		logic := &Logic{taskRepo: mockRepo}
		mockRepo.EXPECT().Update(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, task *customertask.CustomerTask) error {
			require.Equal(t, int64(1), task.ID)
			require.Equal(t, int64(1), task.UpdateBy)
			require.Equal(t, "Updated Task", task.Name)
			return fmt.Errorf("db error")
		})
		err := logic.Update(ctx, &UpdateTaskDatum{
			TaskID:  1,
			StaffID: 1,
			Name:    customerutils.ToPointer("Updated Task"),
		})
		assert.Error(t, err)
	})
}

func TestLogic_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customertask.NewMockReadWriter(ctrl)
	logic := &Logic{taskRepo: mockRepo}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().List(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, datum *customertask.ListTasksDatum) ([]*customertask.CustomerTask, error) {
			require.Equal(t, int64(1), datum.CustomerID)
			return []*customertask.CustomerTask{
				{
					ID:              1,
					Name:            "Sample Task",
					State:           customerpb.Task_NEW,
					AllocateStaffID: customerutils.ToPointer(int64(1)),
					CompleteTime:    customerutils.ToPointer(time.Unix(123, 0)),
				},
				nil,
			}, nil
		})
		tasks, err := logic.List(ctx, &ListTasksDatum{
			CustomerID: 1,
		})
		assert.NoError(t, err)
		assert.NotEmpty(t, tasks)
	})

	t.Run("invalid params", func(t *testing.T) {
		_, err := logic.List(ctx, &ListTasksDatum{
			CustomerID: 0,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("db error", func(t *testing.T) {
		mockRepo.EXPECT().List(ctx, gomock.Any()).DoAndReturn(func(ctx context.Context, datum *customertask.ListTasksDatum) ([]*customertask.CustomerTask, error) {
			require.Equal(t, int64(1), datum.CustomerID)
			return nil, fmt.Errorf("db error")
		})
		_, err := logic.List(ctx, &ListTasksDatum{
			CustomerID: 1,
		})
		assert.Error(t, err)
	})
}

func TestLogic_Delete(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	mockRepo := mock_customertask.NewMockReadWriter(ctrl)
	logic := &Logic{taskRepo: mockRepo}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().Delete(ctx, int64(1), int64(1)).DoAndReturn(func(ctx context.Context, taskID, staffID int64) error {
			require.Equal(t, int64(1), taskID)
			require.Equal(t, int64(1), staffID)
			return nil
		})
		err := logic.Delete(ctx, &DeleteTasksDatum{
			TaskID:  1,
			StaffID: 1,
		})
		assert.NoError(t, err)
	})

	t.Run("invalid params", func(t *testing.T) {
		err := logic.Delete(ctx, &DeleteTasksDatum{
			TaskID: 0,
		})
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("db error", func(t *testing.T) {
		mockRepo.EXPECT().Delete(ctx, int64(1), int64(1)).DoAndReturn(func(ctx context.Context, taskID, staffID int64) error {
			require.Equal(t, int64(1), taskID)
			require.Equal(t, int64(1), staffID)
			return fmt.Errorf("db error")
		})
		err := logic.Delete(ctx, &DeleteTasksDatum{
			TaskID:  1,
			StaffID: 1,
		})
		assert.Error(t, err)
	})
}
