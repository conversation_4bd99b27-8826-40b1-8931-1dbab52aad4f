package contact

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_relation"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

var replaceableTagTypes = map[customerpb.ContactTag_Type]bool{
	customerpb.ContactTag_EMERGENCY:     true,
	customerpb.ContactTag_COMMUNICATION: true,
}

var exclusiveTagTypes = map[customerpb.ContactTag_Type]bool{
	customerpb.ContactTag_EMERGENCY:     true,
	customerpb.ContactTag_COMMUNICATION: true,
	customerpb.ContactTag_PRIMARY:       true,
}

type Logic struct {
	contactRepo       contactrepo.Repository
	contactTagRepo    contacttagrepo.Repository
	contactTagRelRepo contacttagrel.Repository
}

func New() *Logic {
	return &Logic{
		contactRepo:       contactrepo.New(),
		contactTagRepo:    contacttagrepo.New(),
		contactTagRelRepo: contacttagrel.New(),
	}
}

func NewByParams(
	contactRepo contactrepo.Repository,
	contactTagRepo contacttagrepo.Repository,
	contactTagRelRepo contacttagrel.Repository,
) *Logic {
	return &Logic{
		contactRepo:       contactRepo,
		contactTagRepo:    contactTagRepo,
		contactTagRelRepo: contactTagRelRepo,
	}
}

func (l *Logic) WithTx(tx *gorm.DB) *Logic {
	return &Logic{
		contactRepo:       l.contactRepo.WithTx(tx),
		contactTagRepo:    l.contactTagRepo.WithTx(tx),
		contactTagRelRepo: l.contactTagRelRepo.WithTx(tx),
	}
}

func (l *Logic) Create(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()

	dbTags, err := l.findContactTags(ctx, contact.Tags)
	if err != nil {
		return nil, err
	}
	tagTypes := make([]customerpb.ContactTag_Type, 0, len(dbTags))
	for _, tag := range dbTags {
		tagTypes = append(tagTypes, tag.Type)
	}
	if err := l.checkAndRemoveConflictingTags(ctx, contact.CustomerID, contact.OrganizationType,
		contact.OrganizationID, 0, tagTypes); err != nil {
		return nil, err
	}

	// 使用找到的标签替换原始标签，避免创建重复标签
	dbContact.Tags = make([]*contacttagrepo.ContactTag, 0, len(dbTags))
	for _, tag := range dbTags {
		dbContact.Tags = append(dbContact.Tags, tag.ToDB())
	}

	resContact, err := l.contactRepo.Create(ctx, dbContact)
	if err != nil {
		return nil, err
	}

	// add tag and rel
	for _, tag := range dbContact.Tags {
		tagID := tag.ID
		if tagID == 0 {
			// add tag
			resTag, err := l.contactTagRepo.Create(ctx, tag)
			if err != nil {
				return nil, err
			}
			tagID = resTag.ID
		}

		// add tag rel contact
		rel := &contacttagrel.ContactTagRelation{
			OrganizationType: resContact.OrganizationType,
			OrganizationID:   resContact.OrganizationID,
			ContactID:        resContact.ID,
			TagID:            tagID,
		}
		if _, err := l.contactTagRelRepo.Create(ctx, rel); err != nil {
			return nil, err
		}
	}

	// convert to res
	resultContact := convertToContact(resContact)
	tags, err := l.associateTags(ctx, resultContact)
	if err != nil {
		return nil, err
	}
	resultContact.Tags = tags

	return resultContact, nil
}

func (l *Logic) findContactTags(ctx context.Context, templates []*contacttag.ContactTag) (
	[]*contacttag.ContactTag, error) {
	if len(templates) == 0 {
		return nil, nil
	}
	tagIDs := make([]int64, 0, len(templates))
	existedTagIDs := make(map[int64]struct{})
	builtInTypes := make([]customerpb.ContactTag_Type, 0, len(templates))
	for _, t := range templates {
		if t.ID != 0 {
			tagIDs = append(tagIDs, t.ID)
			existedTagIDs[t.ID] = struct{}{}

			continue
		}
		if t.Type != customerpb.ContactTag_TYPE_UNSPECIFIED && t.Type != customerpb.ContactTag_CUSTOM {
			builtInTypes = append(builtInTypes, t.Type)
		}
	}
	result := make([]*contacttag.ContactTag, 0, len(templates))
	if len(builtInTypes) > 0 {
		tagsByType, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
			Types:         builtInTypes,
			Organizations: []*customerpb.OrganizationRef{{Type: customerpb.OrganizationRef_SYSTEM, Id: 0}},
		}, &contacttagrepo.Pagination{
			PageSize: int32(len(templates)),
		}, nil)
		if err != nil {
			return nil, err
		}
		for _, tag := range tagsByType.Data {
			if _, ok := existedTagIDs[tag.ID]; !ok {
				tagIDs = append(tagIDs, tag.ID)
			}
		}
	}
	if len(tagIDs) > 0 {
		tagsByID, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
			IDs: tagIDs,
		}, &contacttagrepo.Pagination{
			PageSize: int32(len(templates)),
		}, nil)
		if err != nil {
			return nil, err
		}
		for _, tag := range tagsByID.Data {
			result = append(result, (&contacttag.ContactTag{}).Load(tag))
		}
	}
	if len(result) == 0 {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND)
	}

	return result, nil
}

// CreateWithoutTX 非事务方法，需要上层保证事务
func (l *Logic) CreateWithoutTX(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	// add contact
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()

	// 查找现有标签，避免创建重复标签
	if len(contact.Tags) > 0 {
		dbTags, err := l.findContactTags(ctx, contact.Tags)
		if err != nil {
			return nil, err
		}
		// 使用找到的标签替换原始标签，避免创建重复标签
		dbContact.Tags = make([]*contacttagrepo.ContactTag, 0, len(dbTags))
		for _, tag := range dbTags {
			dbContact.Tags = append(dbContact.Tags, tag.ToDB())
		}
	}

	resContact, err := l.contactRepo.Create(ctx, dbContact)
	if err != nil {
		return nil, err
	}

	// add tag and rel
	for _, tag := range dbContact.Tags {
		tagID := tag.ID
		if tagID == 0 {
			// add tag
			resTag, err := l.contactTagRepo.Create(ctx, tag)
			if err != nil {
				return nil, err
			}
			tagID = resTag.ID
		}

		// add tag rel contact
		rel := &contacttagrel.ContactTagRelation{
			OrganizationType: resContact.OrganizationType,
			OrganizationID:   resContact.OrganizationID,
			ContactID:        resContact.ID,
			TagID:            tagID,
		}
		if _, err := l.contactTagRelRepo.Create(ctx, rel); err != nil {
			return nil, err
		}
	}

	// convert to res
	resultContact := convertToContact(resContact)
	tags, err := l.associateTags(ctx, resultContact)
	if err != nil {
		return nil, err
	}
	resultContact.Tags = tags

	return resultContact, nil
}

func (l *Logic) SaveContactForCDC(ctx context.Context, contact *Contact) error {
	// conv contact db
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()

	// conv tag db
	if len(contact.Tags) > 0 {
		dbTags, err := l.findContactTags(ctx, contact.Tags)
		if err != nil {
			return err
		}
		// 使用找到的标签替换原始标签，避免创建重复标签
		dbContact.Tags = make([]*contacttagrepo.ContactTag, 0, len(dbTags))
		for _, tag := range dbTags {
			dbContact.Tags = append(dbContact.Tags, tag.ToDB())
		}
	}

	// save contact
	resContact, err := l.contactRepo.Save(ctx, dbContact)
	if err != nil {
		return err
	}

	// save tag rel
	for _, tag := range dbContact.Tags {
		if tag.ID == 0 {
			continue
		}
		if _, err := l.contactTagRelRepo.Save(ctx, &contacttagrel.ContactTagRelation{
			OrganizationType: resContact.OrganizationType,
			OrganizationID:   resContact.OrganizationID,
			ContactID:        resContact.ID,
			TagID:            tag.ID,
		}); err != nil {
			return err
		}
	}

	return nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*Contact, error) {
	dbContact, err := l.contactRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_NOT_FOUND)
		}

		return nil, err
	}
	contact := convertToContact(dbContact)
	tags, err := l.associateTags(ctx, contact)
	if err != nil {
		return nil, err
	}
	contact.Tags = tags

	return contact, nil
}

func (l *Logic) List(ctx context.Context, req *ListContactsRequest) (*ListContactsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}

	// 使用分页工具类处理page token
	paginationHelper := customerutils.NewPaginationHelper()
	pageToken := ""
	if req.Pagination != nil {
		pageToken = req.Pagination.Cursor
	}
	paginationInfo, err := paginationHelper.ProcessPageToken(pageToken, req.Pagination.PageSize)
	if err != nil {
		return nil, errs.Newm(codes.InvalidArgument, fmt.Sprintf("invalid page token: %v", err))
	}

	// 构建分页参数
	pagination := &contactrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		ReturnTotalSize: paginationInfo.ShouldReturnTotalSize(req.Pagination.ReturnTotalSize),
	}

	if paginationInfo.IsPageNumberMode() {
		// 使用offset分页
		pagination.Offset = &paginationInfo.Offset
	} else {
		// 使用cursor分页
		pagination.Cursor = paginationInfo.Cursor
	}

	dbContacts, err := l.contactRepo.ListByCursor(ctx, &contactrepo.ListFilter{
		IDs:             req.Filter.IDs,
		CustomerIDs:     req.Filter.CustomerIDs,
		States:          req.Filter.States,
		Phones:          req.Filter.Phones,
		Emails:          req.Filter.Emails,
		OrganizationRef: req.Filter.OrganizationRef,
		TagTypes:        req.Filter.TagTypes,
	}, pagination, &contactrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}
	contacts := convertToContacts(dbContacts.Data)
	// 组装tag 到contact
	for _, c := range contacts {
		tags, err := l.associateTags(ctx, c)
		if err != nil {
			return nil, err
		}
		c.Tags = tags
	}

	result := &ListContactsResponse{
		Contacts: contacts,
		HasNext:  dbContacts.HasNext,
	}
	if dbContacts.TotalCount != nil {
		result.TotalSize = dbContacts.TotalCount
	}

	// 生成下一页token
	if dbContacts.HasNext && len(dbContacts.Data) > 0 {
		var nextCursor *postgres.Cursor
		if paginationInfo.IsCursorMode() {
			// cursor模式，生成cursor
			lastContact := dbContacts.Data[len(dbContacts.Data)-1]
			nextCursor = &postgres.Cursor{
				ID:        lastContact.ID,
				CreatedAt: lastContact.CreatedTime,
			}
		}
		result.NextToken = paginationHelper.GenerateNextToken(paginationInfo, dbContacts.HasNext, nextCursor)
	}

	return result, nil
}

func (l *Logic) Update(ctx context.Context, updateRef *UpdateContactRequest, isInTx bool) (*Contact, error) {
	dbContact, err := l.Get(ctx, updateRef.ID)
	if err != nil {
		return nil, err
	}
	dbContact.Email = updateRef.Email
	dbContact.Phone = updateRef.Phone
	dbContact.GivenName = updateRef.GivenName
	dbContact.FamilyName = updateRef.FamilyName
	dbContact.Note = updateRef.Note
	dbContact.State = updateRef.State
	dbContact.Title = updateRef.Title
	if !updateRef.UpdateTags {
		updatedContact, err := l.contactRepo.Update(ctx, dbContact.ToDB(), isInTx)
		if err != nil {
			return nil, err
		}

		return convertToContact(updatedContact), nil
	}

	dbTags, err := l.findContactTags(ctx, updateRef.Tags)
	if err != nil {
		return nil, err
	}
	tagTypes := make([]customerpb.ContactTag_Type, 0, len(dbTags))
	for _, tag := range dbTags {
		tagTypes = append(tagTypes, tag.Type)
	}

	if err := l.checkAndRemoveConflictingTags(ctx, dbContact.CustomerID, dbContact.OrganizationType,
		dbContact.OrganizationID, updateRef.ID, tagTypes); err != nil {
		return nil, err
	}

	dbContact.Tags = dbTags

	updatedContact, err := l.contactRepo.Update(ctx, dbContact.ToDB(), isInTx)
	if err != nil {
		return nil, err
	}
	result := convertToContact(updatedContact)
	result.Tags, err = l.associateTags(ctx, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	_, err := l.Get(ctx, id)
	if err != nil {
		return err
	}
	err = l.contactRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func convertToContacts(dbContacts []*contactrepo.Contact) []*Contact {
	contacts := make([]*Contact, 0, len(dbContacts))
	for _, dbContact := range dbContacts {
		contacts = append(contacts, convertToContact(dbContact))
	}

	return contacts
}

func convertToContact(dbContact *contactrepo.Contact) *Contact {
	return &Contact{
		ID:               dbContact.ID,
		CustomerID:       dbContact.CustomerID,
		OrganizationType: dbContact.OrganizationType,
		OrganizationID:   dbContact.OrganizationID,
		GivenName:        dbContact.GivenName,
		FamilyName:       dbContact.FamilyName,
		Email:            dbContact.Email,
		Phone:            dbContact.Phone,
		IsSelf:           dbContact.IsSelf,
		State:            dbContact.State,
		Note:             dbContact.Note,
		Title:            dbContact.Title,
		DeletedTime:      dbContact.DeletedTime,
		CreatedTime:      dbContact.CreatedTime,
		UpdatedTime:      dbContact.UpdatedTime,
	}
}

func (l *Logic) associateTags(ctx context.Context, contact *Contact) ([]*contacttag.ContactTag, error) {
	contactID := contact.ID
	tagRels, err := l.contactTagRelRepo.ListWithLock(ctx, &contacttagrel.ListFilter{
		ContactID: contactID,
	})
	if err != nil {
		return nil, err
	}
	tagIDs := make([]int64, 0, len(tagRels))
	for _, tagRel := range tagRels {
		tagIDs = append(tagIDs, tagRel.TagID)
	}
	dbTags, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
		IDs: tagIDs,
	}, &contacttagrepo.Pagination{
		PageSize: int32(len(tagRels)),
	}, &contacttagrepo.OrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_ASC,
	})
	if err != nil {
		return nil, err
	}

	tags := make([]*contacttag.ContactTag, 0, len(dbTags.Data))
	for _, tag := range dbTags.Data {
		t := &contacttag.ContactTag{}
		tags = append(tags, t.Load(tag))
	}

	return tags, nil
}

func (l *Logic) checkAndRemoveConflictingTags(ctx context.Context, customerID int64,
	orgType customerpb.OrganizationRef_Type, orgID int64, excludeContactID int64,
	tagTypes []customerpb.ContactTag_Type) error {
	exclusiveTypes := make([]customerpb.ContactTag_Type, 0)
	primaryTypes := make([]customerpb.ContactTag_Type, 0)
	replaceableTypes := make([]customerpb.ContactTag_Type, 0)

	for _, tagType := range tagTypes {
		if exclusiveTagTypes[tagType] {
			exclusiveTypes = append(exclusiveTypes, tagType)
			if tagType == customerpb.ContactTag_PRIMARY {
				primaryTypes = append(primaryTypes, tagType)
			} else if replaceableTagTypes[tagType] {
				replaceableTypes = append(replaceableTypes, tagType)
			}
		}
	}

	if len(exclusiveTypes) == 0 {
		return nil // 没有独占标签，无需检查冲突
	}

	contacts, err := l.contactRepo.ListByCursor(ctx, &contactrepo.ListFilter{
		CustomerIDs: []int64{customerID},
		States:      []customerpb.Contact_State{customerpb.Contact_ACTIVE},
		OrganizationRef: &customerpb.OrganizationRef{
			Type: orgType,
			Id:   orgID,
		},
	}, &contactrepo.Pagination{
		PageSize: 1000, // 假设客户不会有超过1000个联系人
	}, &contactrepo.OrderBy{
		Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_ASC,
	})
	if err != nil {
		return err
	}

	if len(primaryTypes) > 0 {
		for _, contact := range contacts.Data {
			if contact.ID == excludeContactID {
				continue // 跳过当前联系人
			}

			tagRels, err := l.contactTagRelRepo.List(ctx, &contacttagrel.ListFilter{
				ContactID: contact.ID,
			})
			if err != nil {
				return err
			}

			for _, tagRel := range tagRels {
				tag, err := l.contactTagRepo.Get(ctx, tagRel.TagID)
				if err != nil {
					continue // 忽略获取失败的标签
				}

				if tag.Type == customerpb.ContactTag_PRIMARY {
					return errs.New(customerpb.ErrCode_ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS)
				}
			}
		}
	}

	if len(replaceableTypes) > 0 {
		return l.replaceExclusiveTagsWithTransaction(ctx, contacts.Data, excludeContactID, replaceableTypes)
	}

	return nil
}

func (l *Logic) replaceExclusiveTagsWithTransaction(ctx context.Context, contacts []*contactrepo.Contact,
	excludeContactID int64, replaceableTypes []customerpb.ContactTag_Type) error {

	for _, contact := range contacts {
		if contact.ID == excludeContactID {
			continue // 跳过当前联系人
		}

		if err := l.replaceContactTagsWithLocking(ctx, contact.ID, replaceableTypes); err != nil {
			return err
		}
	}

	return nil
}

func (l *Logic) replaceContactTagsWithLocking(
	ctx context.Context, contactID int64, replaceableTypes []customerpb.ContactTag_Type,
) error {
	tagRels, err := l.contactTagRelRepo.ListWithLock(ctx, &contacttagrel.ListFilter{
		ContactID: contactID,
	})
	if err != nil {
		return err
	}

	for _, tagRel := range tagRels {
		tag, err := l.contactTagRepo.Get(ctx, tagRel.TagID)
		if err != nil {
			continue // 忽略获取失败的标签
		}

		for _, replaceableType := range replaceableTypes {
			if tag.Type == replaceableType {
				if err := l.contactTagRelRepo.Delete(ctx, tagRel.ContactID, tagRel.TagID); err != nil {
					return err
				}

				break
			}
		}
	}

	return nil
}
