load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_related_data",
    srcs = [
        "customer_related_data.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/customer_related_data",
        "//backend/app/customer/repo/postgres/preferred_tip_config",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
