package customerrelateddata

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	preferredtipconfigrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/preferred_tip_config"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// ApptReminderBy 常量定义 - 对应 Java 中的位运算常量和枚举值
const (
	// 位运算常量 - 用于数据库存储
	ByApp   byte = 0x88 // 1000 1000
	ByCall  byte = 0x84 // 1000 0100
	ByEmail byte = 0x82 // 1000 0010
	ByMsg   byte = 0x81 // 1000 0001

	// 枚举值常量 - 对应 Java 中的 ApptReminderBy 枚举
	ApptReminderByMsg   byte = 1
	ApptReminderByEmail byte = 2
	ApptReminderByCall  byte = 3
	ApptReminderByApp   byte = 4
)

type Logic struct {
	customerRelatedDataRepo customerrelateddatarepo.Repository
	preferredTipConfigRepo  preferredtipconfigrepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRelatedDataRepo: customerrelateddatarepo.New(),
		preferredTipConfigRepo:  preferredtipconfigrepo.New(),
	}
}

func NewByParams(
	customerRelatedDataRepo customerrelateddatarepo.Repository,
	preferredTipConfigRepo preferredtipconfigrepo.Repository,
) *Logic {
	return &Logic{
		customerRelatedDataRepo: customerRelatedDataRepo,
		preferredTipConfigRepo:  preferredTipConfigRepo,
	}
}

func (l *Logic) WithTx(tx *gorm.DB) *Logic {
	return &Logic{
		customerRelatedDataRepo: l.customerRelatedDataRepo.WithTx(tx),
		preferredTipConfigRepo:  l.preferredTipConfigRepo.WithTx(tx),
	}
}

func (l *Logic) Create(
	ctx context.Context, data *CustomerRelatedData,
) (*CustomerRelatedData, error) {
	// fill customer default values
	l.fillDefaultValues(data)

	// create customer related data, set output only columns
	now := time.Now().UTC()
	data.CreatedTime = now
	data.UpdatedTime = now
	data.State = customerpb.CustomerRelatedData_ACTIVE

	dbData := data.ToDB()

	dbData, err := l.customerRelatedDataRepo.Create(ctx, dbData)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED)
	}

	result := convertToCustomerRelatedData(dbData)

	if data.PreferredTipEnable != 0 || data.PreferredTipType != 0 ||
		data.PreferredTipAmount != 0 || data.PreferredTipPercentage != 0 {
		tipConfig := &preferredtipconfigrepo.PreferredTipConfig{
			CustomerID: data.CustomerID,
			BusinessID: data.PreferredBusinessID,
			Enable:     data.PreferredTipEnable,
			TipType:    data.PreferredTipType,
			Amount:     data.PreferredTipAmount,
			Percentage: data.PreferredTipPercentage,
			CompanyID:  data.CompanyID,
		}

		_, err = l.preferredTipConfigRepo.Create(ctx, tipConfig)
		if err != nil {
			return nil, err
		}

		result.PreferredTipEnable = data.PreferredTipEnable
		result.PreferredTipType = data.PreferredTipType
		result.PreferredTipAmount = data.PreferredTipAmount
		result.PreferredTipPercentage = data.PreferredTipPercentage
	}

	return result, nil
}

// fillDefaultValues 填充客户相关数据的默认值
func (l *Logic) fillDefaultValues(data *CustomerRelatedData) {
	if data.ClientColor == "" {
		data.ClientColor = DefaultClientColor
	}
	if data.SendAutoMessage == 0 {
		data.SendAutoMessage = 1
	}
	if data.SendAppAutoMessage == 0 {
		data.SendAppAutoMessage = 1
	}
	if len(data.UnconfirmedReminderBy) == 0 {
		data.UnconfirmedReminderBy = []int32{1}
	}
	if data.PreferredFrequencyDay == 0 {
		data.PreferredFrequencyDay = 28
	}
	if data.PreferredFrequencyType == 0 {
		data.PreferredFrequencyType = 1
	}
	if data.PreferredDay == "" {
		data.PreferredDay = DefaultPreferredDay
	}
	if data.PreferredTime == "" {
		data.PreferredTime = DefaultPreferredTime
	}
}

func (l *Logic) Get(ctx context.Context, customerID int64) (*CustomerRelatedData, error) {
	dbData, err := l.customerRelatedDataRepo.GetByCustomerID(ctx, customerID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND)
		}

		return nil, err
	}

	result := convertToCustomerRelatedData(dbData)

	tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, customerID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if tipConfig != nil {
		result.PreferredTipEnable = tipConfig.Enable
		result.PreferredTipType = tipConfig.TipType
		result.PreferredTipAmount = tipConfig.Amount
		result.PreferredTipPercentage = tipConfig.Percentage
	}

	return result, nil
}

func (l *Logic) List(
	ctx context.Context, req *ListCustomerRelatedDataRequest,
) (*ListCustomerRelatedDataResponse, error) {
	// 构建过滤器
	filter := &customerrelateddatarepo.ListFilter{
		IDs:                  req.Filter.IDs,
		CustomerIDs:          req.Filter.CustomerIDs,
		BusinessIDs:          req.Filter.BusinessIDs,
		CompanyIDs:           req.Filter.CompanyIDs,
		States:               req.Filter.States,
		CustomerCodes:        req.Filter.CustomerCodes,
		AccountIDs:           req.Filter.AccountIDs,
		Sources:              req.Filter.Sources,
		IsLapsed:             req.Filter.IsLapsed,
		IsBlockMessage:       req.Filter.IsBlockMessage,
		IsBlockOnlineBooking: req.Filter.IsBlockOnlineBooking,
		LastServiceTimeGap:   req.Filter.LastServiceTimeGap,
		PreferredGroomerIDs:  req.Filter.PreferredGroomerIDs,
	}

	// 使用分页工具类处理page token
	paginationHelper := customerutils.NewPaginationHelper()
	pageToken := ""
	if req.Pagination != nil {
		pageToken = req.Pagination.Cursor
	}
	paginationInfo, err := paginationHelper.ProcessPageToken(pageToken, req.Pagination.PageSize)
	if err != nil {
		return nil, errs.Newm(codes.InvalidArgument, fmt.Sprintf("invalid page token: %v", err))
	}

	// 构建分页参数
	pagination := &customerrelateddatarepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		ReturnTotalSize: paginationInfo.ShouldReturnTotalSize(req.Pagination.ReturnTotalSize),
	}

	if paginationInfo.IsPageNumberMode() {
		// 使用offset分页
		pagination.Offset = &paginationInfo.Offset
	} else {
		// 使用cursor分页
		pagination.Cursor = paginationInfo.Cursor
	}

	// 构建排序
	var orderBy *customerrelateddatarepo.OrderBy
	if req.OrderBy != nil {
		orderBy = &customerrelateddatarepo.OrderBy{
			Field:     req.OrderBy.Field,
			Direction: req.OrderBy.Direction,
		}
	}

	// 查询数据
	result, err := l.customerRelatedDataRepo.ListByCursor(ctx, filter, pagination, orderBy)
	if err != nil {
		return nil, err
	}

	// 转换数据
	dataList := make([]*CustomerRelatedData, 0, len(result.Data))
	for _, data := range result.Data {
		dataList = append(dataList, convertToCustomerRelatedData(data))
	}

	// 生成下一页token
	var nextToken string
	if result.HasNext && len(result.Data) > 0 {
		var nextCursor *postgres.Cursor
		if paginationInfo.IsCursorMode() {
			// cursor模式，生成cursor
			lastItem := result.Data[len(result.Data)-1]
			nextCursor = &postgres.Cursor{ID: lastItem.ID, CreatedAt: lastItem.CreatedTime}
		}
		nextToken = paginationHelper.GenerateNextToken(paginationInfo, result.HasNext, nextCursor)
	}

	response := &ListCustomerRelatedDataResponse{
		CustomerRelatedData: dataList,
		HasNext:             result.HasNext,
		NextToken:           nextToken,
		TotalSize:           result.TotalCount,
	}

	return response, nil
}

func (l *Logic) Update(
	ctx context.Context, id int64, updateRef *UpdateCustomerRelatedDataRequest,
) (*CustomerRelatedData, error) {
	// check customer related data exists
	dbData, err := l.customerRelatedDataRepo.GetByCustomerID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND)
		}

		return nil, err
	}

	data := convertToCustomerRelatedData(dbData)

	// Load tip config and default frequency data
	tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if tipConfig != nil {
		data.PreferredTipEnable = tipConfig.Enable
		data.PreferredTipType = tipConfig.TipType
		data.PreferredTipAmount = tipConfig.Amount
		data.PreferredTipPercentage = tipConfig.Percentage
	}

	// 使用指针更新结构，支持零值更新
	updateData := data.ToDBUpdateWithPointers(updateRef)
	updatedData, err := l.customerRelatedDataRepo.Update(ctx, updateData)
	if err != nil {
		return nil, err
	}

	result := convertToCustomerRelatedData(updatedData)

	err = l.handlePreferredTipConfig(ctx, data, updateRef, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (l *Logic) BatchUpdate(
	ctx context.Context, updates []*UpdateCustomerRelatedDataRequest,
) ([]*CustomerRelatedData, error) {
	if len(updates) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "updates is required")
	}

	var updatedData []*CustomerRelatedData
	for _, updateReq := range updates {
		updatedItem, err := l.Update(ctx, updateReq.ID, updateReq)
		if err != nil {
			return nil, err
		}
		updatedData = append(updatedData, updatedItem)
	}

	return updatedData, nil
}

func (l *Logic) handlePreferredTipConfig(
	ctx context.Context,
	data *CustomerRelatedData,
	updateRef *UpdateCustomerRelatedDataRequest,
	result *CustomerRelatedData,
) error {
	// 检查是否有任何tip相关字段需要更新
	hasTipUpdate := updateRef.PreferredTipEnable != nil || updateRef.PreferredTipType != nil ||
		updateRef.PreferredTipAmount != nil || updateRef.PreferredTipPercentage != nil

	if hasTipUpdate {
		tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if tipConfig == nil {
			// 创建新的tip配置
			newTipConfig := &preferredtipconfigrepo.PreferredTipConfig{
				CustomerID: data.CustomerID,
				BusinessID: data.PreferredBusinessID,
				CompanyID:  data.CompanyID,
			}

			// 只设置传入的字段
			if updateRef.PreferredTipEnable != nil {
				newTipConfig.Enable = *updateRef.PreferredTipEnable
			}
			if updateRef.PreferredTipType != nil {
				newTipConfig.TipType = *updateRef.PreferredTipType
			}
			if updateRef.PreferredTipAmount != nil {
				newTipConfig.Amount = *updateRef.PreferredTipAmount
			}
			if updateRef.PreferredTipPercentage != nil {
				newTipConfig.Percentage = *updateRef.PreferredTipPercentage
			}

			tipConfig, err = l.preferredTipConfigRepo.Create(ctx, newTipConfig)
		} else {
			// 更新现有的tip配置，只更新传入的字段
			if updateRef.PreferredTipEnable != nil {
				tipConfig.Enable = *updateRef.PreferredTipEnable
			}
			if updateRef.PreferredTipType != nil {
				tipConfig.TipType = *updateRef.PreferredTipType
			}
			if updateRef.PreferredTipAmount != nil {
				tipConfig.Amount = *updateRef.PreferredTipAmount
			}
			if updateRef.PreferredTipPercentage != nil {
				tipConfig.Percentage = *updateRef.PreferredTipPercentage
			}

			tipConfig, err = l.preferredTipConfigRepo.Update(ctx, tipConfig.ToUpdateStruct())
		}

		if err != nil {
			return err
		}

		result.PreferredTipEnable = tipConfig.Enable
		result.PreferredTipType = tipConfig.TipType
		result.PreferredTipAmount = tipConfig.Amount
		result.PreferredTipPercentage = tipConfig.Percentage
	} else {
		// 没有tip更新，加载现有的tip配置
		tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if tipConfig != nil {
			result.PreferredTipEnable = tipConfig.Enable
			result.PreferredTipType = tipConfig.TipType
			result.PreferredTipAmount = tipConfig.Amount
			result.PreferredTipPercentage = tipConfig.Percentage
		}
	}

	return nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	now := time.Now().UTC()
	state := customerpb.CustomerRelatedData_DELETED

	updateData := &customerrelateddatarepo.Update{
		ID:          id,
		UpdatedTime: &now,
		DeletedTime: &now,
		State:       &state,
	}

	_, err := l.customerRelatedDataRepo.Update(ctx, updateData)
	if err != nil {
		return err
	}

	return nil
}

func convertToCustomerRelatedData(dbData *customerrelateddatarepo.CustomerRelatedData) *CustomerRelatedData {
	return &CustomerRelatedData{
		ID:                     dbData.ID,
		CustomerID:             dbData.CustomerID,
		PreferredBusinessID:    dbData.BusinessID,
		CompanyID:              dbData.CompanyID,
		ClientColor:            dbData.ClientColor,
		IsBlockMessage:         dbData.IsBlockMessage,
		IsBlockOnlineBooking:   dbData.IsBlockOnlineBooking,
		LoginEmail:             dbData.LoginEmail,
		ReferralSourceID:       dbData.ReferralSourceID,
		ReferralSourceDesc:     dbData.ReferralSourceDesc,
		SendAutoEmail:          dbData.SendAutoEmail,
		SendAutoMessage:        dbData.SendAutoMessage,
		SendAppAutoMessage:     dbData.SendAppAutoMessage,
		UnconfirmedReminderBy:  customerutils.ParseUnconfirmedReminderBy(dbData.UnconfirmedReminderBy),
		PreferredGroomerID:     dbData.PreferredGroomerID,
		PreferredFrequencyDay:  dbData.PreferredFrequencyDay,
		PreferredFrequencyType: dbData.PreferredFrequencyType,
		LastServiceTime:        dbData.LastServiceTime,
		Source:                 dbData.Source,
		ExternalID:             dbData.ExternalID,
		CreateBy:               dbData.CreateBy,
		UpdateBy:               dbData.UpdateBy,
		IsRecurring:            dbData.IsRecurring,
		ShareApptStatus:        dbData.ShareApptStatus,
		ShareRangeType:         dbData.ShareRangeType,
		ShareRangeValue:        dbData.ShareRangeValue,
		ShareApptJSON:          dbData.ShareApptJSON,
		PreferredDay:           dbData.PreferredDay,
		PreferredTime:          dbData.PreferredTime,
		AccountID:              dbData.AccountID,
		CustomerCode:           dbData.CustomerCode,
		IsUnsubscribed:         dbData.IsUnsubscribed,
		Birthday:               dbData.Birthday,
		ActionState:            dbData.ActionState,
		CustomizeLifeCycleID:   dbData.CustomizeLifeCycleID,
		CustomizeActionStateID: dbData.CustomizeActionStateID,
		State:                  dbData.State,
		DeletedTime:            dbData.DeletedTime,
		CreatedTime:            dbData.CreatedTime,
		UpdatedTime:            dbData.UpdatedTime,
	}
}
