load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "cdc",
    srcs = [
        "action_state_consumer.go",
        "address.go",
        "contact.go",
        "customer_consumer.go",
        "entity.go",
        "history_log_consumer.go",
        "life_cycle_consumer.go",
        "note_consumer.go",
        "source_consumer.go",
        "tag_binding_consumer.go",
        "tag_consumer.go",
        "task_consumer.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/cdc",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/logic/contact_tag",
        "//backend/app/customer/repo/db/customer",
        "//backend/app/customer/repo/organization:staff",
        "//backend/app/customer/repo/postgres/action_state",
        "//backend/app/customer/repo/postgres/activity_log",
        "//backend/app/customer/repo/postgres/activity_relation",
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/postgres/customer_related_data",
        "//backend/app/customer/repo/postgres/life_cycle",
        "//backend/app/customer/repo/postgres/note",
        "//backend/app/customer/repo/postgres/source",
        "//backend/app/customer/repo/postgres/tag",
        "//backend/app/customer/repo/postgres/task",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_ibm_sarama//:sarama",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)

go_test(
    name = "cdc_test",
    srcs = ["entity_test.go"],
    embed = [":cdc"],
    deps = [
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
