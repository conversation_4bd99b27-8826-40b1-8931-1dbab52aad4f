CREATE TABLE contract_template
(
    id         TEXT PRIMARY KEY,
    name       TEXT                     not null,
    version    int                      not null,
    template   TEXT                     not null,
    created_at TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX contract_template_name_version_uk ON contract_template (name, version);

create table contract
(
    id            TEXT primary key,
    creator       TEXT                     not null,
    template_id   TEXT                     not null,
    metadata      JSONB                    not null default '{}',
    parameters    JSONB                    not null default '{}',
    content       TEXT                     not null default '',
    signature_uri TEXT,
    signed_at     TIMESTAMP WITH TIME ZONE,
    created_at    TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    deleted_at    TIMESTAMP WITH TIME ZONE
);

