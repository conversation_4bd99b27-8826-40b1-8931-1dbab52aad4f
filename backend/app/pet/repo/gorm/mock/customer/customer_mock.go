// Code generated by MockGen. DO NOT EDIT.
// Source: ./customer/customer.go
//
// Generated by this command:
//
//	mockgen -source=./customer/customer.go -destination=mock/./customer/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customer "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/customer"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, params *customer.ListParams) ([]*customer.BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, params)
	ret0, _ := ret[0].([]*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, params)
}
