server:
  filter:
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.authn.v1.AuthnService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 500000
    - name: backend.proto.authn.v1.MfaManagementService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 500000
    - name: backend.proto.authn.v1.ImpersonationConsentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 500000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 500000
  service:
    - callee: moego-message-hub
      target: dns://moego-message-hub:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-account
      target: dns://moego-svc-account:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-server-business
      target: http://moego-service-business:9203
      protocol: http
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: postgres.moego_account
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_account
      protocol: gorm
      transport: gorm
    - callee: redis
      target: redis://redis.t2.moego.dev:40179/0?tls=true
      password: iMoReGoTdesstingeCache250310_7fec987d
      timeout: 5000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_account
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  auth:
    validation:
      enable_error_log: false