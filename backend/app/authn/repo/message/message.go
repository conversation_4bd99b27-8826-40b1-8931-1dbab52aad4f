package message

import (
	"context"

	"google.golang.org/genproto/googleapis/type/phone_number"

	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type API interface {
	// SendVerificationCode sends a verification code via SMS.
	SendVerificationCode(ctx context.Context, regionCode, e164Number, content string) error
}

type api struct {
	messageHub messagehubpb.MessageHubServiceClient
}

func NewAPI() API {
	return &api{
		messageHub: grpc.NewClient("moego-message-hub", messagehubpb.NewMessageHubServiceClient),
	}
}

func (a *api) SendVerificationCode(ctx context.Context, regionCode, e164Number, content string) error {
	req := &messagehubpb.SendMessageRequest{
		Payload: &messagehubpb.SendMessageRequest_Sms{
			Sms: &messagehubpb.SendSmsPayload{
				RegionCode: regionCode,
				Recipient: &phone_number.PhoneNumber{
					Kind: &phone_number.PhoneNumber_E164Number{
						E164Number: e164Number,
					},
				},
				Content: content,
			},
		},
	}

	resp, err := a.messageHub.SendMessage(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "fail to send verification code to %s: %v", e164Number, err)

		return authnutils.VerificationCodeSendError
	}

	if resp.GetState() == messagehubpb.SendState_SEND_STATE_FAILED {
		log.ErrorContextf(ctx, "fail to send verification code to %s: [%s] %s",
			e164Number, resp.GetErrorCode(), resp.GetErrorMessage())

		return authnutils.VerificationCodeSendError
	}

	return nil
}
