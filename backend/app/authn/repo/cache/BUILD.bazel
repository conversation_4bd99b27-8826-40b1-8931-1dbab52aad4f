load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cache",
    srcs = ["verification_code.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/repo/cache",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/authn/config",
        "//backend/app/authn/utils",
        "//backend/common/rpc/database/goredis",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/env",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)
