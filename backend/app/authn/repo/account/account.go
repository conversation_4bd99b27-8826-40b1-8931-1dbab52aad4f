package account

import (
	"context"
	"errors"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type API interface {
	// GetAccountByEmail get account by email. If the account of given email does not exist, return nil
	GetAccountByEmail(ctx context.Context, email string) (*accountpb.AccountModel, error)
	// ValidatePassword validate password
	ValidatePassword(ctx context.Context, accountID int64, password string) bool
	InitBusinessSessionContext(ctx context.Context, accountID, sessionID int64) error
	SyncHubspotAccount(ctx context.Context, accountID int64)
}

type api struct {
	account      accountsvcpb.AccountServiceClient
	session      accountsvcpb.SessionServiceClient
	organization organizationsvcpb.HubspotServiceClient
	business     http.Client
}

func (a *api) GetAccountByEmail(ctx context.Context, email string) (*accountpb.AccountModel, error) {
	account, err := a.account.GetAccount(ctx, &accountsvcpb.GetAccountRequest{
		Identifier: &accountsvcpb.GetAccountRequest_Email{Email: email},
	})
	if err != nil {
		var e *errs.Error
		if errors.As(err, &e) && errorspb.Code(e.Code) == errorspb.Code_CODE_ACCOUNT_NOT_EXIST {
			return nil, nil
		}
		log.WarnContextf(ctx, "fail to get account by email %s", email)

		return nil, err
	}

	return account, nil
}

func (a *api) ValidatePassword(ctx context.Context, accountID int64, password string) bool {
	resp, err := a.account.ValidatePassword(ctx, &accountsvcpb.ValidatePasswordRequest{
		Id:       accountID,
		Password: password,
	})
	if err != nil {
		log.ErrorContextf(ctx, "fail to validate password for account %d", accountID)

		return false
	}

	return resp.GetCorrect()
}

type initBusinessSessionContextParams struct {
	AccountID int64 `json:"accountId"`
	SessionID int64 `json:"sessionId"`
}

func (a *api) InitBusinessSessionContext(ctx context.Context, accountID, sessionID int64) error {
	params := &initBusinessSessionContextParams{
		AccountID: accountID,
		SessionID: sessionID,
	}

	return a.business.Post(ctx, "/service/business/session/init/v2", params, nil)
}

func (a *api) SyncHubspotAccount(ctx context.Context, accountID int64) {
	_, err := a.organization.InitContact(ctx, &organizationsvcpb.InitContactRequest{
		AccountId: accountID,
	})

	// log and ignore error
	if err != nil {
		log.ErrorContextf(ctx, "fail to sync hubspot for account id %d", accountID)
	}
}

func NewAPI() API {
	return &api{
		account:      grpc.NewClient("moego-svc-account", accountsvcpb.NewAccountServiceClient),
		session:      grpc.NewClient("moego-svc-account", accountsvcpb.NewSessionServiceClient),
		organization: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewHubspotServiceClient),
		business:     http.NewClientProxy("moego-server-business"),
	}
}
