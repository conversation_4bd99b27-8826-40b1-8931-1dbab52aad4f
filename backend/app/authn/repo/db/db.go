package db

import (
	"context"
	"errors"
	"sync"

	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

const (
	// pgUniqueViolationCode is the error code for unique_violation in PostgreSQL.
	pgUniqueViolationCode = "23505"
)

var (
	db   Database
	once sync.Once
)

// QueryFilter defines the interface for session query filters.
type QueryFilter func(*gorm.DB) *gorm.DB

type Page struct {
	Number  int
	Size    int
	OrderBy clause.OrderBy // 分页必须携带排序规则，以确保分页结果稳定
}

func (p *Page) Apply(sql *gorm.DB) *gorm.DB {
	if p == nil {
		return sql
	}
	size := p.Size
	// 非法 size，设置为 0 返回空结果
	if size < 0 {
		size = 0
	}

	// 非法 number，设置为 1 从头开始返回结果
	number := p.Number
	if number < 1 {
		number = 1
	}

	offset := size * (number - 1)

	return sql.Order(p.OrderBy).Offset(offset).Limit(size)
}

// Database is the interface for the database repository.
type Database interface {
	AuthenticationFactorReadWriter
	TrustedDeviceReadWriter
	ImpersonationConsentReadWriter
	SessionReadWriter
	AccountSourceReadWriter

	// WithTx executes the given function within a database transaction.
	// If the function returns an error, the transaction is rolled back. Otherwise, the transaction is committed.
	WithTx(ctx context.Context, fn func(repo Database) error) error
}

type database struct {
	db *gorm.DB
}

// New returns a new database repository for production use.
func New() Database {
	once.Do(func() {
		gormDB, err := igorm.NewClientProxy("postgres.moego_account")
		if err != nil {
			panic(err)
		}
		db = newDatabase(gormDB)
	})

	return db
}

// newDatabase is the internal constructor that allows injecting a gorm.DB instance, primarily for testing.
func newDatabase(gormDB *gorm.DB) Database {
	return &database{db: gormDB}
}

func (d *database) WithTx(ctx context.Context, fn func(repo Database) error) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&database{db: tx})
	})
}

// IsUniqueConstraintViolationError checks if the error is a unique constraint violation error.
func IsUniqueConstraintViolationError(err error) bool {
	var pgErr *pgconn.PgError

	return errors.As(err, &pgErr) && pgErr.Code == pgUniqueViolationCode
}

// IsRecordNotFoundError checks if the error is a gorm.ErrRecordNotFound error.
func IsRecordNotFoundError(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

// truncateStringByRune 按字符（rune）安全地截断字符串，以满足数据库字段的长度限制。
//
// PostgreSQL 的 `varchar(n)` 或 `character varying(n)` 类型是根据字符数（而不是字节数）来限制长度的。
// 此函数通过将字符串转换为 rune 切片来确保截断发生在字符边界上，从而正确处理
// 中文、日文、Emoji 等多字节字符，避免因直接按字节截断而导致的乱码问题和数据库写入错误。
func truncateStringByRune(s string, maxLen int) string {
	runes := []rune(s)
	if len(runes) > maxLen {
		return string(runes[:maxLen])
	}

	return s
}
