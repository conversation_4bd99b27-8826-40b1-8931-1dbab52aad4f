package db

import (
	"context"
)

const (
	maxRefererLinkLen = 256
)

// SessionReadWriter defines the interface for reading and writing sessions.
type SessionReadWriter interface {
	CreateSession(ctx context.Context, session *Session) error
	GetSession(ctx context.Context, id int64) (*Session, error)
	ListSessions(ctx context.Context, page *Page, filters ...QueryFilter) ([]*Session, error)
	DeleteSession(ctx context.Context, id int64) error
	DeleteSessions(ctx context.Context, ids []int64) error
}

func (d *database) CreateSession(ctx context.Context, session *Session) error {
	session.RefererLink = truncateStringByRune(session.RefererLink, maxRefererLinkLen)

	return d.db.WithContext(ctx).Create(session).Error
}

func (d *database) GetSession(ctx context.Context, id int64) (*Session, error) {
	var session Session
	err := d.db.WithContext(ctx).
		Where("id = ? AND status = ?", id, SessionStatusActive). // Only find active sessions
		First(&session).
		Error
	if err != nil {
		if IsRecordNotFoundError(err) {
			return nil, nil
		}

		return nil, err
	}

	return &session, nil
}

func (d *database) ListSessions(ctx context.Context, page *Page, filters ...QueryFilter) ([]*Session, error) {
	var sessions []*Session
	sql := d.db.WithContext(ctx).Model(&Session{})
	for _, filter := range filters {
		sql = filter(sql)
	}
	sql = page.Apply(sql)

	err := sql.Find(&sessions).Error
	if err != nil {
		return nil, err
	}

	return sessions, nil
}

func (d *database) DeleteSession(ctx context.Context, id int64) error {
	// Soft delete by updating the status to SessionStatusDeleted.
	return d.db.WithContext(ctx).Model(&Session{}).
		Where("id = ?", id).
		Update("status", SessionStatusDeleted).Error
}

func (d *database) DeleteSessions(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	// Soft delete by updating the status to SessionStatusDeleted.
	return d.db.WithContext(ctx).Model(&Session{}).
		Where("id IN ?", ids).
		Update("status", SessionStatusDeleted).Error
}
