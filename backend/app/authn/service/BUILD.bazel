load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "authn_service.go",
        "impersonation_consent_service.go",
        "mfa_management_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/authn/logic",
        "//backend/proto/authn/v1:authn",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
