package agent

import (
	"github.com/openai/openai-go/packages/param"
	"github.com/openai/openai-go/responses"
)

func getAvailableTools() []responses.ToolUnionParam {
	tools := []responses.ToolUnionParam{
		{
			OfFunction: &responses.FunctionToolParam{
				Name:        "create_appointment",
				Description: param.NewOpt("To book a new grooming appointment"),
				Parameters: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"pet_id": map[string]any{
							"type":        "integer",
							"description": "The id of the pet, you can get it from the pet list",
						},
						"service_id": map[string]any{
							"type":        "integer",
							"description": "The id of the service",
						},
						"start_date": map[string]any{
							"type":        "string",
							"description": "The start date of the appointment",
						},
						"start_time": map[string]any{
							"type":        "integer",
							"description": "The minutes after 00:00 at start_date",
						},
					},
				},
			},
		},
		{
			OfFunction: &responses.FunctionToolParam{
				Name:        "check_availability",
				Description: param.NewOpt("Use this before promising an appointment time. It will return a list of available times for a given service on a specific date. You should offer these times to the customer."),
				Parameters: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"service_type": map[string]any{
							"type":        "string",
							"description": "service type",
						},
						"requested_date": map[string]any{
							"type":        "string",
							"description": "requested date",
						},
					},
				},
			},
		},
		{
			OfFunction: &responses.FunctionToolParam{
				Name:        "get_grooming_status",
				Description: param.NewOpt(`Use this when a caller asks "How is my dog doing?", "Is Fluffy ready yet?", or "What's the status of my appointment?". The status can be "Waiting", "In Progress", "Finishing Up", or "Ready for Pickup".`),
				Parameters: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"pet_name": map[string]any{
							"type":        "string",
							"description": "The name of the pet",
						},
					},
				},
			},
		},
	}
	return tools
}
