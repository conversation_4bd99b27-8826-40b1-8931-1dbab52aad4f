load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "agent",
    srcs = [
        "agent.go",
        "function_call.go",
        "instruction.go",
        "openai.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/agent",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/voice_agent/api",
        "//backend/app/voice_agent/entity/twilio",
        "//backend/app/voice_agent/logic/agent/converter",
        "//backend/app/voice_agent/repo/customer",
        "//backend/app/voice_agent/repo/openai",
        "@com_github_gorilla_websocket//:websocket",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_openai_openai_go//packages/param",
        "@com_github_openai_openai_go//responses",
    ],
)
