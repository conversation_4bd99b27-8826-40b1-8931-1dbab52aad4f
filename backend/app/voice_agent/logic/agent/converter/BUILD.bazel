load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "converter",
    srcs = [
        "business.go",
        "customer.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent/logic/agent/converter",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/voice_agent/repo/customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
    ],
)
