package converter

import (
	"encoding/json"

	"github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/customer"
)

type Customer struct {
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
}

func ConvertCustomer(customer *customer.Customer) string {
	c := &Customer{
		FirstName: customer.FirstName,
		LastName:  customer.LastName,
	}
	str, _ := json.Marshal(c)
	return string(str)
}
