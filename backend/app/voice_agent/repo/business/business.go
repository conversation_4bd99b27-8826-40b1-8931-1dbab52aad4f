package business

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

type Client interface {
	Get(ctx context.Context, ID int64) (*organizationpb.LocationModel, error)
}

func New() Client {
	conn, err := grpc.NewClient("moego-svc-organization:9090",
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		panic(err)
	}
	c := organizationsvcpb.NewBusinessServiceClient(conn)
	return &impl{
		client: c,
	}
}

type impl struct {
	client organizationsvcpb.BusinessServiceClient
}

func (i *impl) Get(ctx context.Context, ID int64) (*organizationpb.LocationModel, error) {
	req := &organizationsvcpb.GetLocationDetailRequest{
		Id: ID,
	}
	rsp, err := i.client.GetLocationDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp.GetLocation(), nil
}
