package onboardingutils

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"net/http"

	"github.com/google/uuid"
	"google.golang.org/grpc/metadata"
)

const (
	MaxMetadataSize  = 64 * 1024 // 64KB
	MaxMetadataDepth = 10        // 10 levels deep
)

var (
	HTTPClient = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
)

func getGrpcMetadata(ctx context.Context) metadata.MD {
	// 从上下文中获取 grpc metadata
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		return md
	}

	return metadata.New(nil)
}

func newDeviceID() string {
	return uuid.New().String()
}

func GetDeviceID(ctx context.Context) string {
	md := getGrpcMetadata(ctx)

	id := md.Get("x-moe-device-id")
	if len(id) > 0 && len(id[0]) > 0 {
		return id[0]
	}

	return newDeviceID()
}

// ValidateAndCleanMetadata validates and cleans user-controlled metadata
// It checks for size limits and nesting depth to prevent attacks like
// billion laughs or memory exhaustion
func ValidateAndCleanMetadata(metadata []byte) ([]byte, error) {
	// Check size limit
	if len(metadata) > MaxMetadataSize {
		return nil, fmt.Errorf("metadata size exceeds limit of %d bytes", MaxMetadataSize)
	}

	// Parse JSON to check depth
	var data interface{}
	if err := json.Unmarshal(metadata, &data); err != nil {
		return nil, fmt.Errorf("invalid metadata JSON: %w", err)
	}

	// Check nesting depth
	if depth := getDepth(data); depth > MaxMetadataDepth {
		return nil, fmt.Errorf("metadata nesting depth exceeds limit of %d levels", MaxMetadataDepth)
	}

	// If all checks pass, return the original metadata
	return metadata, nil
}

// getDepth calculates the nesting depth of a JSON structure
func getDepth(data interface{}) int {
	switch val := data.(type) {
	case map[string]interface{}:
		maxDepth := 0
		for _, v := range val {
			if depth := getDepth(v); depth > maxDepth {
				maxDepth = depth
			}
		}

		return maxDepth + 1
	case []interface{}:
		maxDepth := 0
		for _, v := range val {
			if depth := getDepth(v); depth > maxDepth {
				maxDepth = depth
			}
		}

		return maxDepth + 1
	default:
		return 0
	}
}

// GetIP get current IP address
func GetIP(ctx context.Context) string {
	// 先尝试从 grpc metadata 中获取
	md := getGrpcMetadata(ctx)
	ip := md.Get("x-forwarded-for")
	if len(ip) > 0 && len(ip[0]) > 0 {
		return ip[0]
	}

	// 获取本机IP地址
	addresses, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range addresses {
		ipNet, ok := address.(*net.IPNet)
		// 跳过回环地址
		if !ok || ipNet.IP.IsLoopback() {
			continue
		}
		// 只返回IPv4地址
		if ipNet.IP.To4() != nil {
			return ipNet.IP.String()
		}
	}

	return ""
}
