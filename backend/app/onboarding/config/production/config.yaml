server:
  app: onboarding
  server: onboarding
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.onboarding.v1.OnboardingService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 10000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres
      target: dsn://postgresql://${secret.datasource.postgres.moego_onboarding.username}:${secret.datasource.postgres.moego_onboarding.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_onboarding
      protocol: gorm
      transport: gorm
    - callee: moego-pawpilot
      target: http://moego-pawpilot:8080
      protocol: http
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: true
  auth:
    validation:
      enable_error_log: false
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}

secrets:
  - name: "moego/production/nacos"
    prefix: "secret.nacos."
  - name: 'moego/production/datasource'
    prefix: 'secret.datasource.'