package gorm

import (
	"context"
	"fmt"
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

// OnboardingRepo defines the interface for interacting with the onboarding data.
type OnboardingRepo interface {
	GetOnboardingFlow(ctx context.Context, companyID, businessID, staffID int64, flowID string) (*OnboardingFlow, error)
	CreateOnboardingFlow(ctx context.Context, flow *OnboardingFlow) error
	UpdateOnboardingFlow(ctx context.Context, flow *OnboardingFlow) error

	GetOnboardingSteps(ctx context.Context, flowRecordID int64) ([]*OnboardingStep, error)
	CreateOnboardingStep(ctx context.Context, step *OnboardingStep) error
	UpdateOnboardingStep(ctx context.Context, step *OnboardingStep) error

	// Task related methods
	CreateBookingPageTask(ctx context.Context, task *BookingPageTask) error
	GetBookingPageTask(ctx context.Context, companyID, businessID int64,
		staffID *int64, taskID *string) (*BookingPageTask, error)
	UpdateBookingPageTask(ctx context.Context, task *BookingPageTask) error

	// WithTx returns a new repo instance that uses the provided transaction
	WithTx(ctx context.Context) (OnboardingRepo, error)

	// Transaction control methods
	Commit() error
	Rollback() error
}

type impl struct {
	db *gorm.DB
}

var (
	mu       sync.Mutex
	instance OnboardingRepo
)

// New creates a new OnboardingRepo instance
func New() (OnboardingRepo, error) {
	mu.Lock()
	defer mu.Unlock()

	// Allow re-initialization for testing purposes
	db, err := igorm.NewClientProxy("postgres")
	if err != nil {
		return nil, fmt.Errorf("failed to create database client: %w", err)
	}
	instance = &impl{
		db: db,
	}

	return instance, nil
}

func (r *impl) GetOnboardingFlow(ctx context.Context, companyID, businessID, staffID int64,
	flowID string) (*OnboardingFlow, error) {
	var flow OnboardingFlow
	err := r.db.WithContext(ctx).Where("company_id = ? AND business_id = ? AND staff_id = ? AND flow_id = ?",
		companyID, businessID, staffID, flowID).First(&flow).Error

	return &flow, err
}

func (r *impl) CreateOnboardingFlow(ctx context.Context, flow *OnboardingFlow) error {
	return r.db.WithContext(ctx).Create(flow).Error
}

func (r *impl) UpdateOnboardingFlow(ctx context.Context, flow *OnboardingFlow) error {
	return r.db.WithContext(ctx).Save(flow).Error
}

func (r *impl) GetOnboardingSteps(ctx context.Context, flowRecordID int64) ([]*OnboardingStep, error) {
	var steps []*OnboardingStep
	err := r.db.WithContext(ctx).Where("flow_record_id = ?", flowRecordID).Find(&steps).Error

	return steps, err
}

func (r *impl) CreateOnboardingStep(ctx context.Context, step *OnboardingStep) error {
	return r.db.WithContext(ctx).Create(step).Error
}

func (r *impl) UpdateOnboardingStep(ctx context.Context, step *OnboardingStep) error {
	return r.db.WithContext(ctx).Save(step).Error
}

// Task related methods
func (r *impl) CreateBookingPageTask(ctx context.Context, task *BookingPageTask) error {
	return r.db.WithContext(ctx).Create(task).Error
}

func (r *impl) GetBookingPageTask(ctx context.Context, companyID, businessID int64,
	staffID *int64, taskID *string) (*BookingPageTask, error) {
	var task BookingPageTask
	query := r.db.WithContext(ctx).Where("company_id = ? AND business_id = ?", companyID, businessID)
	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}
	if staffID != nil {
		query = query.Where("staff_id = ?", *staffID)
	}
	err := query.Order("created_at DESC").First(&task).Error

	return &task, err
}

func (r *impl) UpdateBookingPageTask(ctx context.Context, task *BookingPageTask) error {
	return r.db.WithContext(ctx).Save(task).Error
}

// WithTx returns a new repo instance that uses the provided transaction
func (r *impl) WithTx(ctx context.Context) (OnboardingRepo, error) {
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	return &impl{db: tx}, nil
}

// Commit commits the transaction
func (r *impl) Commit() error {
	return r.db.Commit().Error
}

// Rollback rolls back the transaction
func (r *impl) Rollback() error {
	return r.db.Rollback().Error
}
