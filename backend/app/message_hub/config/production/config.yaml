server:
  app: template
  server: template-go
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.message_hub.v1.MessageHubService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.message_hub.v1.ProviderCallbackService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 60000
  service:
    - callee: postgres.moego_message_hub
      target: dsn://postgresql://${secret.datasource.postgres.moego_message_hub.username}:${secret.datasource.postgres.moego_message_hub.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_message_hub
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
        - name: postgres.moego_message_hub
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}

secrets:
  - name: 'moego/production/datasource'
    prefix: 'secret.datasource.'
  - name: "moego/production/nacos"
    prefix: "secret.nacos."