package provider

import (
	"context"
	"net/url"
	"strconv"
	"time"

	twilio "github.com/twilio/twilio-go"
	twilioapi "github.com/twilio/twilio-go/rest/api/v2010"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/config"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/db"
	messagehubutils "github.com/MoeGolibrary/moego/backend/app/message_hub/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
)

type twilioProvider struct {
	client *twilio.RestClient
}

// NewTwilioProvider creates a new MessageProvider for Twilio.
func NewTwilioProvider() MessageProvider {
	twilioCfg := config.GetCfg().Twilio
	if twilioCfg == nil {
		panic("no twilio config")
	}

	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: twilioCfg.AccountSid,
		Password: twilioCfg.AuthToken,
	})

	return &twilioProvider{
		client: client,
	}
}

// Name returns the unique name for the Twilio provider.
func (p *twilioProvider) Name() string {
	return messagehubutils.MessageProviderTwilio
}

// SendSMS sends an SMS using the Twilio client and returns a fully formed MessageState.
func (p *twilioProvider) SendSMS(_ context.Context, msg *db.Message) *db.MessageState {
	params := &twilioapi.CreateMessageParams{}
	params.SetBody(msg.Content)
	params.SetFrom(msg.Sender)
	params.SetTo(msg.Recipient)
	params.SetStatusCallback(getStatusCallbackURL(msg.ID))

	resp, err := p.client.Api.CreateMessage(params)
	if err != nil {
		// 捕获灾难性错误，并将其封装在 MessageState 中
		failedState := messagehubutils.TwilioMessageStateFailed
		errMsg := err.Error()

		return &db.MessageState{
			ID:           msg.ID,
			Type:         msg.Type,
			State:        &failedState,
			ErrorCode:    &failedState,
			ErrorMessage: &errMsg,
			CreatedAt:    time.Now(),
		}
	}

	return p.buildMessageState(msg, resp)
}

func (p *twilioProvider) buildMessageState(msg *db.Message, sms *twilioapi.ApiV2010Message) *db.MessageState {
	var errorCode *string
	if sms.ErrorCode != nil {
		code := strconv.Itoa(*sms.ErrorCode)
		errorCode = &code
	}

	createdAt := time.Now()
	if sms.DateCreated != nil {
		if d, err := parseRFC2822(*sms.DateCreated); err == nil {
			createdAt = d
		}
	}

	return &db.MessageState{
		ID:           msg.ID,
		Type:         msg.Type,
		State:        sms.Status,
		ErrorCode:    errorCode,
		ErrorMessage: sms.ErrorMessage,
		ParsedExtra: &db.SmsStateDetails{
			Sid:                 sms.Sid,
			NumSegments:         sms.NumSegments,
			MessagingServiceSid: sms.MessagingServiceSid,
		},
		CreatedAt: createdAt,
	}
}

func getStatusCallbackURL(id string) string {
	q := url.Values{}
	q.Set("id", id)

	u := url.URL{
		Scheme:   "https",
		Host:     env.GetDomainHost(env.GO),
		Path:     "/moego.bff/message-hub/handleTwilioSmsStatusCallback",
		RawQuery: q.Encode(),
	}

	return u.String()
}

func parseRFC2822(s string) (time.Time, error) {
	// Twilio uses RFC 2822 format, e.g., "Mon, 02 Jan 2006 15:04:05 -0700"
	layout := "Mon, 02 Jan 2006 15:04:05 -0700"

	return time.Parse(layout, s)
}
