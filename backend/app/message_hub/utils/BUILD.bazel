load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "balancer.go",
        "const.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/message_hub/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/message_hub/v1:message_hub",
        "@com_github_google_uuid//:uuid",
    ],
)
