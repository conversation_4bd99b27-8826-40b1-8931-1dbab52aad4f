package logic

import (
	"context"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/provider"
	messagehubutils "github.com/MoeGolibrary/moego/backend/app/message_hub/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type SmsLogic struct {
	db        db.MessageHub
	providers map[string]provider.MessageProvider
}

func NewSmsLogic() *SmsLogic {
	providers := []provider.MessageProvider{
		provider.NewTwilioProvider(),
		provider.NewAwsProvider(),
	}

	return newSmsLogicByParams(db.NewMessageHub(), providers)
}

func newSmsLogicByParams(db db.MessageHub, providers []provider.MessageProvider) *SmsLogic {
	providerMap := make(map[string]provider.MessageProvider, len(providers))
	for _, p := range providers {
		providerMap[p.Name()] = p
	}

	return &SmsLogic{
		db:        db,
		providers: providerMap,
	}
}

func (l *SmsLogic) Send(ctx context.Context,
	payload *messagehubpb.SendSmsPayload) (*messagehubpb.SendMessageResponse, error) {

	// build message
	msg, err := l.buildMessage(ctx, payload)
	if err != nil {
		return nil, err
	}

	// save to db
	if err := l.db.CreateMessage(ctx, msg); err != nil {
		return nil, err
	}

	// dynamically select provider and send message
	p, ok := l.providers[msg.Provider]
	if !ok {
		log.ErrorContextf(ctx, "unsupported provider: %s", msg.Provider)

		return nil, errs.Newm(codes.InvalidArgument, "Unsupported provider")
	}

	state := p.SendSMS(ctx, msg)
	log.InfoContextf(ctx, "Sent SMS via %s, message id: %s", msg.Provider, msg.ID)

	if err := l.db.CreateMessageState(ctx, state); err != nil {
		log.ErrorContextf(ctx, "failed to save message state for message %s: %v", msg.ID, err)

		return nil, err
	}

	return &messagehubpb.SendMessageResponse{
		MessageId:    msg.ID,
		State:        messagehubutils.ToSendStateEnum(state.State),
		ErrorCode:    state.ErrorCode,
		ErrorMessage: state.ErrorMessage,
	}, nil
}

func (l *SmsLogic) HandleTwilioSmsStatusCallbackV2(ctx context.Context,
	req *messagehubpb.HandleTwilioSmsStatusCallbackRequest) error {
	log.InfoContextf(ctx, "Received Twilio callback: %+v", req)

	stateStr := req.GetMessageStatus()
	state := &db.MessageState{
		ID:        req.GetCustom().GetId(),
		Type:      messagehubutils.MessageTypeSMS,
		State:     &stateStr,
		ErrorCode: req.ErrorCode,
	}

	return l.db.UpdateMessageState(ctx, state)
}

func (l *SmsLogic) HandleAwsMessagingStatusCallback(
	ctx context.Context, req *messagehubpb.HandleAwsMessagingStatusCallbackRequest,
) error {
	log.InfoContextf(ctx, "Received AWS callback: %+v", req)

	stateStr := req.GetMessageStatus()
	var errorCode, errorMessage *string

	// Determine if the status represents a failure state.
	// Only populate errorCode and errorMessage for failures to avoid redundancy
	// and semantic mismatch (e.g., storing a success description in ErrorMessage).
	if messagehubutils.IsSendFailed(&stateStr) {
		errorCode = &stateStr
		errorMessage = &req.MessageStatusDescription
	}

	state := &db.MessageState{
		ID:           req.GetContext().GetId(),
		Type:         messagehubutils.MessageTypeSMS,
		State:        &stateStr,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
	}

	return l.db.UpdateMessageState(ctx, state)
}

func (l *SmsLogic) buildMessage(ctx context.Context, payload *messagehubpb.SendSmsPayload) (*db.Message, error) {
	var sender, providerName string
	// if sender is empty, use platform number
	if payload.GetSender() == nil || payload.GetSender().GetE164Number() == "" {
		platformSender, err := l.getPlatformSender(ctx, payload.GetRegionCode())
		if err != nil {
			return nil, err
		}
		sender = platformSender.Identity
		providerName = platformSender.Provider
	} else {
		sender = payload.GetSender().GetE164Number()
		// TODO: How to determine the provider when the sender is specified?
		// For now, default to Twilio. This needs a clearer business rule.
		providerName = messagehubutils.MessageProviderTwilio
	}

	// set callbacks
	var callbacks *db.Callbacks
	if payload.GetStateCallback() != nil {
		var t string
		var p map[string]any
		switch payload.GetStateCallback().GetMethod().(type) {
		case *messagehubpb.Callback_Kafka:
			{
				t = messagehubutils.MessageCallbackTypeKafka
				p = map[string]any{
					"topic": payload.GetStateCallback().GetKafka().GetTopic(),
				}
			}
		case *messagehubpb.Callback_Grpc:
			{
				t = messagehubutils.MessageCallbackTypeGrpc
				p = map[string]any{
					"server": payload.GetStateCallback().GetGrpc().GetServer(),
				}
			}
		}
		callbacks = &db.Callbacks{
			State: &db.Callback{
				Type:   t,
				Params: p,
			},
		}
	}

	return &db.Message{
		ID:        messagehubutils.NewUUID(),
		Provider:  providerName,
		Direction: messagehubutils.MessageDirectionOutbound,
		Type:      messagehubutils.MessageTypeSMS,
		Sender:    sender,
		Recipient: payload.GetRecipient().GetE164Number(),
		Content:   payload.GetContent(),
		Callbacks: callbacks,
	}, nil
}

func (l *SmsLogic) getPlatformSender(ctx context.Context, country string) (*db.PlatformSender, error) {
	senders, err := l.db.ListPlatformSenders(ctx, country)
	if err != nil {
		return nil, err
	}

	sender, ok := messagehubutils.WeightedPick(senders)
	if !ok {
		return nil, errs.Newm(codes.InvalidArgument, "Unsupported country")
	}

	return sender, nil
}
