package logic

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/phone_number"

	mockdb "github.com/MoeGolibrary/moego/backend/app/message_hub/mock/repo/db"
	mockprovider "github.com/MoeGolibrary/moego/backend/app/message_hub/mock/repo/provider"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/provider"
	messagehubutils "github.com/MoeGolibrary/moego/backend/app/message_hub/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type smsLogicTestSuite struct {
	suite.Suite
	ctrl               *gomock.Controller
	mockDB             *mockdb.MockMessageHub
	mockTwilioProvider *mockprovider.MockMessageProvider
	mockAwsProvider    *mockprovider.MockMessageProvider
	logic              *SmsLogic
}

func (s *smsLogicTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockDB = mockdb.NewMockMessageHub(s.ctrl)
	s.mockTwilioProvider = mockprovider.NewMockMessageProvider(s.ctrl)
	s.mockAwsProvider = mockprovider.NewMockMessageProvider(s.ctrl)

	s.mockTwilioProvider.EXPECT().Name().Return(messagehubutils.MessageProviderTwilio)
	s.mockAwsProvider.EXPECT().Name().Return(messagehubutils.MessageProviderAWS)
	providers := []provider.MessageProvider{s.mockTwilioProvider, s.mockAwsProvider}
	s.logic = newSmsLogicByParams(s.mockDB, providers)
}

func (s *smsLogicTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func TestSmsLogicTestSuite(t *testing.T) {
	suite.Run(t, new(smsLogicTestSuite))
}

func (s *smsLogicTestSuite) TestSend_Success() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:   "hello",
		Recipient: &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:    &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockTwilioProvider.EXPECT().SendSMS(ctx, gomock.Any()).Return(&db.MessageState{
		State:       pointer.Get(messagehubutils.TwilioMessageStateSent),
		ParsedExtra: &db.SmsStateDetails{Sid: pointer.Get("sid-1")},
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.NotEmpty(resp.MessageId)
	s.Equal(messagehubpb.SendState_SEND_STATE_SENT, resp.State)
}

func (s *smsLogicTestSuite) TestSend_Success_UsePlatformSender() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:    "hello",
		Recipient:  &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		RegionCode: "US",
	}
	platformSender := &db.PlatformSender{Identity: "+**********", Provider: messagehubutils.MessageProviderAWS, Weight: 1}
	s.mockDB.EXPECT().ListPlatformSenders(ctx, "US").Return([]*db.PlatformSender{platformSender}, nil)
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockAwsProvider.EXPECT().SendSMS(ctx, gomock.Any()).Return(&db.MessageState{
		ParsedExtra: &db.SmsStateDetails{Sid: pointer.Get("sid-x")},
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.NotEmpty(resp.MessageId)
}

func (s *smsLogicTestSuite) TestSend_Success_WithKafkaCallback() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:    "hello",
		Recipient:  &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:     &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		RegionCode: "US",
		StateCallback: &messagehubpb.Callback{
			Method: &messagehubpb.Callback_Kafka{
				Kafka: &messagehubpb.KafkaCallback{
					Topic: "test-topic",
				},
			},
		},
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockTwilioProvider.EXPECT().SendSMS(gomock.Any(), gomock.Any()).Return(&db.MessageState{
		ParsedExtra: &db.SmsStateDetails{Sid: pointer.Get("sid-kafka")},
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.NotEmpty(resp.MessageId)
}

func (s *smsLogicTestSuite) TestSend_Success_WithGrpcCallback() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:    "hello",
		Recipient:  &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:     &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		RegionCode: "US",
		StateCallback: &messagehubpb.Callback{
			Method: &messagehubpb.Callback_Grpc{
				Grpc: &messagehubpb.GrpcCallback{
					Server: "test-server",
				},
			},
		},
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockTwilioProvider.EXPECT().SendSMS(gomock.Any(), gomock.Any()).Return(&db.MessageState{
		ParsedExtra: &db.SmsStateDetails{Sid: pointer.Get("sid-grpc")},
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.NotEmpty(resp.MessageId)
}

func (s *smsLogicTestSuite) TestSend_Failure_BuildMessageError() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Recipient:  &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		RegionCode: "US",
	}
	s.mockDB.EXPECT().ListPlatformSenders(ctx, "US").Return(nil, errors.New("db error"))

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
}

func (s *smsLogicTestSuite) TestSend_Failure_CreateMessageError() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:    "hello",
		Recipient:  &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:     &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		RegionCode: "US",
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(errors.New("db error"))

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
}

func (s *smsLogicTestSuite) TestSend_Failure_ProviderSendSMSError() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:   "hello",
		Recipient: &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:    &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockTwilioProvider.EXPECT().SendSMS(gomock.Any(), gomock.Any()).Return(&db.MessageState{
		State:        pointer.Get(messagehubutils.AWSMessageStateFailed),
		ErrorCode:    pointer.Get(messagehubutils.AWSMessageStateFailed),
		ErrorMessage: pointer.Get("provider error"),
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Equal(messagehubpb.SendState_SEND_STATE_FAILED, resp.State)
	s.Equal(pointer.Get(messagehubutils.AWSMessageStateFailed), resp.ErrorCode)
	s.Equal(pointer.Get("provider error"), resp.ErrorMessage)
}

func (s *smsLogicTestSuite) TestSend_Failure_CreateMessageStateError() {
	// --- Arrange ---
	ctx := context.Background()
	payload := &messagehubpb.SendSmsPayload{
		Content:   "hello",
		Recipient: &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
		Sender:    &phone_number.PhoneNumber{Kind: &phone_number.PhoneNumber_E164Number{E164Number: "+**********"}},
	}
	s.mockDB.EXPECT().CreateMessage(ctx, gomock.Any()).Return(nil)
	s.mockTwilioProvider.EXPECT().SendSMS(gomock.Any(), gomock.Any()).Return(&db.MessageState{
		ParsedExtra: &db.SmsStateDetails{Sid: pointer.Get("sid-4")},
	})
	s.mockDB.EXPECT().CreateMessageState(ctx, gomock.Any()).Return(errors.New("state error"))

	// --- Act ---
	resp, err := s.logic.Send(ctx, payload)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
}

func (s *smsLogicTestSuite) TestHandleAwsMessagingStatusCallback_Success() {
	// --- Arrange ---
	ctx := context.Background()
	req := &messagehubpb.HandleAwsMessagingStatusCallbackRequest{
		MessageStatus: messagehubutils.AWSMessageStateDelivered,
		Context:       &messagehubpb.AwsMessagingStatusCallbackContext{Id: "msg-aws-1"},
	}
	s.mockDB.EXPECT().UpdateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	err := s.logic.HandleAwsMessagingStatusCallback(ctx, req)

	// --- Assert ---
	s.NoError(err)
}

func (s *smsLogicTestSuite) TestHandleAwsMessagingStatusCallback_Failure_UpdateMessageStateError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &messagehubpb.HandleAwsMessagingStatusCallbackRequest{
		MessageStatus: messagehubutils.AWSMessageStateDelivered,
		Context:       &messagehubpb.AwsMessagingStatusCallbackContext{Id: "msg-aws-2"},
	}
	s.mockDB.EXPECT().UpdateMessageState(ctx, gomock.Any()).Return(errors.New("db error"))

	// --- Act ---
	err := s.logic.HandleAwsMessagingStatusCallback(ctx, req)

	// --- Assert ---
	s.Error(err)
}

func (s *smsLogicTestSuite) TestHandleTwilioSmsStatusCallbackV2_Success() {
	// --- Arrange ---
	ctx := context.Background()
	req := &messagehubpb.HandleTwilioSmsStatusCallbackRequest{
		MessageStatus: messagehubutils.TwilioMessageStateDelivered,
		Custom:        &messagehubpb.CustomTwilioCallbackData{Id: "msg-5"},
	}
	s.mockDB.EXPECT().UpdateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	err := s.logic.HandleTwilioSmsStatusCallbackV2(ctx, req)

	// --- Assert ---
	s.NoError(err)
}

func (s *smsLogicTestSuite) TestHandleTwilioSmsStatusCallbackV2_Success_WithErrorCode() {
	// --- Arrange ---
	ctx := context.Background()
	req := &messagehubpb.HandleTwilioSmsStatusCallbackRequest{
		MessageStatus: messagehubutils.TwilioMessageStateFailed,
		Custom:        &messagehubpb.CustomTwilioCallbackData{Id: "msg-6"},
		ErrorCode:     pointer.Get("123"),
	}
	s.mockDB.EXPECT().UpdateMessageState(ctx, gomock.Any()).Return(nil)

	// --- Act ---
	err := s.logic.HandleTwilioSmsStatusCallbackV2(ctx, req)

	// --- Assert ---
	s.NoError(err)
}

func (s *smsLogicTestSuite) TestHandleTwilioSmsStatusCallbackV2_Failure_UpdateMessageStateError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &messagehubpb.HandleTwilioSmsStatusCallbackRequest{
		MessageStatus: messagehubutils.TwilioMessageStateDelivered,
		Custom:        &messagehubpb.CustomTwilioCallbackData{Id: "msg-5"},
	}
	s.mockDB.EXPECT().UpdateMessageState(ctx, gomock.Any()).Return(errors.New("db error"))

	// --- Act ---
	err := s.logic.HandleTwilioSmsStatusCallbackV2(ctx, req)

	// --- Assert ---
	s.Error(err)
}
