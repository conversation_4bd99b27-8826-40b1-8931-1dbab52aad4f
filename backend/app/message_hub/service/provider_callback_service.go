package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/logic"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

// ProviderCallbackService is the server API for ProviderCallbackService service.
type ProviderCallbackService struct {
	messagehubpb.UnimplementedProviderCallbackServiceServer
	smsLogic *logic.SmsLogic
}

// NewProviderCallbackService creates a new ProviderCallbackService.
func NewProviderCallbackService() *ProviderCallbackService {
	return &ProviderCallbackService{
		smsLogic: logic.NewSmsLogic(),
	}
}

// HandleAwsMessagingStatusCallback implements messagehubpb.ProviderCallbackServiceServer.
func (s *ProviderCallbackService) HandleAwsMessagingStatusCallback(
	ctx context.Context, req *messagehubpb.HandleAwsMessagingStatusCallbackRequest,
) (*messagehubpb.HandleAwsMessagingStatusCallbackResponse, error) {
	if err := s.smsLogic.HandleAwsMessagingStatusCallback(ctx, req); err != nil {
		return nil, err
	}

	return &messagehubpb.HandleAwsMessagingStatusCallbackResponse{}, nil
}

func (s *ProviderCallbackService) HandleTwilioSmsStatusCallback(
	ctx context.Context, req *messagehubpb.HandleTwilioSmsStatusCallbackRequest,
) (*messagehubpb.HandleTwilioSmsStatusCallbackResponse, error) {
	if err := s.smsLogic.HandleTwilioSmsStatusCallbackV2(ctx, req); err != nil {
		return nil, err
	}

	return &messagehubpb.HandleTwilioSmsStatusCallbackResponse{}, nil
}
