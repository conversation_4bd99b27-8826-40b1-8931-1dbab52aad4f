package document_test

import (
	"bytes"
	"context"
	"errors"
	"io"
	"testing"

	"github.com/bytedance/sonic"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/search/logic/document"
	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch/mock"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

func convertStr(str string) *string {
	return &str
}

func NewMockLogic(os opensearch.OpenSearch) *document.Logic {
	return document.NewMock(os)
}

func TestNew(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	os := mock.NewMockOpenSearch(ctrl)
	opensearch.SetGlobalOpenSearch(os)
	logic := document.New()
	t.Log(logic)
}

func TestBulk(t *testing.T) {
	t.Run("Bulk success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("2"),
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test2"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_DELETE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("3"),
					},
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)
		os.EXPECT().
			Bulk(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *opensearchapi.BulkRequest) (*opensearch.BulkResponse, error) {
				// 1. 读取请求体
				body, err := io.ReadAll(request.Body)
				require.NoError(t, err)

				// 2. 将请求体按换行符分割成单独的操作
				lines := make([][]byte, 0)
				for i, line := range bytes.Split(body, []byte("\n")) {
					if len(line) > 0 {
						t.Logf("line %d: %s", i, string(line))
						lines = append(lines, line)
					}
				}
				require.Equal(t, 5, len(lines)) // 3个操作，每个操作2行（元数据+数据），最后一个空行

				// 3. 验证每个操作的元数据
				// 第一个操作：index
				var indexMeta map[string]any
				require.NoError(t, sonic.Unmarshal(lines[0], &indexMeta))
				indexOp := indexMeta["index"].(map[string]any)
				require.Equal(t, "test-index", indexOp["_index"])

				// 第二个操作：update
				var updateMeta map[string]any
				require.NoError(t, sonic.Unmarshal(lines[2], &updateMeta))
				updateOp := updateMeta["update"].(map[string]any)
				require.Equal(t, "test-index", updateOp["_index"])
				require.Equal(t, "2", updateOp["_id"])

				// 第三个操作：delete
				var deleteMeta map[string]any
				require.NoError(t, sonic.Unmarshal(lines[4], &deleteMeta))
				deleteOp := deleteMeta["delete"].(map[string]any)
				require.Equal(t, "test-index", deleteOp["_index"])
				require.Equal(t, "3", deleteOp["_id"])

				// 4. 验证操作数据
				// index 操作的数据
				var indexData map[string]any
				require.NoError(t, sonic.Unmarshal(lines[1], &indexData))
				require.Equal(t, "test1", indexData["name"])

				// update 操作的数据
				var updateData map[string]any
				require.NoError(t, sonic.Unmarshal(lines[3], &updateData))
				t.Logf("updateData: %v", updateData)
				updateDoc := updateData["doc"].(string)
				t.Logf("updateDoc: %s", updateDoc)
				var updateDocMap map[string]any
				require.NoError(t, sonic.Unmarshal([]byte(updateDoc), &updateDocMap))
				require.Equal(t, "test2", updateDocMap["name"])

				// delete 操作没有数据行
				return &opensearch.BulkResponse{
					Took:   100,
					Errors: false,
					Items: []map[string]*opensearch.BulkResponseItem{
						{
							"index": {
								Index:   "test-index",
								ID:      "1",
								Version: 1,
								Result:  "created",
								Status:  201,
								Shards: &opensearch.BulkResponseItemShards{
									Total:      1,
									Successful: 1,
									Failed:     0,
								},
							},
						},
						{
							"update": {
								Index:   "test-index",
								ID:      "2",
								Version: 2,
								Result:  "updated",
								Status:  200,
								Shards: &opensearch.BulkResponseItemShards{
									Total:      1,
									Successful: 1,
									Failed:     0,
								},
							},
						},
						{
							"delete": {
								Index:   "test-index",
								ID:      "3",
								Version: 1,
								Result:  "deleted",
								Status:  200,
								Shards: &opensearch.BulkResponseItemShards{
									Total:      1,
									Successful: 1,
									Failed:     0,
								},
							},
						},
					},
				}, nil
			})

		resp, err := logic.Bulk(ctx, req)

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, 3, len(resp.Results))
		require.Equal(t, searchpb.OperationType_INDEX, resp.Results[0].OperationType)
		require.Equal(t, int32(201), resp.Results[0].Status)
		require.Equal(t, searchpb.OperationType_UPDATE, resp.Results[1].OperationType)
		require.Equal(t, int32(200), resp.Results[1].Status)
		require.Equal(t, searchpb.OperationType_DELETE, resp.Results[2].OperationType)
		require.Equal(t, int32(200), resp.Results[2].Status)
	})

	t.Run("Bulk opensearch error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("2"),
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test2"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_DELETE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("3"),
					},
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)
		os.EXPECT().
			Bulk(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("opensearch error"))

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "opensearch error")
	})
	t.Run("Bulk with errors", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						// 缺少ID,应该报错
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test2"),
						},
					},
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is UPDATE but id is nil")
	})

	t.Run("Bulk with partial success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					},
				},
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("2"),
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"invalid": structpb.NewStringValue("json"),
						},
					},
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		os.EXPECT().
			Bulk(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *opensearchapi.BulkRequest) (*opensearch.BulkResponse, error) {
				// 1. 读取请求体
				body, err := io.ReadAll(request.Body)
				require.NoError(t, err)

				// 2. 将请求体按换行符分割成单独的操作
				lines := make([][]byte, 0)
				for i, line := range bytes.Split(body, []byte("\n")) {
					if len(line) > 0 {
						t.Logf("line %d: %s", i, string(line))
						lines = append(lines, line)
					}
				}
				require.Equal(t, 4, len(lines)) // 2个操作，每个操作2行（元数据+数据），最后一个空行

				// 3. 验证每个操作的元数据
				// 第一个操作：index
				var indexMeta map[string]any
				require.NoError(t, sonic.Unmarshal(lines[0], &indexMeta))
				indexOp := indexMeta["index"].(map[string]any)
				require.Equal(t, "test-index", indexOp["_index"])

				// 第二个操作：update
				var updateMeta map[string]any
				require.NoError(t, sonic.Unmarshal(lines[2], &updateMeta))
				updateOp := updateMeta["update"].(map[string]any)
				require.Equal(t, "test-index", updateOp["_index"])
				require.Equal(t, "2", updateOp["_id"])

				// 4. 验证操作数据
				// index 操作的数据
				var indexData map[string]any
				require.NoError(t, sonic.Unmarshal(lines[1], &indexData))
				require.Equal(t, "test1", indexData["name"])

				// update 操作的数据包含无效的JSON
				var updateData map[string]any
				require.NoError(t, sonic.Unmarshal(lines[3], &updateData))
				updateDoc := updateData["doc"].(string)
				var updateDocMap map[string]any
				require.NoError(t, sonic.Unmarshal([]byte(updateDoc), &updateDocMap))
				require.Equal(t, "json", updateDocMap["invalid"])

				return &opensearch.BulkResponse{
					Took:   100,
					Errors: true,
					Items: []map[string]*opensearch.BulkResponseItem{
						{
							"index": {
								Index:   "test-index",
								ID:      "1",
								Version: 1,
								Result:  "created",
								Status:  201,
								Shards: &opensearch.BulkResponseItemShards{
									Total:      1,
									Successful: 1,
									Failed:     0,
								},
							},
						},
						{
							"update": {
								Index:  "test-index",
								ID:     "2",
								Status: 400,
								Result: "error",
								Error: &opensearch.BulkResponseItemError{
									Type:   "mapper_parsing_exception",
									Reason: "failed to parse",
								},
								Shards: &opensearch.BulkResponseItemShards{
									Total:      1,
									Successful: 0,
									Failed:     1,
								},
							},
						},
					},
				}, nil
			})

		resp, err := logic.Bulk(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.True(t, resp.HasErrors)
		require.Equal(t, 2, len(resp.Results))

		// 验证成功的操作
		require.Equal(t, searchpb.OperationType_INDEX, resp.Results[0].OperationType)
		require.Equal(t, int32(201), resp.Results[0].Status)
		require.Nil(t, resp.Results[0].Error)

		// 验证失败的操作
		require.Equal(t, searchpb.OperationType_UPDATE, resp.Results[1].OperationType)
		require.Equal(t, int32(400), resp.Results[1].Status)
		require.NotNil(t, resp.Results[1].Error)
		require.Equal(t, "mapper_parsing_exception", resp.Results[1].Error.ErrorType)
		require.Equal(t, "failed to parse", resp.Results[1].Error.ErrorReason)
	})

	t.Run("Bulk with errors - invalid operation", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						// 缺少必需的ID字段
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					},
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is UPDATE but id is nil")
	})

	t.Run("Bulk with errors - delete with document", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_DELETE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("1"),
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"name": structpb.NewStringValue("test1"),
						},
					}, // DELETE操作不应该有文档
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is delete but document is not nil")
	})

	t.Run("Bulk with errors - missing document", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					// 缺少必需的文档
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is INDEX but document is nil")
	})

	t.Run("Bulk with errors - update not document", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("1"),
					},
					// 缺少必需的文档
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is UPDATE but document is nil")
	})

	t.Run("Bulk with errors - no index", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_UPDATE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "",
						Id:    convertStr("1"),
					},
					// 缺少必需的文档
				},
			},
		}

		os := mock.NewMockOpenSearch(ctrl)
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "operation is nil or target is nil or index is empty")
	})

	t.Run("Bulk with errors - invalid document", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_INDEX,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
					},
					Document: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"invalid": structpb.NewStringValue("json"),
						},
					},
				},
			},
		}
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Bulk(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("invalid document format")).
			AnyTimes()
		logic := NewMockLogic(os)

		resp, err := logic.Bulk(ctx, req)
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "invalid document")
	})

}

func TestConvert(t *testing.T) {
	t.Run("delete no document", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		os := mock.NewMockOpenSearch(ctrl)

		os.EXPECT().
			Bulk(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("invalid document format")).
			AnyTimes()
		c := &document.Converter{}
		req := &searchpb.BulkDocumentRequest{
			Operations: []*searchpb.BulkDocumentRequest_BulkOperation{
				{
					OperationType: searchpb.OperationType_DELETE,
					Target: &searchpb.BulkDocumentRequest_BulkOperation_BulkTarget{
						Index: "test-index",
						Id:    convertStr("1"),
					},
					Document: nil,
				},
			},
		}
		bulkreq, err := c.ConvertBulkRequest(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, bulkreq)
	})
}
