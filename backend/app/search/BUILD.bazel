load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "search_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/search/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/search/v1:search",
    ],
)

go_binary(
    name = "search",
    embed = [":search_lib"],
    visibility = ["//visibility:public"],
)
