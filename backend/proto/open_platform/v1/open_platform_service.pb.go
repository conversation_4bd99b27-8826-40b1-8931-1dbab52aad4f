// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用company_id作为标识符 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: 使用empty返回 --)
// (-- api-linter: core::0140::uri=disabled
//     aip.dev/not-precedent: 使用url --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用customers --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/open_platform/v1/open_platform_service.proto

package open_platformpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 114700
	// 授权码未找到
	ErrCode_ERR_CODE_OAUTH_NOT_FOUND ErrCode = 114701
	// 授权码失效
	ErrCode_ERR_CODE_OAUTH_EXPIRED ErrCode = 114702
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		114700: "ERR_CODE_UNSPECIFIED",
		114701: "ERR_CODE_OAUTH_NOT_FOUND",
		114702: "ERR_CODE_OAUTH_EXPIRED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":              0,
		"ERR_CODE_UNSPECIFIED":     114700,
		"ERR_CODE_OAUTH_NOT_FOUND": 114701,
		"ERR_CODE_OAUTH_EXPIRED":   114702,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{0}
}

// Link类型
type GetLinkSettingRequest_LinkType int32

const (
	// 未指定
	GetLinkSettingRequest_LINK_TYPE_UNSPECIFIED GetLinkSettingRequest_LinkType = 0
	// Google Ads OAuth2
	GetLinkSettingRequest_LINK_TYPE_GOOGLE_ADS_OAUTH2 GetLinkSettingRequest_LinkType = 1
	// Meta Ads OAuth2
	GetLinkSettingRequest_LINK_TYPE_META_ADS_OAUTH2 GetLinkSettingRequest_LinkType = 2
)

// Enum value maps for GetLinkSettingRequest_LinkType.
var (
	GetLinkSettingRequest_LinkType_name = map[int32]string{
		0: "LINK_TYPE_UNSPECIFIED",
		1: "LINK_TYPE_GOOGLE_ADS_OAUTH2",
		2: "LINK_TYPE_META_ADS_OAUTH2",
	}
	GetLinkSettingRequest_LinkType_value = map[string]int32{
		"LINK_TYPE_UNSPECIFIED":       0,
		"LINK_TYPE_GOOGLE_ADS_OAUTH2": 1,
		"LINK_TYPE_META_ADS_OAUTH2":   2,
	}
)

func (x GetLinkSettingRequest_LinkType) Enum() *GetLinkSettingRequest_LinkType {
	p := new(GetLinkSettingRequest_LinkType)
	*p = x
	return p
}

func (x GetLinkSettingRequest_LinkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetLinkSettingRequest_LinkType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes[1].Descriptor()
}

func (GetLinkSettingRequest_LinkType) Type() protoreflect.EnumType {
	return &file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes[1]
}

func (x GetLinkSettingRequest_LinkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetLinkSettingRequest_LinkType.Descriptor instead.
func (GetLinkSettingRequest_LinkType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{12, 0}
}

// GetGoogleAdsUserInfoRequest 获取授权用户相关信息请求
type GetGoogleAdsUserInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGoogleAdsUserInfoRequest) Reset() {
	*x = GetGoogleAdsUserInfoRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGoogleAdsUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoogleAdsUserInfoRequest) ProtoMessage() {}

func (x *GetGoogleAdsUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoogleAdsUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetGoogleAdsUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetGoogleAdsUserInfoRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetGoogleAdsUserInfoResponse 获取授权用户相关信息响应
type GetGoogleAdsUserInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// google user info
	UserInfo *GoogleOAuthUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	// google ads setting
	AdsSetting    *GoogleAdsSetting `protobuf:"bytes,2,opt,name=ads_setting,json=adsSetting,proto3" json:"ads_setting,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGoogleAdsUserInfoResponse) Reset() {
	*x = GetGoogleAdsUserInfoResponse{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGoogleAdsUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoogleAdsUserInfoResponse) ProtoMessage() {}

func (x *GetGoogleAdsUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoogleAdsUserInfoResponse.ProtoReflect.Descriptor instead.
func (*GetGoogleAdsUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetGoogleAdsUserInfoResponse) GetUserInfo() *GoogleOAuthUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetGoogleAdsUserInfoResponse) GetAdsSetting() *GoogleAdsSetting {
	if x != nil {
		return x.AdsSetting
	}
	return nil
}

// RevokeGoogleAdsOAuthRequest 解除授权请求
type RevokeGoogleAdsOAuthRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staffID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeGoogleAdsOAuthRequest) Reset() {
	*x = RevokeGoogleAdsOAuthRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeGoogleAdsOAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeGoogleAdsOAuthRequest) ProtoMessage() {}

func (x *RevokeGoogleAdsOAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeGoogleAdsOAuthRequest.ProtoReflect.Descriptor instead.
func (*RevokeGoogleAdsOAuthRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{2}
}

func (x *RevokeGoogleAdsOAuthRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RevokeGoogleAdsOAuthRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// ListGoogleAdsAccountsRequest 获取授权用户Ads账号列表请求
type ListGoogleAdsAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGoogleAdsAccountsRequest) Reset() {
	*x = ListGoogleAdsAccountsRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGoogleAdsAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGoogleAdsAccountsRequest) ProtoMessage() {}

func (x *ListGoogleAdsAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGoogleAdsAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListGoogleAdsAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListGoogleAdsAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// ListGoogleAdsAccountsResponse 获取授权用户Ads账号列表响应
type ListGoogleAdsAccountsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// GoogleAdsCustomer
	Customers     []*GoogleAdsCustomer `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGoogleAdsAccountsResponse) Reset() {
	*x = ListGoogleAdsAccountsResponse{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGoogleAdsAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGoogleAdsAccountsResponse) ProtoMessage() {}

func (x *ListGoogleAdsAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGoogleAdsAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListGoogleAdsAccountsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListGoogleAdsAccountsResponse) GetCustomers() []*GoogleAdsCustomer {
	if x != nil {
		return x.Customers
	}
	return nil
}

// LinkGoogleAdsAccountsRequest 绑定用户Ads账号请求
type LinkGoogleAdsAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staffID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// businessID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// google ads customer ids
	GoogleAdsCustomerIds []int64 `protobuf:"varint,4,rep,packed,name=google_ads_customer_ids,json=googleAdsCustomerIds,proto3" json:"google_ads_customer_ids,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *LinkGoogleAdsAccountsRequest) Reset() {
	*x = LinkGoogleAdsAccountsRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkGoogleAdsAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkGoogleAdsAccountsRequest) ProtoMessage() {}

func (x *LinkGoogleAdsAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkGoogleAdsAccountsRequest.ProtoReflect.Descriptor instead.
func (*LinkGoogleAdsAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{5}
}

func (x *LinkGoogleAdsAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LinkGoogleAdsAccountsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *LinkGoogleAdsAccountsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LinkGoogleAdsAccountsRequest) GetGoogleAdsCustomerIds() []int64 {
	if x != nil {
		return x.GoogleAdsCustomerIds
	}
	return nil
}

// GetMetaAdsUserInfoRequest 获取授权用户相关信息请求
type GetMetaAdsUserInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMetaAdsUserInfoRequest) Reset() {
	*x = GetMetaAdsUserInfoRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMetaAdsUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetaAdsUserInfoRequest) ProtoMessage() {}

func (x *GetMetaAdsUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetaAdsUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetMetaAdsUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetMetaAdsUserInfoRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetMetaAdsUserInfoResponse 获取授权用户相关信息响应
type GetMetaAdsUserInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// meta user info
	UserInfo *MetaOAuthUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	// meta ads setting
	AdsSetting    *MetaAdsSetting `protobuf:"bytes,2,opt,name=ads_setting,json=adsSetting,proto3" json:"ads_setting,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMetaAdsUserInfoResponse) Reset() {
	*x = GetMetaAdsUserInfoResponse{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMetaAdsUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetaAdsUserInfoResponse) ProtoMessage() {}

func (x *GetMetaAdsUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetaAdsUserInfoResponse.ProtoReflect.Descriptor instead.
func (*GetMetaAdsUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetMetaAdsUserInfoResponse) GetUserInfo() *MetaOAuthUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetMetaAdsUserInfoResponse) GetAdsSetting() *MetaAdsSetting {
	if x != nil {
		return x.AdsSetting
	}
	return nil
}

// RevokeMetaAdsOAuthRequest 解除授权请求
type RevokeMetaAdsOAuthRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staffID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMetaAdsOAuthRequest) Reset() {
	*x = RevokeMetaAdsOAuthRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMetaAdsOAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMetaAdsOAuthRequest) ProtoMessage() {}

func (x *RevokeMetaAdsOAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMetaAdsOAuthRequest.ProtoReflect.Descriptor instead.
func (*RevokeMetaAdsOAuthRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{8}
}

func (x *RevokeMetaAdsOAuthRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RevokeMetaAdsOAuthRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// ListMetaAdsAccountsRequest 获取授权用户Ads账号列表请求
type ListMetaAdsAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetaAdsAccountsRequest) Reset() {
	*x = ListMetaAdsAccountsRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetaAdsAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetaAdsAccountsRequest) ProtoMessage() {}

func (x *ListMetaAdsAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetaAdsAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListMetaAdsAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListMetaAdsAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// ListMetaAdsAccountsResponse 获取授权用户Ads账号列表响应
type ListMetaAdsAccountsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MetaAdsAccount
	Accounts      []*MetaAdsAccount `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetaAdsAccountsResponse) Reset() {
	*x = ListMetaAdsAccountsResponse{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetaAdsAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetaAdsAccountsResponse) ProtoMessage() {}

func (x *ListMetaAdsAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetaAdsAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListMetaAdsAccountsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListMetaAdsAccountsResponse) GetAccounts() []*MetaAdsAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

// LinkMetaAdsAccountsRequest 绑定用户Ads账号请求
type LinkMetaAdsAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staffID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// businessID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// meta ads account ids
	MetaAdsAccountIds []int64 `protobuf:"varint,4,rep,packed,name=meta_ads_account_ids,json=metaAdsAccountIds,proto3" json:"meta_ads_account_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LinkMetaAdsAccountsRequest) Reset() {
	*x = LinkMetaAdsAccountsRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkMetaAdsAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkMetaAdsAccountsRequest) ProtoMessage() {}

func (x *LinkMetaAdsAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkMetaAdsAccountsRequest.ProtoReflect.Descriptor instead.
func (*LinkMetaAdsAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{11}
}

func (x *LinkMetaAdsAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LinkMetaAdsAccountsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *LinkMetaAdsAccountsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LinkMetaAdsAccountsRequest) GetMetaAdsAccountIds() []int64 {
	if x != nil {
		return x.MetaAdsAccountIds
	}
	return nil
}

// GetLinkSettingRequest 获取Link配置请求
type GetLinkSettingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Link类型
	Type          GetLinkSettingRequest_LinkType `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.open_platform.v1.GetLinkSettingRequest_LinkType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLinkSettingRequest) Reset() {
	*x = GetLinkSettingRequest{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLinkSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkSettingRequest) ProtoMessage() {}

func (x *GetLinkSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkSettingRequest.ProtoReflect.Descriptor instead.
func (*GetLinkSettingRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetLinkSettingRequest) GetType() GetLinkSettingRequest_LinkType {
	if x != nil {
		return x.Type
	}
	return GetLinkSettingRequest_LINK_TYPE_UNSPECIFIED
}

// GetLinkSettingResponse 获取Link配置响应
type GetLinkSettingResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// link
	Link          string `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLinkSettingResponse) Reset() {
	*x = GetLinkSettingResponse{}
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLinkSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkSettingResponse) ProtoMessage() {}

func (x *GetLinkSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkSettingResponse.ProtoReflect.Descriptor instead.
func (*GetLinkSettingResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetLinkSettingResponse) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

var File_backend_proto_open_platform_v1_open_platform_service_proto protoreflect.FileDescriptor

const file_backend_proto_open_platform_v1_open_platform_service_proto_rawDesc = "" +
	"\n" +
	":backend/proto/open_platform/v1/open_platform_service.proto\x12\x1ebackend.proto.open_platform.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a2backend/proto/open_platform/v1/open_platform.proto\"<\n" +
	"\x1bGetGoogleAdsUserInfoRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"\xc3\x01\n" +
	"\x1cGetGoogleAdsUserInfoResponse\x12P\n" +
	"\tuser_info\x18\x01 \x01(\v23.backend.proto.open_platform.v1.GoogleOAuthUserInfoR\buserInfo\x12Q\n" +
	"\vads_setting\x18\x02 \x01(\v20.backend.proto.open_platform.v1.GoogleAdsSettingR\n" +
	"adsSetting\"W\n" +
	"\x1bRevokeGoogleAdsOAuthRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"=\n" +
	"\x1cListGoogleAdsAccountsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"p\n" +
	"\x1dListGoogleAdsAccountsResponse\x12O\n" +
	"\tcustomers\x18\x01 \x03(\v21.backend.proto.open_platform.v1.GoogleAdsCustomerR\tcustomers\"\xb0\x01\n" +
	"\x1cLinkGoogleAdsAccountsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x125\n" +
	"\x17google_ads_customer_ids\x18\x04 \x03(\x03R\x14googleAdsCustomerIds\":\n" +
	"\x19GetMetaAdsUserInfoRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"\xbd\x01\n" +
	"\x1aGetMetaAdsUserInfoResponse\x12N\n" +
	"\tuser_info\x18\x01 \x01(\v21.backend.proto.open_platform.v1.MetaOAuthUserInfoR\buserInfo\x12O\n" +
	"\vads_setting\x18\x02 \x01(\v2..backend.proto.open_platform.v1.MetaAdsSettingR\n" +
	"adsSetting\"U\n" +
	"\x19RevokeMetaAdsOAuthRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\";\n" +
	"\x1aListMetaAdsAccountsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"i\n" +
	"\x1bListMetaAdsAccountsResponse\x12J\n" +
	"\baccounts\x18\x01 \x03(\v2..backend.proto.open_platform.v1.MetaAdsAccountR\baccounts\"\xa8\x01\n" +
	"\x1aLinkMetaAdsAccountsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12/\n" +
	"\x14meta_ads_account_ids\x18\x04 \x03(\x03R\x11metaAdsAccountIds\"\xd2\x01\n" +
	"\x15GetLinkSettingRequest\x12R\n" +
	"\x04type\x18\x01 \x01(\x0e2>.backend.proto.open_platform.v1.GetLinkSettingRequest.LinkTypeR\x04type\"e\n" +
	"\bLinkType\x12\x19\n" +
	"\x15LINK_TYPE_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bLINK_TYPE_GOOGLE_ADS_OAUTH2\x10\x01\x12\x1d\n" +
	"\x19LINK_TYPE_META_ADS_OAUTH2\x10\x02\",\n" +
	"\x16GetLinkSettingResponse\x12\x12\n" +
	"\x04link\x18\x01 \x01(\tR\x04link*t\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\x8c\x80\a\x12\x1e\n" +
	"\x18ERR_CODE_OAUTH_NOT_FOUND\x10\x8d\x80\a\x12\x1c\n" +
	"\x16ERR_CODE_OAUTH_EXPIRED\x10\x8e\x80\a2\x90\t\n" +
	"\x13OpenPlatformService\x12\x91\x01\n" +
	"\x14GetGoogleAdsUserInfo\x12;.backend.proto.open_platform.v1.GetGoogleAdsUserInfoRequest\x1a<.backend.proto.open_platform.v1.GetGoogleAdsUserInfoResponse\x12k\n" +
	"\x14RevokeGoogleAdsOAuth\x12;.backend.proto.open_platform.v1.RevokeGoogleAdsOAuthRequest\x1a\x16.google.protobuf.Empty\x12\x94\x01\n" +
	"\x15ListGoogleAdsAccounts\x12<.backend.proto.open_platform.v1.ListGoogleAdsAccountsRequest\x1a=.backend.proto.open_platform.v1.ListGoogleAdsAccountsResponse\x12m\n" +
	"\x15LinkGoogleAdsAccounts\x12<.backend.proto.open_platform.v1.LinkGoogleAdsAccountsRequest\x1a\x16.google.protobuf.Empty\x12\x8b\x01\n" +
	"\x12GetMetaAdsUserInfo\x129.backend.proto.open_platform.v1.GetMetaAdsUserInfoRequest\x1a:.backend.proto.open_platform.v1.GetMetaAdsUserInfoResponse\x12g\n" +
	"\x12RevokeMetaAdsOAuth\x129.backend.proto.open_platform.v1.RevokeMetaAdsOAuthRequest\x1a\x16.google.protobuf.Empty\x12\x8e\x01\n" +
	"\x13ListMetaAdsAccounts\x12:.backend.proto.open_platform.v1.ListMetaAdsAccountsRequest\x1a;.backend.proto.open_platform.v1.ListMetaAdsAccountsResponse\x12i\n" +
	"\x13LinkMetaAdsAccounts\x12:.backend.proto.open_platform.v1.LinkMetaAdsAccountsRequest\x1a\x16.google.protobuf.Empty\x12\x7f\n" +
	"\x0eGetLinkSetting\x125.backend.proto.open_platform.v1.GetLinkSettingRequest\x1a6.backend.proto.open_platform.v1.GetLinkSettingResponseBz\n" +
	"(com.moego.backend.proto.open_platform.v1P\x01ZLgithub.com/MoeGolibrary/moego/backend/proto/open_platform/v1;open_platformpbb\x06proto3"

var (
	file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescOnce sync.Once
	file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescData []byte
)

func file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescGZIP() []byte {
	file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_open_platform_v1_open_platform_service_proto_rawDesc), len(file_backend_proto_open_platform_v1_open_platform_service_proto_rawDesc)))
	})
	return file_backend_proto_open_platform_v1_open_platform_service_proto_rawDescData
}

var file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_open_platform_v1_open_platform_service_proto_goTypes = []any{
	(ErrCode)(0),                          // 0: backend.proto.open_platform.v1.ErrCode
	(GetLinkSettingRequest_LinkType)(0),   // 1: backend.proto.open_platform.v1.GetLinkSettingRequest.LinkType
	(*GetGoogleAdsUserInfoRequest)(nil),   // 2: backend.proto.open_platform.v1.GetGoogleAdsUserInfoRequest
	(*GetGoogleAdsUserInfoResponse)(nil),  // 3: backend.proto.open_platform.v1.GetGoogleAdsUserInfoResponse
	(*RevokeGoogleAdsOAuthRequest)(nil),   // 4: backend.proto.open_platform.v1.RevokeGoogleAdsOAuthRequest
	(*ListGoogleAdsAccountsRequest)(nil),  // 5: backend.proto.open_platform.v1.ListGoogleAdsAccountsRequest
	(*ListGoogleAdsAccountsResponse)(nil), // 6: backend.proto.open_platform.v1.ListGoogleAdsAccountsResponse
	(*LinkGoogleAdsAccountsRequest)(nil),  // 7: backend.proto.open_platform.v1.LinkGoogleAdsAccountsRequest
	(*GetMetaAdsUserInfoRequest)(nil),     // 8: backend.proto.open_platform.v1.GetMetaAdsUserInfoRequest
	(*GetMetaAdsUserInfoResponse)(nil),    // 9: backend.proto.open_platform.v1.GetMetaAdsUserInfoResponse
	(*RevokeMetaAdsOAuthRequest)(nil),     // 10: backend.proto.open_platform.v1.RevokeMetaAdsOAuthRequest
	(*ListMetaAdsAccountsRequest)(nil),    // 11: backend.proto.open_platform.v1.ListMetaAdsAccountsRequest
	(*ListMetaAdsAccountsResponse)(nil),   // 12: backend.proto.open_platform.v1.ListMetaAdsAccountsResponse
	(*LinkMetaAdsAccountsRequest)(nil),    // 13: backend.proto.open_platform.v1.LinkMetaAdsAccountsRequest
	(*GetLinkSettingRequest)(nil),         // 14: backend.proto.open_platform.v1.GetLinkSettingRequest
	(*GetLinkSettingResponse)(nil),        // 15: backend.proto.open_platform.v1.GetLinkSettingResponse
	(*GoogleOAuthUserInfo)(nil),           // 16: backend.proto.open_platform.v1.GoogleOAuthUserInfo
	(*GoogleAdsSetting)(nil),              // 17: backend.proto.open_platform.v1.GoogleAdsSetting
	(*GoogleAdsCustomer)(nil),             // 18: backend.proto.open_platform.v1.GoogleAdsCustomer
	(*MetaOAuthUserInfo)(nil),             // 19: backend.proto.open_platform.v1.MetaOAuthUserInfo
	(*MetaAdsSetting)(nil),                // 20: backend.proto.open_platform.v1.MetaAdsSetting
	(*MetaAdsAccount)(nil),                // 21: backend.proto.open_platform.v1.MetaAdsAccount
	(*emptypb.Empty)(nil),                 // 22: google.protobuf.Empty
}
var file_backend_proto_open_platform_v1_open_platform_service_proto_depIdxs = []int32{
	16, // 0: backend.proto.open_platform.v1.GetGoogleAdsUserInfoResponse.user_info:type_name -> backend.proto.open_platform.v1.GoogleOAuthUserInfo
	17, // 1: backend.proto.open_platform.v1.GetGoogleAdsUserInfoResponse.ads_setting:type_name -> backend.proto.open_platform.v1.GoogleAdsSetting
	18, // 2: backend.proto.open_platform.v1.ListGoogleAdsAccountsResponse.customers:type_name -> backend.proto.open_platform.v1.GoogleAdsCustomer
	19, // 3: backend.proto.open_platform.v1.GetMetaAdsUserInfoResponse.user_info:type_name -> backend.proto.open_platform.v1.MetaOAuthUserInfo
	20, // 4: backend.proto.open_platform.v1.GetMetaAdsUserInfoResponse.ads_setting:type_name -> backend.proto.open_platform.v1.MetaAdsSetting
	21, // 5: backend.proto.open_platform.v1.ListMetaAdsAccountsResponse.accounts:type_name -> backend.proto.open_platform.v1.MetaAdsAccount
	1,  // 6: backend.proto.open_platform.v1.GetLinkSettingRequest.type:type_name -> backend.proto.open_platform.v1.GetLinkSettingRequest.LinkType
	2,  // 7: backend.proto.open_platform.v1.OpenPlatformService.GetGoogleAdsUserInfo:input_type -> backend.proto.open_platform.v1.GetGoogleAdsUserInfoRequest
	4,  // 8: backend.proto.open_platform.v1.OpenPlatformService.RevokeGoogleAdsOAuth:input_type -> backend.proto.open_platform.v1.RevokeGoogleAdsOAuthRequest
	5,  // 9: backend.proto.open_platform.v1.OpenPlatformService.ListGoogleAdsAccounts:input_type -> backend.proto.open_platform.v1.ListGoogleAdsAccountsRequest
	7,  // 10: backend.proto.open_platform.v1.OpenPlatformService.LinkGoogleAdsAccounts:input_type -> backend.proto.open_platform.v1.LinkGoogleAdsAccountsRequest
	8,  // 11: backend.proto.open_platform.v1.OpenPlatformService.GetMetaAdsUserInfo:input_type -> backend.proto.open_platform.v1.GetMetaAdsUserInfoRequest
	10, // 12: backend.proto.open_platform.v1.OpenPlatformService.RevokeMetaAdsOAuth:input_type -> backend.proto.open_platform.v1.RevokeMetaAdsOAuthRequest
	11, // 13: backend.proto.open_platform.v1.OpenPlatformService.ListMetaAdsAccounts:input_type -> backend.proto.open_platform.v1.ListMetaAdsAccountsRequest
	13, // 14: backend.proto.open_platform.v1.OpenPlatformService.LinkMetaAdsAccounts:input_type -> backend.proto.open_platform.v1.LinkMetaAdsAccountsRequest
	14, // 15: backend.proto.open_platform.v1.OpenPlatformService.GetLinkSetting:input_type -> backend.proto.open_platform.v1.GetLinkSettingRequest
	3,  // 16: backend.proto.open_platform.v1.OpenPlatformService.GetGoogleAdsUserInfo:output_type -> backend.proto.open_platform.v1.GetGoogleAdsUserInfoResponse
	22, // 17: backend.proto.open_platform.v1.OpenPlatformService.RevokeGoogleAdsOAuth:output_type -> google.protobuf.Empty
	6,  // 18: backend.proto.open_platform.v1.OpenPlatformService.ListGoogleAdsAccounts:output_type -> backend.proto.open_platform.v1.ListGoogleAdsAccountsResponse
	22, // 19: backend.proto.open_platform.v1.OpenPlatformService.LinkGoogleAdsAccounts:output_type -> google.protobuf.Empty
	9,  // 20: backend.proto.open_platform.v1.OpenPlatformService.GetMetaAdsUserInfo:output_type -> backend.proto.open_platform.v1.GetMetaAdsUserInfoResponse
	22, // 21: backend.proto.open_platform.v1.OpenPlatformService.RevokeMetaAdsOAuth:output_type -> google.protobuf.Empty
	12, // 22: backend.proto.open_platform.v1.OpenPlatformService.ListMetaAdsAccounts:output_type -> backend.proto.open_platform.v1.ListMetaAdsAccountsResponse
	22, // 23: backend.proto.open_platform.v1.OpenPlatformService.LinkMetaAdsAccounts:output_type -> google.protobuf.Empty
	15, // 24: backend.proto.open_platform.v1.OpenPlatformService.GetLinkSetting:output_type -> backend.proto.open_platform.v1.GetLinkSettingResponse
	16, // [16:25] is the sub-list for method output_type
	7,  // [7:16] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_open_platform_v1_open_platform_service_proto_init() }
func file_backend_proto_open_platform_v1_open_platform_service_proto_init() {
	if File_backend_proto_open_platform_v1_open_platform_service_proto != nil {
		return
	}
	file_backend_proto_open_platform_v1_open_platform_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_open_platform_v1_open_platform_service_proto_rawDesc), len(file_backend_proto_open_platform_v1_open_platform_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_open_platform_v1_open_platform_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_open_platform_v1_open_platform_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_open_platform_v1_open_platform_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_open_platform_v1_open_platform_service_proto_msgTypes,
	}.Build()
	File_backend_proto_open_platform_v1_open_platform_service_proto = out.File
	file_backend_proto_open_platform_v1_open_platform_service_proto_goTypes = nil
	file_backend_proto_open_platform_v1_open_platform_service_proto_depIdxs = nil
}
