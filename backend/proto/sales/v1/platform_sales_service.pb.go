// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/sales/v1/platform_sales_service.proto

package salespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SyncPlatformSalesRequest
type SyncPlatformSalesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sales code
	SalesCode string `protobuf:"bytes,1,opt,name=sales_code,json=salesCode,proto3" json:"sales_code,omitempty"`
	// term type, e.g. monthly, annual
	TermType string `protobuf:"bytes,2,opt,name=term_type,json=termType,proto3" json:"term_type,omitempty"`
	// subscription items
	// location count
	LocationCount int32 `protobuf:"varint,3,opt,name=location_count,json=locationCount,proto3" json:"location_count,omitempty"`
	// van count
	VanCount int32 `protobuf:"varint,4,opt,name=van_count,json=vanCount,proto3" json:"van_count,omitempty"`
	// hardware items
	// bbpos count
	BbposCount int32 `protobuf:"varint,5,opt,name=bbpos_count,json=bbposCount,proto3" json:"bbpos_count,omitempty"`
	// reader m2 count
	ReaderM2Count int32 `protobuf:"varint,6,opt,name=reader_m2_count,json=readerM2Count,proto3" json:"reader_m2_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncPlatformSalesRequest) Reset() {
	*x = SyncPlatformSalesRequest{}
	mi := &file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlatformSalesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlatformSalesRequest) ProtoMessage() {}

func (x *SyncPlatformSalesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlatformSalesRequest.ProtoReflect.Descriptor instead.
func (*SyncPlatformSalesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_platform_sales_service_proto_rawDescGZIP(), []int{0}
}

func (x *SyncPlatformSalesRequest) GetSalesCode() string {
	if x != nil {
		return x.SalesCode
	}
	return ""
}

func (x *SyncPlatformSalesRequest) GetTermType() string {
	if x != nil {
		return x.TermType
	}
	return ""
}

func (x *SyncPlatformSalesRequest) GetLocationCount() int32 {
	if x != nil {
		return x.LocationCount
	}
	return 0
}

func (x *SyncPlatformSalesRequest) GetVanCount() int32 {
	if x != nil {
		return x.VanCount
	}
	return 0
}

func (x *SyncPlatformSalesRequest) GetBbposCount() int32 {
	if x != nil {
		return x.BbposCount
	}
	return 0
}

func (x *SyncPlatformSalesRequest) GetReaderM2Count() int32 {
	if x != nil {
		return x.ReaderM2Count
	}
	return 0
}

// SyncPlatformSalesResponse
type SyncPlatformSalesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncPlatformSalesResponse) Reset() {
	*x = SyncPlatformSalesResponse{}
	mi := &file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlatformSalesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlatformSalesResponse) ProtoMessage() {}

func (x *SyncPlatformSalesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlatformSalesResponse.ProtoReflect.Descriptor instead.
func (*SyncPlatformSalesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_platform_sales_service_proto_rawDescGZIP(), []int{1}
}

var File_backend_proto_sales_v1_platform_sales_service_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_platform_sales_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/sales/v1/platform_sales_service.proto\x12\x16backend.proto.sales.v1\x1a\x1bbuf/validate/validate.proto\"\xec\x01\n" +
	"\x18SyncPlatformSalesRequest\x12&\n" +
	"\n" +
	"sales_code\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\tsalesCode\x12\x1b\n" +
	"\tterm_type\x18\x02 \x01(\tR\btermType\x12%\n" +
	"\x0elocation_count\x18\x03 \x01(\x05R\rlocationCount\x12\x1b\n" +
	"\tvan_count\x18\x04 \x01(\x05R\bvanCount\x12\x1f\n" +
	"\vbbpos_count\x18\x05 \x01(\x05R\n" +
	"bbposCount\x12&\n" +
	"\x0freader_m2_count\x18\x06 \x01(\x05R\rreaderM2Count\"\x1b\n" +
	"\x19SyncPlatformSalesResponse2\x90\x01\n" +
	"\x14PlatformSalesService\x12x\n" +
	"\x11SyncPlatformSales\x120.backend.proto.sales.v1.SyncPlatformSalesRequest\x1a1.backend.proto.sales.v1.SyncPlatformSalesResponseBb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_platform_sales_service_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_platform_sales_service_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_platform_sales_service_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_platform_sales_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_platform_sales_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_platform_sales_service_proto_rawDesc), len(file_backend_proto_sales_v1_platform_sales_service_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_platform_sales_service_proto_rawDescData
}

var file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_sales_v1_platform_sales_service_proto_goTypes = []any{
	(*SyncPlatformSalesRequest)(nil),  // 0: backend.proto.sales.v1.SyncPlatformSalesRequest
	(*SyncPlatformSalesResponse)(nil), // 1: backend.proto.sales.v1.SyncPlatformSalesResponse
}
var file_backend_proto_sales_v1_platform_sales_service_proto_depIdxs = []int32{
	0, // 0: backend.proto.sales.v1.PlatformSalesService.SyncPlatformSales:input_type -> backend.proto.sales.v1.SyncPlatformSalesRequest
	1, // 1: backend.proto.sales.v1.PlatformSalesService.SyncPlatformSales:output_type -> backend.proto.sales.v1.SyncPlatformSalesResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_platform_sales_service_proto_init() }
func file_backend_proto_sales_v1_platform_sales_service_proto_init() {
	if File_backend_proto_sales_v1_platform_sales_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_platform_sales_service_proto_rawDesc), len(file_backend_proto_sales_v1_platform_sales_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_sales_v1_platform_sales_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_platform_sales_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_sales_v1_platform_sales_service_proto_msgTypes,
	}.Build()
	File_backend_proto_sales_v1_platform_sales_service_proto = out.File
	file_backend_proto_sales_v1_platform_sales_service_proto_goTypes = nil
	file_backend_proto_sales_v1_platform_sales_service_proto_depIdxs = nil
}
