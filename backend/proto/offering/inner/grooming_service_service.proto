syntax = "proto3";

<<<<<<< HEAD
// api-linter: core::0131::response-message-name=disabled
=======
>>>>>>> 0c56c641ab89e414607b4940ccc9ef6e76519446
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)
package backend.proto.offering.inner;

<<<<<<< HEAD
import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";
import "backend/proto/offering/inner/grooming_service.proto";
=======
import "backend/proto/offering/inner/grooming_service.proto";
import "backend/proto/offering/v1/service.proto";
import "buf/validate/validate.proto";
>>>>>>> 0c56c641ab89e414607b4940ccc9ef6e76519446

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.inner";

<<<<<<< HEAD
// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: 兼容结构，后续会下线 --)
// Grooming Service Service
service GroomingServiceService {
  // get applicable service list
  rpc GetInnerApplicableServiceList(GetInnerApplicableServiceListRequest) returns (GetInnerApplicableServiceListResponse);

  //
//  rpc GetApplicableEvaluationList(GetApplicableEvaluationListRequest) returns (GetApplicableEvaluationListResponse);
//
//  //
//  rpc GetServiceListByIds(GetServiceListByIdsRequest) returns (GetServiceListByIdsResponse);
//
//  //
//  rpc CustomizedServiceByPet(CustomizedServiceByPetRequest) returns (CustomizedServiceByPetResponse);
}

// get applicable service list request
message GetInnerApplicableServiceListRequest {
  // company id
  int64 company_id = 1;
  // grooming_service 中的 service item type
  optional offering.inner.ServiceItemType service_item_type = 2;
  // business id, empty for all business in company
  optional int64 business_id = 3;
  // only return available services
  bool only_available = 4;
  // pet id
  optional int64 pet_id = 5;
  // service type
  offering.inner.ServiceType service_type = 6;
  // pet info, for service filter
  optional offering.inner.ServiceApplicableFilter filter = 7;
  // keyword, search by name
  optional string keyword = 8;
  // inactive
  optional bool inactive = 10;
  // customer address zipcode
  optional string zipcode = 11;
  // customer address coordinate
  optional google.type.LatLng coordinate = 12;
}

// get applicable service list response
message GetInnerApplicableServiceListResponse {
  // service list
  repeated offering.inner.ServiceModel service_list = 1;
}

=======
// Service for legacy grooming service operations.
service GroomingServiceService {
  // Batch get by ids
  rpc BatchGetServices(BatchGetServicesRequest) returns (BatchGetServicesResponse);
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// (-- api-linter: core::0231::request-names-field=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// Batch get by ids request
message BatchGetServicesRequest {
  // service ids
  repeated int64 ids = 1 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }
  }];

  // related data mask。默认只加载基本信息
  optional backend.proto.offering.v1.ServiceRelatedDataMask related_data_mask = 2;
}

// (-- api-linter: core::0231::response-resource-field=disabled
//     aip.dev/not-precedent: 自定义 model --)
// Batch get by ids response
message BatchGetServicesResponse {
  // Services
  repeated ServiceModel services = 1;
}
>>>>>>> 0c56c641ab89e414607b4940ccc9ef6e76519446
