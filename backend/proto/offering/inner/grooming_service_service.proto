syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)
package backend.proto.offering.inner;

import "backend/proto/offering/inner/grooming_service.proto";
import "backend/proto/offering/v1/service.proto";
import "buf/validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.inner";

// Service for legacy grooming service operations.
service GroomingServiceService {
  // Batch get by ids
  rpc BatchGetServices(BatchGetServicesRequest) returns (BatchGetServicesResponse);
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// (-- api-linter: core::0231::request-names-field=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// Batch get by ids request
message BatchGetServicesRequest {
  // service ids
  repeated int64 ids = 1 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }
  }];

  // related data。默认只加载基本信息
  repeated backend.proto.offering.v1.RelatedDataEnum related_data = 2;

  // company id
  optional int64 company_id = 3;
}

// (-- api-linter: core::0231::response-resource-field=disabled
//     aip.dev/not-precedent: 自定义 model --)
// Batch get by ids response
message BatchGetServicesResponse {
  // Services
  repeated ServiceModel services = 1;
}
