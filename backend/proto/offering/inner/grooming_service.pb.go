// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/inner/grooming_service.proto

// api-linter: core::0191::file-layout=disabled
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The care type enum
type ServiceItemType int32

const (
	// Unspecified care type
	ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED ServiceItemType = 0
	// Grooming care type
	ServiceItemType_GROOMING ServiceItemType = 1
	// Boarding care type
	ServiceItemType_BOARDING ServiceItemType = 2
	// Daycare care type
	ServiceItemType_DAYCARE ServiceItemType = 3
	// Evaluation care type
	ServiceItemType_EVALUATION ServiceItemType = 4
	// Dog walking care type
	ServiceItemType_DOG_WALKING ServiceItemType = 5
	// Training group class
	ServiceItemType_GROUP_CLASS ServiceItemType = 6
)

// Enum value maps for ServiceItemType.
var (
	ServiceItemType_name = map[int32]string{
		0: "SERVICE_ITEM_TYPE_UNSPECIFIED",
		1: "GROOMING",
		2: "BOARDING",
		3: "DAYCARE",
		4: "EVALUATION",
		5: "DOG_WALKING",
		6: "GROUP_CLASS",
	}
	ServiceItemType_value = map[string]int32{
		"SERVICE_ITEM_TYPE_UNSPECIFIED": 0,
		"GROOMING":                      1,
		"BOARDING":                      2,
		"DAYCARE":                       3,
		"EVALUATION":                    4,
		"DOG_WALKING":                   5,
		"GROUP_CLASS":                   6,
	}
)

func (x ServiceItemType) Enum() *ServiceItemType {
	p := new(ServiceItemType)
	*p = x
	return p
}

func (x ServiceItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[0].Descriptor()
}

func (ServiceItemType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[0]
}

func (x ServiceItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceItemType.Descriptor instead.
func (ServiceItemType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{0}
}

// service price unit
type ServicePriceUnit int32

const (
	// Unspecified service price unit
	ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED ServicePriceUnit = 0
	// Per session service price unit
	ServicePriceUnit_PER_SESSION ServicePriceUnit = 1
	// Per night service price unit
	ServicePriceUnit_PER_NIGHT ServicePriceUnit = 2
	// Per hour service price unit
	ServicePriceUnit_PER_HOUR ServicePriceUnit = 3
	// Per day service price unit
	ServicePriceUnit_PER_DAY ServicePriceUnit = 4
)

// Enum value maps for ServicePriceUnit.
var (
	ServicePriceUnit_name = map[int32]string{
		0: "SERVICE_PRICE_UNIT_UNSPECIFIED",
		1: "PER_SESSION",
		2: "PER_NIGHT",
		3: "PER_HOUR",
		4: "PER_DAY",
	}
	ServicePriceUnit_value = map[string]int32{
		"SERVICE_PRICE_UNIT_UNSPECIFIED": 0,
		"PER_SESSION":                    1,
		"PER_NIGHT":                      2,
		"PER_HOUR":                       3,
		"PER_DAY":                        4,
	}
)

func (x ServicePriceUnit) Enum() *ServicePriceUnit {
	p := new(ServicePriceUnit)
	*p = x
	return p
}

func (x ServicePriceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServicePriceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[1].Descriptor()
}

func (ServicePriceUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[1]
}

func (x ServicePriceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServicePriceUnit.Descriptor instead.
func (ServicePriceUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{1}
}

// service type
type ServiceType int32

const (
	// Unspecified service type
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// normal service
	ServiceType_SERVICE ServiceType = 1
	// service add on
	ServiceType_ADDON ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "ADDON",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE":                  1,
		"ADDON":                    2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[2].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[2]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{2}
}

// source
type Source int32

const (
	// source is not set
	Source_SOURCE_UNSPECIFIED Source = 0
	// source is from MoeGo platform (e.x. b web/app)
	Source_MOEGO_PLATFORM Source = 1
	// source is from Enterprise Hub
	Source_ENTERPRISE_HUB Source = 2
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "MOEGO_PLATFORM",
		2: "ENTERPRISE_HUB",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"MOEGO_PLATFORM":     1,
		"ENTERPRISE_HUB":     2,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[3].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[3]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{3}
}

// date type
type DateType int32

const (
	// unspecified
	DateType_DATE_TYPE_UNSPECIFIED DateType = 0
	// every day, except checkout day(the old enum name in pet detail is called every day, will cause confusion)
	DateType_EVERY_DAY_EXCEPT_CHECKOUT_DAY DateType = 1
	// specific date
	DateType_SPECIFIC_DATE DateType = 2
	// date point
	DateType_DATE_POINT DateType = 3
	// every day include checkout day
	DateType_EVERY_DAY_INCLUDE_CHECKOUT_DAY DateType = 4
	// every day except check-in day
	DateType_EVERY_DAY_EXCEPT_CHECKIN_DAY DateType = 5
	// last day
	DateType_LAST_DAY DateType = 6
	// first day
	DateType_FIRST_DAY DateType = 7
)

// Enum value maps for DateType.
var (
	DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "EVERY_DAY_EXCEPT_CHECKOUT_DAY",
		2: "SPECIFIC_DATE",
		3: "DATE_POINT",
		4: "EVERY_DAY_INCLUDE_CHECKOUT_DAY",
		5: "EVERY_DAY_EXCEPT_CHECKIN_DAY",
		6: "LAST_DAY",
		7: "FIRST_DAY",
	}
	DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":          0,
		"EVERY_DAY_EXCEPT_CHECKOUT_DAY":  1,
		"SPECIFIC_DATE":                  2,
		"DATE_POINT":                     3,
		"EVERY_DAY_INCLUDE_CHECKOUT_DAY": 4,
		"EVERY_DAY_EXCEPT_CHECKIN_DAY":   5,
		"LAST_DAY":                       6,
		"FIRST_DAY":                      7,
	}
)

func (x DateType) Enum() *DateType {
	p := new(DateType)
	*p = x
	return p
}

func (x DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[4].Descriptor()
}

func (DateType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[4]
}

func (x DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateType.Descriptor instead.
func (DateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{4}
}

// pet type
type PetType int32

const (
	// unspecified
	PetType_PET_TYPE_UNSPECIFIED PetType = 0
	// dog
	PetType_PET_TYPE_DOG PetType = 1
	// cat
	PetType_PET_TYPE_CAT PetType = 2
	// bird
	PetType_PET_TYPE_BIRD PetType = 3
	// rabbit
	PetType_PET_TYPE_RABBIT PetType = 4
	// guinea pig
	PetType_PET_TYPE_GUINEA_PIG PetType = 5
	// horse
	PetType_PET_TYPE_HORSE PetType = 6
	// rat
	PetType_PET_TYPE_RAT PetType = 7
	// mouse
	PetType_PET_TYPE_MOUSE PetType = 8
	// hamster
	PetType_PET_TYPE_HAMSTER PetType = 9
	// chinchilla
	PetType_PET_TYPE_CHINCHILLA PetType = 10
	// other
	PetType_PET_TYPE_OTHER PetType = 11
)

// Enum value maps for PetType.
var (
	PetType_name = map[int32]string{
		0:  "PET_TYPE_UNSPECIFIED",
		1:  "PET_TYPE_DOG",
		2:  "PET_TYPE_CAT",
		3:  "PET_TYPE_BIRD",
		4:  "PET_TYPE_RABBIT",
		5:  "PET_TYPE_GUINEA_PIG",
		6:  "PET_TYPE_HORSE",
		7:  "PET_TYPE_RAT",
		8:  "PET_TYPE_MOUSE",
		9:  "PET_TYPE_HAMSTER",
		10: "PET_TYPE_CHINCHILLA",
		11: "PET_TYPE_OTHER",
	}
	PetType_value = map[string]int32{
		"PET_TYPE_UNSPECIFIED": 0,
		"PET_TYPE_DOG":         1,
		"PET_TYPE_CAT":         2,
		"PET_TYPE_BIRD":        3,
		"PET_TYPE_RABBIT":      4,
		"PET_TYPE_GUINEA_PIG":  5,
		"PET_TYPE_HORSE":       6,
		"PET_TYPE_RAT":         7,
		"PET_TYPE_MOUSE":       8,
		"PET_TYPE_HAMSTER":     9,
		"PET_TYPE_CHINCHILLA":  10,
		"PET_TYPE_OTHER":       11,
	}
)

func (x PetType) Enum() *PetType {
	p := new(PetType)
	*p = x
	return p
}

func (x PetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_inner_grooming_service_proto_enumTypes[5].Descriptor()
}

func (PetType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_inner_grooming_service_proto_enumTypes[5]
}

func (x PetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetType.Descriptor instead.
func (PetType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{5}
}

// (-- api-linter: core::0140::prepositions=disabled
//
//	aip.dev/not-precedent: 兼容结构，后续会下线 --)
//
// Service model
type ServiceModel struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,4,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// images
	Images []string `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,9,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// service type
	ServiceItemType ServiceItemType `protobuf:"varint,10,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.offering.inner.ServiceItemType" json:"service_item_type,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,11,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,12,opt,name=price_unit,json=priceUnit,proto3,enum=backend.proto.offering.inner.ServicePriceUnit" json:"price_unit,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,13,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// whether add to commission base
	AddToCommissionBase bool `protobuf:"varint,15,opt,name=add_to_commission_base,json=addToCommissionBase,proto3" json:"add_to_commission_base,omitempty"`
	// whether can tip
	CanTip bool `protobuf:"varint,16,opt,name=can_tip,json=canTip,proto3" json:"can_tip,omitempty"`
	// whether the service is available for all locations
	IsAllLocation bool `protobuf:"varint,17,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// available locations (only if is_available_in_all_locations is false)
	AvailableBusinessIdList []int64 `protobuf:"varint,18,rep,packed,name=available_business_id_list,json=availableBusinessIdList,proto3" json:"available_business_id_list,omitempty"`
	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,19,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	CustomizedBreed []*CustomizedBreed `protobuf:"bytes,20,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,21,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,22,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// weight filter only for compatible with old version, use pet_size in new version
	WeightFilter bool `protobuf:"varint,23,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	// weight range (only if weight_filter is true)
	WeightRange []float64 `protobuf:"fixed64,24,rep,packed,name=weight_range,json=weightRange,proto3" json:"weight_range,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,25,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,26,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// required dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,27,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// whether the service is available for all lodging(only if require_dedicated_lodging is true)
	LodgingFilter bool `protobuf:"varint,28,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
	CustomizedLodgings []int64 `protobuf:"varint,29,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// whether the add on is available for all services(only for add on)
	ServiceFilter bool `protobuf:"varint,30,opt,name=service_filter,json=serviceFilter,proto3" json:"service_filter,omitempty"`
	// service filters(only for add on)
	ServiceFilterList []*ServiceFilter `protobuf:"bytes,31,rep,name=service_filter_list,json=serviceFilterList,proto3" json:"service_filter_list,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,32,opt,name=type,proto3,enum=backend.proto.offering.inner.ServiceType" json:"type,omitempty"`
	// max duration (only for daycare service)
	MaxDuration int32 `protobuf:"varint,33,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// auto rollover rule
	AutoRolloverRule *AutoRolloverRule `protobuf:"bytes,34,opt,name=auto_rollover_rule,json=autoRolloverRule,proto3" json:"auto_rollover_rule,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,35,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,36,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,37,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,38,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// available staffs
	AvailableStaffs *AvailableStaffs `protobuf:"bytes,39,opt,name=available_staffs,json=availableStaffs,proto3" json:"available_staffs,omitempty"`
	// location staff override rules
	LocationStaffOverrideList []*LocationStaffOverrideRule `protobuf:"bytes,40,rep,name=location_staff_override_list,json=locationStaffOverrideList,proto3" json:"location_staff_override_list,omitempty"`
	// whether the service is available for all staff
	AvailableForAllStaff bool `protobuf:"varint,42,opt,name=available_for_all_staff,json=availableForAllStaff,proto3" json:"available_for_all_staff,omitempty"`
	// pet code filter
	PetCodeFilter *PetCodeFilter `protobuf:"bytes,43,opt,name=pet_code_filter,json=petCodeFilter,proto3" json:"pet_code_filter,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,44,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// source
	Source Source `protobuf:"varint,45,opt,name=source,proto3,enum=backend.proto.offering.inner.Source" json:"source,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired bool `protobuf:"varint,52,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb bool `protobuf:"varint,53,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,54,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// additional service rule
	AdditionalServiceRule *AdditionalServiceRule `protobuf:"bytes,55,opt,name=additional_service_rule,json=additionalServiceRule,proto3,oneof" json:"additional_service_rule,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ServiceModel) Reset() {
	*x = ServiceModel{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModel) ProtoMessage() {}

func (x *ServiceModel) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModel.ProtoReflect.Descriptor instead.
func (*ServiceModel) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceModel) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceModel) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceModel) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceModel) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *ServiceModel) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceModel) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *ServiceModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceModel) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceModel) GetAddToCommissionBase() bool {
	if x != nil {
		return x.AddToCommissionBase
	}
	return false
}

func (x *ServiceModel) GetCanTip() bool {
	if x != nil {
		return x.CanTip
	}
	return false
}

func (x *ServiceModel) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *ServiceModel) GetAvailableBusinessIdList() []int64 {
	if x != nil {
		return x.AvailableBusinessIdList
	}
	return nil
}

func (x *ServiceModel) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedBreed() []*CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *ServiceModel) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

func (x *ServiceModel) GetWeightFilter() bool {
	if x != nil {
		return x.WeightFilter
	}
	return false
}

func (x *ServiceModel) GetWeightRange() []float64 {
	if x != nil {
		return x.WeightRange
	}
	return nil
}

func (x *ServiceModel) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *ServiceModel) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *ServiceModel) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *ServiceModel) GetServiceFilter() bool {
	if x != nil {
		return x.ServiceFilter
	}
	return false
}

func (x *ServiceModel) GetServiceFilterList() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilterList
	}
	return nil
}

func (x *ServiceModel) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceModel) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *ServiceModel) GetAutoRolloverRule() *AutoRolloverRule {
	if x != nil {
		return x.AutoRolloverRule
	}
	return nil
}

func (x *ServiceModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceModel) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ServiceModel) GetAvailableStaffs() *AvailableStaffs {
	if x != nil {
		return x.AvailableStaffs
	}
	return nil
}

func (x *ServiceModel) GetLocationStaffOverrideList() []*LocationStaffOverrideRule {
	if x != nil {
		return x.LocationStaffOverrideList
	}
	return nil
}

func (x *ServiceModel) GetAvailableForAllStaff() bool {
	if x != nil {
		return x.AvailableForAllStaff
	}
	return false
}

func (x *ServiceModel) GetPetCodeFilter() *PetCodeFilter {
	if x != nil {
		return x.PetCodeFilter
	}
	return nil
}

func (x *ServiceModel) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *ServiceModel) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

func (x *ServiceModel) GetIsEvaluationRequired() bool {
	if x != nil {
		return x.IsEvaluationRequired
	}
	return false
}

func (x *ServiceModel) GetIsEvaluationRequiredForOb() bool {
	if x != nil {
		return x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *ServiceModel) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *ServiceModel) GetAdditionalServiceRule() *AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// customized breed
type CustomizedBreed struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet type id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// pet breed ids
	Breeds []int64 `protobuf:"varint,2,rep,packed,name=breeds,proto3" json:"breeds,omitempty"`
	// allow all breeds
	IsAll         *bool `protobuf:"varint,3,opt,name=is_all,json=isAll,proto3,oneof" json:"is_all,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomizedBreed) Reset() {
	*x = CustomizedBreed{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomizedBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedBreed) ProtoMessage() {}

func (x *CustomizedBreed) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedBreed.ProtoReflect.Descriptor instead.
func (*CustomizedBreed) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{1}
}

func (x *CustomizedBreed) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *CustomizedBreed) GetBreeds() []int64 {
	if x != nil {
		return x.Breeds
	}
	return nil
}

func (x *CustomizedBreed) GetIsAll() bool {
	if x != nil && x.IsAll != nil {
		return *x.IsAll
	}
	return false
}

// (-- api-linter: core::0140::prepositions=disabled
//
//	aip.dev/not-precedent: 兼容结构，后续会下线 --)
//
// service filter
type ServiceFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.offering.inner.ServiceItemType" json:"service_item_type,omitempty"`
	// whether the addon is available for all services
	AvailableForAllServices *bool `protobuf:"varint,2,opt,name=available_for_all_services,json=availableForAllServices,proto3,oneof" json:"available_for_all_services,omitempty"`
	// available service ids (only if available_for_all_services is false)
	AvailableServiceIdList []int64 `protobuf:"varint,3,rep,packed,name=available_service_id_list,json=availableServiceIdList,proto3" json:"available_service_id_list,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceFilter) Reset() {
	*x = ServiceFilter{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilter) ProtoMessage() {}

func (x *ServiceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilter.ProtoReflect.Descriptor instead.
func (*ServiceFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceFilter) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceFilter) GetAvailableForAllServices() bool {
	if x != nil && x.AvailableForAllServices != nil {
		return *x.AvailableForAllServices
	}
	return false
}

func (x *ServiceFilter) GetAvailableServiceIdList() []int64 {
	if x != nil {
		return x.AvailableServiceIdList
	}
	return nil
}

// pet code filter
type PetCodeFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether to filter by white list or black list
	IsWhiteList bool `protobuf:"varint,1,opt,name=is_white_list,json=isWhiteList,proto3" json:"is_white_list,omitempty"`
	// whether it applies to all pet codes.
	IsAllPetCode bool `protobuf:"varint,2,opt,name=is_all_pet_code,json=isAllPetCode,proto3" json:"is_all_pet_code,omitempty"`
	// pet code list, only valid when is_all_pet_code is false
	PetCodeIds    []int64 `protobuf:"varint,3,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetCodeFilter) Reset() {
	*x = PetCodeFilter{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetCodeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeFilter) ProtoMessage() {}

func (x *PetCodeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeFilter.ProtoReflect.Descriptor instead.
func (*PetCodeFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{3}
}

func (x *PetCodeFilter) GetIsWhiteList() bool {
	if x != nil {
		return x.IsWhiteList
	}
	return false
}

func (x *PetCodeFilter) GetIsAllPetCode() bool {
	if x != nil {
		return x.IsAllPetCode
	}
	return false
}

func (x *PetCodeFilter) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// (-- api-linter: core::0140::prepositions=disabled
//
//	aip.dev/not-precedent: 兼容结构，后续会下线 --)
//
// auto roll over rule
type AutoRolloverRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// auto rollover enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// auto rollover after x minutes
	AfterMinute int32 `protobuf:"varint,2,opt,name=after_minute,json=afterMinute,proto3" json:"after_minute,omitempty"`
	// auto rollover target service id
	TargetServiceId int64 `protobuf:"varint,3,opt,name=target_service_id,json=targetServiceId,proto3" json:"target_service_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AutoRolloverRule) Reset() {
	*x = AutoRolloverRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoRolloverRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoRolloverRule) ProtoMessage() {}

func (x *AutoRolloverRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoRolloverRule.ProtoReflect.Descriptor instead.
func (*AutoRolloverRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{4}
}

func (x *AutoRolloverRule) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AutoRolloverRule) GetAfterMinute() int32 {
	if x != nil {
		return x.AfterMinute
	}
	return 0
}

func (x *AutoRolloverRule) GetTargetServiceId() int64 {
	if x != nil {
		return x.TargetServiceId
	}
	return 0
}

// Available staffs
type AvailableStaffs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// is all available for this service
	IsAllAvailable bool `protobuf:"varint,1,opt,name=is_all_available,json=isAllAvailable,proto3" json:"is_all_available,omitempty"`
	// available staff ids
	Ids           []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableStaffs) Reset() {
	*x = AvailableStaffs{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableStaffs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaffs) ProtoMessage() {}

func (x *AvailableStaffs) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaffs.ProtoReflect.Descriptor instead.
func (*AvailableStaffs) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{5}
}

func (x *AvailableStaffs) GetIsAllAvailable() bool {
	if x != nil {
		return x.IsAllAvailable
	}
	return false
}

func (x *AvailableStaffs) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// service with available location/staff customized info
type LocationStaffOverrideRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business override rule
	LocationOverride *LocationOverrideRule `protobuf:"bytes,1,opt,name=location_override,json=locationOverride,proto3" json:"location_override,omitempty"`
	// staff override rules
	StaffOverrideList []*StaffOverrideRule `protobuf:"bytes,2,rep,name=staff_override_list,json=staffOverrideList,proto3" json:"staff_override_list,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LocationStaffOverrideRule) Reset() {
	*x = LocationStaffOverrideRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationStaffOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationStaffOverrideRule) ProtoMessage() {}

func (x *LocationStaffOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationStaffOverrideRule.ProtoReflect.Descriptor instead.
func (*LocationStaffOverrideRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{6}
}

func (x *LocationStaffOverrideRule) GetLocationOverride() *LocationOverrideRule {
	if x != nil {
		return x.LocationOverride
	}
	return nil
}

func (x *LocationStaffOverrideRule) GetStaffOverrideList() []*StaffOverrideRule {
	if x != nil {
		return x.StaffOverrideList
	}
	return nil
}

// location override rules model
type LocationOverrideRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax(null for not override)
	TaxId *int64 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// duration(null for not override)
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// max duration(only for daycare service)
	MaxDuration   *int32 `protobuf:"varint,5,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationOverrideRule) Reset() {
	*x = LocationOverrideRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationOverrideRule) ProtoMessage() {}

func (x *LocationOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationOverrideRule.ProtoReflect.Descriptor instead.
func (*LocationOverrideRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{7}
}

func (x *LocationOverrideRule) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LocationOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *LocationOverrideRule) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *LocationOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *LocationOverrideRule) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

// service with available staff customized info
type StaffOverrideRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax(null for not override)
	Duration      *int32 `protobuf:"varint,3,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaffOverrideRule) Reset() {
	*x = StaffOverrideRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaffOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffOverrideRule) ProtoMessage() {}

func (x *StaffOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffOverrideRule.ProtoReflect.Descriptor instead.
func (*StaffOverrideRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{8}
}

func (x *StaffOverrideRule) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *StaffOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// additional service rule
type AdditionalServiceRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// enable
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// min stay length
	MinStayLength int32 `protobuf:"varint,2,opt,name=min_stay_length,json=minStayLength,proto3" json:"min_stay_length,omitempty"`
	// apply rules
	ApplyRules    []*AdditionalServiceRule_ApplyRule `protobuf:"bytes,3,rep,name=apply_rules,json=applyRules,proto3" json:"apply_rules,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdditionalServiceRule) Reset() {
	*x = AdditionalServiceRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalServiceRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalServiceRule) ProtoMessage() {}

func (x *AdditionalServiceRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalServiceRule.ProtoReflect.Descriptor instead.
func (*AdditionalServiceRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{9}
}

func (x *AdditionalServiceRule) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AdditionalServiceRule) GetMinStayLength() int32 {
	if x != nil {
		return x.MinStayLength
	}
	return 0
}

func (x *AdditionalServiceRule) GetApplyRules() []*AdditionalServiceRule_ApplyRule {
	if x != nil {
		return x.ApplyRules
	}
	return nil
}

// (-- api-linter: core::0140::prepositions=disabled
//
//	aip.dev/not-precedent: this is not a preposition --)
//
// service applicable filter
type ServiceApplicableFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter by pet
	FilterByPet *ServiceFilterByPet `protobuf:"bytes,1,opt,name=filter_by_pet,json=filterByPet,proto3" json:"filter_by_pet,omitempty"`
	// filter by selected service
	FilterByService *ServiceFilterByService `protobuf:"bytes,2,opt,name=filter_by_service,json=filterByService,proto3" json:"filter_by_service,omitempty"`
	// filter by selected lodging
	FilterByLodging *ServiceFilterByLodging `protobuf:"bytes,3,opt,name=filter_by_lodging,json=filterByLodging,proto3" json:"filter_by_lodging,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceApplicableFilter) Reset() {
	*x = ServiceApplicableFilter{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceApplicableFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceApplicableFilter) ProtoMessage() {}

func (x *ServiceApplicableFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceApplicableFilter.ProtoReflect.Descriptor instead.
func (*ServiceApplicableFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceApplicableFilter) GetFilterByPet() *ServiceFilterByPet {
	if x != nil {
		return x.FilterByPet
	}
	return nil
}

func (x *ServiceApplicableFilter) GetFilterByService() *ServiceFilterByService {
	if x != nil {
		return x.FilterByService
	}
	return nil
}

func (x *ServiceApplicableFilter) GetFilterByLodging() *ServiceFilterByLodging {
	if x != nil {
		return x.FilterByLodging
	}
	return nil
}

// (-- api-linter: core::0140::prepositions=disabled
//
//	aip.dev/not-precedent: this is not a preposition --)
//
// service filter by pet
type ServiceFilterByPet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet type
	PetType PetType `protobuf:"varint,1,opt,name=pet_type,json=petType,proto3,enum=backend.proto.offering.inner.PetType" json:"pet_type,omitempty"`
	// pet breed
	PetBreed *string `protobuf:"bytes,2,opt,name=pet_breed,json=petBreed,proto3,oneof" json:"pet_breed,omitempty"`
	// pet size
	PetSizeId *int64 `protobuf:"varint,3,opt,name=pet_size_id,json=petSizeId,proto3,oneof" json:"pet_size_id,omitempty"`
	// weight, should be deprecated, use pet size
	PetWeight *float64 `protobuf:"fixed64,4,opt,name=pet_weight,json=petWeight,proto3,oneof" json:"pet_weight,omitempty"`
	// pet coat type id
	PetCoatTypeId *int64 `protobuf:"varint,5,opt,name=pet_coat_type_id,json=petCoatTypeId,proto3,oneof" json:"pet_coat_type_id,omitempty"`
	// pet code ids
	PetCodeIds    []int64 `protobuf:"varint,6,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceFilterByPet) Reset() {
	*x = ServiceFilterByPet{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceFilterByPet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByPet) ProtoMessage() {}

func (x *ServiceFilterByPet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByPet.ProtoReflect.Descriptor instead.
func (*ServiceFilterByPet) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{11}
}

func (x *ServiceFilterByPet) GetPetType() PetType {
	if x != nil {
		return x.PetType
	}
	return PetType_PET_TYPE_UNSPECIFIED
}

func (x *ServiceFilterByPet) GetPetBreed() string {
	if x != nil && x.PetBreed != nil {
		return *x.PetBreed
	}
	return ""
}

func (x *ServiceFilterByPet) GetPetSizeId() int64 {
	if x != nil && x.PetSizeId != nil {
		return *x.PetSizeId
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetWeight() float64 {
	if x != nil && x.PetWeight != nil {
		return *x.PetWeight
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetCoatTypeId() int64 {
	if x != nil && x.PetCoatTypeId != nil {
		return *x.PetCoatTypeId
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// service filter by selected service
type ServiceFilterByService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service id list
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// service item type, only support one item type in one filter
	ServiceItemType *ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.offering.inner.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceFilterByService) Reset() {
	*x = ServiceFilterByService{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceFilterByService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByService) ProtoMessage() {}

func (x *ServiceFilterByService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByService.ProtoReflect.Descriptor instead.
func (*ServiceFilterByService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{12}
}

func (x *ServiceFilterByService) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ServiceFilterByService) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

// service filter by selected lodging
type ServiceFilterByLodging struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// lodging id list
	LodgingTypeIds []int64 `protobuf:"varint,1,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ServiceFilterByLodging) Reset() {
	*x = ServiceFilterByLodging{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceFilterByLodging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByLodging) ProtoMessage() {}

func (x *ServiceFilterByLodging) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByLodging.ProtoReflect.Descriptor instead.
func (*ServiceFilterByLodging) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceFilterByLodging) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// apply rule
type AdditionalServiceRule_ApplyRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// date type
	DateType DateType `protobuf:"varint,2,opt,name=date_type,json=dateType,proto3,enum=backend.proto.offering.inner.DateType" json:"date_type,omitempty"`
	// quantity per day
	QuantityPerDay int32 `protobuf:"varint,3,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdditionalServiceRule_ApplyRule) Reset() {
	*x = AdditionalServiceRule_ApplyRule{}
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalServiceRule_ApplyRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalServiceRule_ApplyRule) ProtoMessage() {}

func (x *AdditionalServiceRule_ApplyRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalServiceRule_ApplyRule.ProtoReflect.Descriptor instead.
func (*AdditionalServiceRule_ApplyRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *AdditionalServiceRule_ApplyRule) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *AdditionalServiceRule_ApplyRule) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *AdditionalServiceRule_ApplyRule) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

var File_backend_proto_offering_inner_grooming_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_inner_grooming_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/offering/inner/grooming_service.proto\x12\x1cbackend.proto.offering.inner\x1a\x1fgoogle/protobuf/timestamp.proto\"\x96\x13\n" +
	"\fServiceModel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\binactive\x18\x04 \x01(\bR\binactive\x12\x16\n" +
	"\x06images\x18\x05 \x03(\tR\x06images\x12\x1f\n" +
	"\vcategory_id\x18\x06 \x01(\x03R\n" +
	"categoryId\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x126\n" +
	"\x17require_dedicated_staff\x18\t \x01(\bR\x15requireDedicatedStaff\x12Y\n" +
	"\x11service_item_type\x18\n" +
	" \x01(\x0e2-.backend.proto.offering.inner.ServiceItemTypeR\x0fserviceItemType\x12\x14\n" +
	"\x05price\x18\v \x01(\x01R\x05price\x12M\n" +
	"\n" +
	"price_unit\x18\f \x01(\x0e2..backend.proto.offering.inner.ServicePriceUnitR\tpriceUnit\x12\x15\n" +
	"\x06tax_id\x18\r \x01(\x03R\x05taxId\x12\x1a\n" +
	"\bduration\x18\x0e \x01(\x05R\bduration\x123\n" +
	"\x16add_to_commission_base\x18\x0f \x01(\bR\x13addToCommissionBase\x12\x17\n" +
	"\acan_tip\x18\x10 \x01(\bR\x06canTip\x12&\n" +
	"\x0fis_all_location\x18\x11 \x01(\bR\risAllLocation\x12;\n" +
	"\x1aavailable_business_id_list\x18\x12 \x03(\x03R\x17availableBusinessIdList\x12!\n" +
	"\fbreed_filter\x18\x13 \x01(\bR\vbreedFilter\x12X\n" +
	"\x10customized_breed\x18\x14 \x03(\v2-.backend.proto.offering.inner.CustomizedBreedR\x0fcustomizedBreed\x12&\n" +
	"\x0fpet_size_filter\x18\x15 \x01(\bR\rpetSizeFilter\x120\n" +
	"\x14customized_pet_sizes\x18\x16 \x03(\x03R\x12customizedPetSizes\x12#\n" +
	"\rweight_filter\x18\x17 \x01(\bR\fweightFilter\x12!\n" +
	"\fweight_range\x18\x18 \x03(\x01R\vweightRange\x12\x1f\n" +
	"\vcoat_filter\x18\x19 \x01(\bR\n" +
	"coatFilter\x12'\n" +
	"\x0fcustomized_coat\x18\x1a \x03(\x03R\x0ecustomizedCoat\x12:\n" +
	"\x19require_dedicated_lodging\x18\x1b \x01(\bR\x17requireDedicatedLodging\x12%\n" +
	"\x0elodging_filter\x18\x1c \x01(\bR\rlodgingFilter\x12/\n" +
	"\x13customized_lodgings\x18\x1d \x03(\x03R\x12customizedLodgings\x12%\n" +
	"\x0eservice_filter\x18\x1e \x01(\bR\rserviceFilter\x12[\n" +
	"\x13service_filter_list\x18\x1f \x03(\v2+.backend.proto.offering.inner.ServiceFilterR\x11serviceFilterList\x12=\n" +
	"\x04type\x18  \x01(\x0e2).backend.proto.offering.inner.ServiceTypeR\x04type\x12!\n" +
	"\fmax_duration\x18! \x01(\x05R\vmaxDuration\x12\\\n" +
	"\x12auto_rollover_rule\x18\" \x01(\v2..backend.proto.offering.inner.AutoRolloverRuleR\x10autoRolloverRule\x12;\n" +
	"\vcreate_time\x18# \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18$ \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1d\n" +
	"\n" +
	"is_deleted\x18% \x01(\bR\tisDeleted\x12\x1d\n" +
	"\n" +
	"company_id\x18& \x01(\x03R\tcompanyId\x12X\n" +
	"\x10available_staffs\x18' \x01(\v2-.backend.proto.offering.inner.AvailableStaffsR\x0favailableStaffs\x12x\n" +
	"\x1clocation_staff_override_list\x18( \x03(\v27.backend.proto.offering.inner.LocationStaffOverrideRuleR\x19locationStaffOverrideList\x125\n" +
	"\x17available_for_all_staff\x18* \x01(\bR\x14availableForAllStaff\x12S\n" +
	"\x0fpet_code_filter\x18+ \x01(\v2+.backend.proto.offering.inner.PetCodeFilterR\rpetCodeFilter\x12,\n" +
	"\x12bundle_service_ids\x18, \x03(\x03R\x10bundleServiceIds\x12<\n" +
	"\x06source\x18- \x01(\x0e2$.backend.proto.offering.inner.SourceR\x06source\x124\n" +
	"\x16is_evaluation_required\x184 \x01(\bR\x14isEvaluationRequired\x12@\n" +
	"\x1dis_evaluation_required_for_ob\x185 \x01(\bR\x19isEvaluationRequiredForOb\x12#\n" +
	"\revaluation_id\x186 \x01(\x03R\fevaluationId\x12p\n" +
	"\x17additional_service_rule\x187 \x01(\v23.backend.proto.offering.inner.AdditionalServiceRuleH\x00R\x15additionalServiceRule\x88\x01\x01B\x1a\n" +
	"\x18_additional_service_rule\"p\n" +
	"\x0fCustomizedBreed\x12\x1e\n" +
	"\vpet_type_id\x18\x01 \x01(\x03R\tpetTypeId\x12\x16\n" +
	"\x06breeds\x18\x02 \x03(\x03R\x06breeds\x12\x1a\n" +
	"\x06is_all\x18\x03 \x01(\bH\x00R\x05isAll\x88\x01\x01B\t\n" +
	"\a_is_all\"\x86\x02\n" +
	"\rServiceFilter\x12Y\n" +
	"\x11service_item_type\x18\x01 \x01(\x0e2-.backend.proto.offering.inner.ServiceItemTypeR\x0fserviceItemType\x12@\n" +
	"\x1aavailable_for_all_services\x18\x02 \x01(\bH\x00R\x17availableForAllServices\x88\x01\x01\x129\n" +
	"\x19available_service_id_list\x18\x03 \x03(\x03R\x16availableServiceIdListB\x1d\n" +
	"\x1b_available_for_all_services\"|\n" +
	"\rPetCodeFilter\x12\"\n" +
	"\ris_white_list\x18\x01 \x01(\bR\visWhiteList\x12%\n" +
	"\x0fis_all_pet_code\x18\x02 \x01(\bR\fisAllPetCode\x12 \n" +
	"\fpet_code_ids\x18\x03 \x03(\x03R\n" +
	"petCodeIds\"{\n" +
	"\x10AutoRolloverRule\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12!\n" +
	"\fafter_minute\x18\x02 \x01(\x05R\vafterMinute\x12*\n" +
	"\x11target_service_id\x18\x03 \x01(\x03R\x0ftargetServiceId\"M\n" +
	"\x0fAvailableStaffs\x12(\n" +
	"\x10is_all_available\x18\x01 \x01(\bR\x0eisAllAvailable\x12\x10\n" +
	"\x03ids\x18\x02 \x03(\x03R\x03ids\"\xdd\x01\n" +
	"\x19LocationStaffOverrideRule\x12_\n" +
	"\x11location_override\x18\x01 \x01(\v22.backend.proto.offering.inner.LocationOverrideRuleR\x10locationOverride\x12_\n" +
	"\x13staff_override_list\x18\x02 \x03(\v2/.backend.proto.offering.inner.StaffOverrideRuleR\x11staffOverrideList\"\xea\x01\n" +
	"\x14LocationOverrideRule\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1a\n" +
	"\x06tax_id\x18\x03 \x01(\x03H\x01R\x05taxId\x88\x01\x01\x12\x1f\n" +
	"\bduration\x18\x04 \x01(\x05H\x02R\bduration\x88\x01\x01\x12&\n" +
	"\fmax_duration\x18\x05 \x01(\x05H\x03R\vmaxDuration\x88\x01\x01B\b\n" +
	"\x06_priceB\t\n" +
	"\a_tax_idB\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_duration\"\x81\x01\n" +
	"\x11StaffOverrideRule\x12\x19\n" +
	"\bstaff_id\x18\x01 \x01(\x03R\astaffId\x12\x19\n" +
	"\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1f\n" +
	"\bduration\x18\x03 \x01(\x05H\x01R\bduration\x88\x01\x01B\b\n" +
	"\x06_priceB\v\n" +
	"\t_duration\"\xd3\x02\n" +
	"\x15AdditionalServiceRule\x12\x16\n" +
	"\x06enable\x18\x01 \x01(\bR\x06enable\x12&\n" +
	"\x0fmin_stay_length\x18\x02 \x01(\x05R\rminStayLength\x12^\n" +
	"\vapply_rules\x18\x03 \x03(\v2=.backend.proto.offering.inner.AdditionalServiceRule.ApplyRuleR\n" +
	"applyRules\x1a\x99\x01\n" +
	"\tApplyRule\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\x12C\n" +
	"\tdate_type\x18\x02 \x01(\x0e2&.backend.proto.offering.inner.DateTypeR\bdateType\x12(\n" +
	"\x10quantity_per_day\x18\x03 \x01(\x05R\x0equantityPerDay\"\xb3\x02\n" +
	"\x17ServiceApplicableFilter\x12T\n" +
	"\rfilter_by_pet\x18\x01 \x01(\v20.backend.proto.offering.inner.ServiceFilterByPetR\vfilterByPet\x12`\n" +
	"\x11filter_by_service\x18\x02 \x01(\v24.backend.proto.offering.inner.ServiceFilterByServiceR\x0ffilterByService\x12`\n" +
	"\x11filter_by_lodging\x18\x03 \x01(\v24.backend.proto.offering.inner.ServiceFilterByLodgingR\x0ffilterByLodging\"\xd3\x02\n" +
	"\x12ServiceFilterByPet\x12@\n" +
	"\bpet_type\x18\x01 \x01(\x0e2%.backend.proto.offering.inner.PetTypeR\apetType\x12 \n" +
	"\tpet_breed\x18\x02 \x01(\tH\x00R\bpetBreed\x88\x01\x01\x12#\n" +
	"\vpet_size_id\x18\x03 \x01(\x03H\x01R\tpetSizeId\x88\x01\x01\x12\"\n" +
	"\n" +
	"pet_weight\x18\x04 \x01(\x01H\x02R\tpetWeight\x88\x01\x01\x12,\n" +
	"\x10pet_coat_type_id\x18\x05 \x01(\x03H\x03R\rpetCoatTypeId\x88\x01\x01\x12 \n" +
	"\fpet_code_ids\x18\x06 \x03(\x03R\n" +
	"petCodeIdsB\f\n" +
	"\n" +
	"_pet_breedB\x0e\n" +
	"\f_pet_size_idB\r\n" +
	"\v_pet_weightB\x13\n" +
	"\x11_pet_coat_type_id\"\xaf\x01\n" +
	"\x16ServiceFilterByService\x12\x1f\n" +
	"\vservice_ids\x18\x01 \x03(\x03R\n" +
	"serviceIds\x12^\n" +
	"\x11service_item_type\x18\x02 \x01(\x0e2-.backend.proto.offering.inner.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01B\x14\n" +
	"\x12_service_item_type\"B\n" +
	"\x16ServiceFilterByLodging\x12(\n" +
	"\x10lodging_type_ids\x18\x01 \x03(\x03R\x0elodgingTypeIds*\x8f\x01\n" +
	"\x0fServiceItemType\x12!\n" +
	"\x1dSERVICE_ITEM_TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bGROOMING\x10\x01\x12\f\n" +
	"\bBOARDING\x10\x02\x12\v\n" +
	"\aDAYCARE\x10\x03\x12\x0e\n" +
	"\n" +
	"EVALUATION\x10\x04\x12\x0f\n" +
	"\vDOG_WALKING\x10\x05\x12\x0f\n" +
	"\vGROUP_CLASS\x10\x06*q\n" +
	"\x10ServicePriceUnit\x12\"\n" +
	"\x1eSERVICE_PRICE_UNIT_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vPER_SESSION\x10\x01\x12\r\n" +
	"\tPER_NIGHT\x10\x02\x12\f\n" +
	"\bPER_HOUR\x10\x03\x12\v\n" +
	"\aPER_DAY\x10\x04*C\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aSERVICE\x10\x01\x12\t\n" +
	"\x05ADDON\x10\x02*H\n" +
	"\x06Source\x12\x16\n" +
	"\x12SOURCE_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eMOEGO_PLATFORM\x10\x01\x12\x12\n" +
	"\x0eENTERPRISE_HUB\x10\x02*\xce\x01\n" +
	"\bDateType\x12\x19\n" +
	"\x15DATE_TYPE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dEVERY_DAY_EXCEPT_CHECKOUT_DAY\x10\x01\x12\x11\n" +
	"\rSPECIFIC_DATE\x10\x02\x12\x0e\n" +
	"\n" +
	"DATE_POINT\x10\x03\x12\"\n" +
	"\x1eEVERY_DAY_INCLUDE_CHECKOUT_DAY\x10\x04\x12 \n" +
	"\x1cEVERY_DAY_EXCEPT_CHECKIN_DAY\x10\x05\x12\f\n" +
	"\bLAST_DAY\x10\x06\x12\r\n" +
	"\tFIRST_DAY\x10\a*\x85\x02\n" +
	"\aPetType\x12\x18\n" +
	"\x14PET_TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fPET_TYPE_DOG\x10\x01\x12\x10\n" +
	"\fPET_TYPE_CAT\x10\x02\x12\x11\n" +
	"\rPET_TYPE_BIRD\x10\x03\x12\x13\n" +
	"\x0fPET_TYPE_RABBIT\x10\x04\x12\x17\n" +
	"\x13PET_TYPE_GUINEA_PIG\x10\x05\x12\x12\n" +
	"\x0ePET_TYPE_HORSE\x10\x06\x12\x10\n" +
	"\fPET_TYPE_RAT\x10\a\x12\x12\n" +
	"\x0ePET_TYPE_MOUSE\x10\b\x12\x14\n" +
	"\x10PET_TYPE_HAMSTER\x10\t\x12\x17\n" +
	"\x13PET_TYPE_CHINCHILLA\x10\n" +
	"\x12\x12\n" +
	"\x0ePET_TYPE_OTHER\x10\vBv\n" +
	"&com.moego.backend.proto.offering.innerP\x01ZJgithub.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpbb\x06proto3"

var (
	file_backend_proto_offering_inner_grooming_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_inner_grooming_service_proto_rawDescData []byte
)

func file_backend_proto_offering_inner_grooming_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_inner_grooming_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_inner_grooming_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_inner_grooming_service_proto_rawDescData
}

var file_backend_proto_offering_inner_grooming_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_backend_proto_offering_inner_grooming_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_backend_proto_offering_inner_grooming_service_proto_goTypes = []any{
	(ServiceItemType)(0),                    // 0: backend.proto.offering.inner.ServiceItemType
	(ServicePriceUnit)(0),                   // 1: backend.proto.offering.inner.ServicePriceUnit
	(ServiceType)(0),                        // 2: backend.proto.offering.inner.ServiceType
	(Source)(0),                             // 3: backend.proto.offering.inner.Source
	(DateType)(0),                           // 4: backend.proto.offering.inner.DateType
	(PetType)(0),                            // 5: backend.proto.offering.inner.PetType
	(*ServiceModel)(nil),                    // 6: backend.proto.offering.inner.ServiceModel
	(*CustomizedBreed)(nil),                 // 7: backend.proto.offering.inner.CustomizedBreed
	(*ServiceFilter)(nil),                   // 8: backend.proto.offering.inner.ServiceFilter
	(*PetCodeFilter)(nil),                   // 9: backend.proto.offering.inner.PetCodeFilter
	(*AutoRolloverRule)(nil),                // 10: backend.proto.offering.inner.AutoRolloverRule
	(*AvailableStaffs)(nil),                 // 11: backend.proto.offering.inner.AvailableStaffs
	(*LocationStaffOverrideRule)(nil),       // 12: backend.proto.offering.inner.LocationStaffOverrideRule
	(*LocationOverrideRule)(nil),            // 13: backend.proto.offering.inner.LocationOverrideRule
	(*StaffOverrideRule)(nil),               // 14: backend.proto.offering.inner.StaffOverrideRule
	(*AdditionalServiceRule)(nil),           // 15: backend.proto.offering.inner.AdditionalServiceRule
	(*ServiceApplicableFilter)(nil),         // 16: backend.proto.offering.inner.ServiceApplicableFilter
	(*ServiceFilterByPet)(nil),              // 17: backend.proto.offering.inner.ServiceFilterByPet
	(*ServiceFilterByService)(nil),          // 18: backend.proto.offering.inner.ServiceFilterByService
	(*ServiceFilterByLodging)(nil),          // 19: backend.proto.offering.inner.ServiceFilterByLodging
	(*AdditionalServiceRule_ApplyRule)(nil), // 20: backend.proto.offering.inner.AdditionalServiceRule.ApplyRule
	(*timestamppb.Timestamp)(nil),           // 21: google.protobuf.Timestamp
}
var file_backend_proto_offering_inner_grooming_service_proto_depIdxs = []int32{
	0,  // 0: backend.proto.offering.inner.ServiceModel.service_item_type:type_name -> backend.proto.offering.inner.ServiceItemType
	1,  // 1: backend.proto.offering.inner.ServiceModel.price_unit:type_name -> backend.proto.offering.inner.ServicePriceUnit
	7,  // 2: backend.proto.offering.inner.ServiceModel.customized_breed:type_name -> backend.proto.offering.inner.CustomizedBreed
	8,  // 3: backend.proto.offering.inner.ServiceModel.service_filter_list:type_name -> backend.proto.offering.inner.ServiceFilter
	2,  // 4: backend.proto.offering.inner.ServiceModel.type:type_name -> backend.proto.offering.inner.ServiceType
	10, // 5: backend.proto.offering.inner.ServiceModel.auto_rollover_rule:type_name -> backend.proto.offering.inner.AutoRolloverRule
	21, // 6: backend.proto.offering.inner.ServiceModel.create_time:type_name -> google.protobuf.Timestamp
	21, // 7: backend.proto.offering.inner.ServiceModel.update_time:type_name -> google.protobuf.Timestamp
	11, // 8: backend.proto.offering.inner.ServiceModel.available_staffs:type_name -> backend.proto.offering.inner.AvailableStaffs
	12, // 9: backend.proto.offering.inner.ServiceModel.location_staff_override_list:type_name -> backend.proto.offering.inner.LocationStaffOverrideRule
	9,  // 10: backend.proto.offering.inner.ServiceModel.pet_code_filter:type_name -> backend.proto.offering.inner.PetCodeFilter
	3,  // 11: backend.proto.offering.inner.ServiceModel.source:type_name -> backend.proto.offering.inner.Source
	15, // 12: backend.proto.offering.inner.ServiceModel.additional_service_rule:type_name -> backend.proto.offering.inner.AdditionalServiceRule
	0,  // 13: backend.proto.offering.inner.ServiceFilter.service_item_type:type_name -> backend.proto.offering.inner.ServiceItemType
	13, // 14: backend.proto.offering.inner.LocationStaffOverrideRule.location_override:type_name -> backend.proto.offering.inner.LocationOverrideRule
	14, // 15: backend.proto.offering.inner.LocationStaffOverrideRule.staff_override_list:type_name -> backend.proto.offering.inner.StaffOverrideRule
	20, // 16: backend.proto.offering.inner.AdditionalServiceRule.apply_rules:type_name -> backend.proto.offering.inner.AdditionalServiceRule.ApplyRule
	17, // 17: backend.proto.offering.inner.ServiceApplicableFilter.filter_by_pet:type_name -> backend.proto.offering.inner.ServiceFilterByPet
	18, // 18: backend.proto.offering.inner.ServiceApplicableFilter.filter_by_service:type_name -> backend.proto.offering.inner.ServiceFilterByService
	19, // 19: backend.proto.offering.inner.ServiceApplicableFilter.filter_by_lodging:type_name -> backend.proto.offering.inner.ServiceFilterByLodging
	5,  // 20: backend.proto.offering.inner.ServiceFilterByPet.pet_type:type_name -> backend.proto.offering.inner.PetType
	0,  // 21: backend.proto.offering.inner.ServiceFilterByService.service_item_type:type_name -> backend.proto.offering.inner.ServiceItemType
	4,  // 22: backend.proto.offering.inner.AdditionalServiceRule.ApplyRule.date_type:type_name -> backend.proto.offering.inner.DateType
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_inner_grooming_service_proto_init() }
func file_backend_proto_offering_inner_grooming_service_proto_init() {
	if File_backend_proto_offering_inner_grooming_service_proto != nil {
		return
	}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[7].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_proto_msgTypes[12].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_inner_grooming_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_inner_grooming_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_inner_grooming_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_inner_grooming_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_inner_grooming_service_proto = out.File
	file_backend_proto_offering_inner_grooming_service_proto_goTypes = nil
	file_backend_proto_offering_inner_grooming_service_proto_depIdxs = nil
}
