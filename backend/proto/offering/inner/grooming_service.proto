syntax = "proto3";

// api-linter: core::0191::file-layout=disabled
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)
package backend.proto.offering.inner;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.inner";

// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: 兼容结构，后续会下线 --)
// Service model
message ServiceModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // description
  string description = 3;
  // inactive
  bool inactive = 4;
  // images
  repeated string images = 5;
  // category id
  int64 category_id = 6;
  // color code
  string color_code = 7;
  // require dedicated staff
  bool require_dedicated_staff = 9;
  // service type
  ServiceItemType service_item_type = 10;
  // price
  double price = 11;
  // price unit
  ServicePriceUnit price_unit = 12;
  // tax id
  int64 tax_id = 13;
  // duration
  int32 duration = 14;
  // whether add to commission base
  bool add_to_commission_base = 15;
  // whether can tip
  bool can_tip = 16;
  // whether the service is available for all locations
  bool is_all_location = 17;
  // available locations (only if is_available_in_all_locations is false)
  repeated int64 available_business_id_list = 18;
  // whether the service is available for all pet type & breed
  bool breed_filter = 19;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated CustomizedBreed customized_breed = 20;
  // available for all pet size
  bool pet_size_filter = 21;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 22;
  // weight filter only for compatible with old version, use pet_size in new version
  bool weight_filter = 23;
  // weight range (only if weight_filter is true)
  repeated double weight_range = 24;
  // available for all pet coat type
  bool coat_filter = 25;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 26;
  // required dedicated lodging
  bool require_dedicated_lodging = 27;
  // whether the service is available for all lodging(only if require_dedicated_lodging is true)
  bool lodging_filter = 28;
  // available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
  repeated int64 customized_lodgings = 29;
  // whether the add on is available for all services(only for add on)
  bool service_filter = 30;
  // service filters(only for add on)
  repeated ServiceFilter service_filter_list = 31;
  // service type
  ServiceType type = 32;
  // max duration (only for daycare service)
  int32 max_duration = 33;
  // auto rollover rule
  AutoRolloverRule auto_rollover_rule = 34;
  // create time
  google.protobuf.Timestamp create_time = 35;
  // update time
  google.protobuf.Timestamp update_time = 36;
  // is deleted
  bool is_deleted = 37;
  // company id
  int64 company_id = 38;
  // available staffs
  AvailableStaffs available_staffs = 39;
  // location staff override rules
  repeated LocationStaffOverrideRule location_staff_override_list = 40;
  // whether the service is available for all staff
  bool available_for_all_staff = 42;

  // pet code filter
  PetCodeFilter pet_code_filter = 43;

  // bundle services
  repeated int64 bundle_service_ids = 44;

  // source
  Source source = 45;

  // whether evaluation is required
  bool is_evaluation_required = 52;

  // whether evaluation is required before online booking
  bool is_evaluation_required_for_ob = 53;

  // evaluation id
  int64 evaluation_id = 54;

  // additional service rule
  optional AdditionalServiceRule additional_service_rule = 55;
}

// customized breed
message CustomizedBreed {
  // pet type id
  int64 pet_type_id = 1;
  // pet breed ids
  repeated int64 breeds = 2;
  // allow all breeds
  optional bool is_all = 3;
}

// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: 兼容结构，后续会下线 --)
// service filter
message ServiceFilter {
  // service item type
  ServiceItemType service_item_type = 1;
  // whether the addon is available for all services
  optional bool available_for_all_services = 2;
  // available service ids (only if available_for_all_services is false)
  repeated int64 available_service_id_list = 3;
}

// pet code filter
message PetCodeFilter {
  // whether to filter by white list or black list
  bool is_white_list = 1;
  // whether it applies to all pet codes.
  bool is_all_pet_code = 2;
  // pet code list, only valid when is_all_pet_code is false
  repeated int64 pet_code_ids = 3;
}

// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: 兼容结构，后续会下线 --)
// auto roll over rule
message AutoRolloverRule {
  // auto rollover enabled
  bool enabled = 1;
  // auto rollover after x minutes
  int32 after_minute = 2;
  // auto rollover target service id
  int64 target_service_id = 3;
}

// Available staffs
message AvailableStaffs {
  // is all available for this service
  bool is_all_available = 1;
  // available staff ids
  repeated int64 ids = 2;
}

// service with available location/staff customized info
message LocationStaffOverrideRule {
  // business override rule
  LocationOverrideRule location_override = 1;
  // staff override rules
  repeated StaffOverrideRule staff_override_list = 2;
}

// location override rules model
message LocationOverrideRule {
  // business id
  int64 business_id = 1;
  // price(null for not override)
  optional double price = 2;
  // tax(null for not override)
  optional int64 tax_id = 3;
  // duration(null for not override)
  optional int32 duration = 4;
  // max duration(only for daycare service)
  optional int32 max_duration = 5;
}

// service with available staff customized info
message StaffOverrideRule {
  // staff id
  int64 staff_id = 1;
  // price(null for not override)
  optional double price = 2;
  // tax(null for not override)
  optional int32 duration = 3;
}

// additional service rule
message AdditionalServiceRule {
  // enable
  bool enable = 1;
  // min stay length
  int32 min_stay_length = 2;
  // apply rule
  message ApplyRule {
    // service id
    int64 service_id = 1;
    // date type
    DateType date_type = 2;
    // quantity per day
    int32 quantity_per_day = 3;
  }
  // apply rules
  repeated ApplyRule apply_rules = 3;
}

// The care type enum
enum ServiceItemType {
  // Unspecified care type
  SERVICE_ITEM_TYPE_UNSPECIFIED = 0;
  // Grooming care type
  GROOMING = 1;
  // Boarding care type
  BOARDING = 2;
  // Daycare care type
  DAYCARE = 3;
  // Evaluation care type
  EVALUATION = 4;
  // Dog walking care type
  DOG_WALKING = 5;
  // Training group class
  GROUP_CLASS = 6;
}

// service price unit
enum ServicePriceUnit {
  // Unspecified service price unit
  SERVICE_PRICE_UNIT_UNSPECIFIED = 0;
  // Per session service price unit
  PER_SESSION = 1;
  // Per night service price unit
  PER_NIGHT = 2;
  // Per hour service price unit
  PER_HOUR = 3;
  // Per day service price unit
  PER_DAY = 4;
}

// service type
enum ServiceType {
  // Unspecified service type
  SERVICE_TYPE_UNSPECIFIED = 0;
  // normal service
  SERVICE = 1;
  // service add on
  ADDON = 2;
}

// source
enum Source {
  // source is not set
  SOURCE_UNSPECIFIED = 0;
  // source is from MoeGo platform (e.x. b web/app)
  MOEGO_PLATFORM = 1;
  // source is from Enterprise Hub
  ENTERPRISE_HUB = 2;
}

// date type
enum DateType {
  // unspecified
  DATE_TYPE_UNSPECIFIED = 0;
  // every day, except checkout day(the old enum name in pet detail is called every day, will cause confusion)
  EVERY_DAY_EXCEPT_CHECKOUT_DAY = 1;
  // specific date
  SPECIFIC_DATE = 2;
  // date point
  DATE_POINT = 3;
  // every day include checkout day
  EVERY_DAY_INCLUDE_CHECKOUT_DAY = 4;
  // every day except check-in day
  EVERY_DAY_EXCEPT_CHECKIN_DAY = 5;
  // last day
  LAST_DAY = 6;
  // first day
  FIRST_DAY = 7;
}

// pet type
enum PetType {
  // unspecified
  PET_TYPE_UNSPECIFIED = 0;
  // dog
  PET_TYPE_DOG = 1;
  // cat
  PET_TYPE_CAT = 2;
  // bird
  PET_TYPE_BIRD = 3;
  // rabbit
  PET_TYPE_RABBIT = 4;
  // guinea pig
  PET_TYPE_GUINEA_PIG = 5;
  // horse
  PET_TYPE_HORSE = 6;
  // rat
  PET_TYPE_RAT = 7;
  // mouse
  PET_TYPE_MOUSE = 8;
  // hamster
  PET_TYPE_HAMSTER = 9;
  // chinchilla
  PET_TYPE_CHINCHILLA = 10;
  // other
  PET_TYPE_OTHER = 11;
}

// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: this is not a preposition --)
// service applicable filter
message ServiceApplicableFilter {
  // filter by pet
  ServiceFilterByPet filter_by_pet = 1;
  // filter by selected service
  ServiceFilterByService filter_by_service = 2;
  // filter by selected lodging
  ServiceFilterByLodging filter_by_lodging = 3;
}

// (-- api-linter: core::0140::prepositions=disabled
//     aip.dev/not-precedent: this is not a preposition --)
// service filter by pet
message ServiceFilterByPet {
  // pet type
  PetType pet_type = 1;
  // pet breed
  optional string pet_breed = 2;
  // pet size
  optional int64 pet_size_id = 3;
  // weight, should be deprecated, use pet size
  optional double pet_weight = 4;
  // pet coat type id
  optional int64 pet_coat_type_id = 5;
  // pet code ids
  repeated int64 pet_code_ids = 6;
}

// service filter by selected service
message ServiceFilterByService {
  // service id list
  repeated int64 service_ids = 1;
  // service item type, only support one item type in one filter
  optional ServiceItemType service_item_type = 2;
}

// service filter by selected lodging
message ServiceFilterByLodging {
  // lodging id list
  repeated int64 lodging_type_ids = 1;
}