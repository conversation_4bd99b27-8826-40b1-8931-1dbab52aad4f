// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/inner/grooming_service_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0231::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// (-- api-linter: core::0231::request-names-field=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// Batch get by ids request
type BatchGetServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// related data。默认只加载基本信息
	RelatedData []v1.RelatedDataEnum `protobuf:"varint,2,rep,packed,name=related_data,json=relatedData,proto3,enum=backend.proto.offering.v1.RelatedDataEnum" json:"related_data,omitempty"`
	// company id
	CompanyId     *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesRequest) Reset() {
	*x = BatchGetServicesRequest{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesRequest) ProtoMessage() {}

func (x *BatchGetServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *BatchGetServicesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchGetServicesRequest) GetRelatedData() []v1.RelatedDataEnum {
	if x != nil {
		return x.RelatedData
	}
	return nil
}

func (x *BatchGetServicesRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// (-- api-linter: core::0231::response-resource-field=disabled
//
//	aip.dev/not-precedent: 自定义 model --)
//
// Batch get by ids response
type BatchGetServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Services
	Services      []*ServiceModel `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesResponse) Reset() {
	*x = BatchGetServicesResponse{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesResponse) ProtoMessage() {}

func (x *BatchGetServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{1}
}

func (x *BatchGetServicesResponse) GetServices() []*ServiceModel {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_backend_proto_offering_inner_grooming_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc = "" +
	"\n" +
	";backend/proto/offering/inner/grooming_service_service.proto\x12\x1cbackend.proto.offering.inner\x1a3backend/proto/offering/inner/grooming_service.proto\x1a'backend/proto/offering/v1/service.proto\x1a\x1bbuf/validate/validate.proto\"\xc2\x01\n" +
	"\x17BatchGetServicesRequest\x12%\n" +
	"\x03ids\x18\x01 \x03(\x03B\x13\xbaH\x10\x92\x01\r\b\x01\x10\xe8\a\x18\x01\"\x04\"\x02 \x00R\x03ids\x12M\n" +
	"\frelated_data\x18\x02 \x03(\x0e2*.backend.proto.offering.v1.RelatedDataEnumR\vrelatedData\x12\"\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03H\x00R\tcompanyId\x88\x01\x01B\r\n" +
	"\v_company_id\"b\n" +
	"\x18BatchGetServicesResponse\x12F\n" +
	"\bservices\x18\x01 \x03(\v2*.backend.proto.offering.inner.ServiceModelR\bservices2\x9c\x01\n" +
	"\x16GroomingServiceService\x12\x81\x01\n" +
	"\x10BatchGetServices\x125.backend.proto.offering.inner.BatchGetServicesRequest\x1a6.backend.proto.offering.inner.BatchGetServicesResponseBv\n" +
	"&com.moego.backend.proto.offering.innerP\x01ZJgithub.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpbb\x06proto3"

var (
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData
}

var file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = []any{
	(*BatchGetServicesRequest)(nil),  // 0: backend.proto.offering.inner.BatchGetServicesRequest
	(*BatchGetServicesResponse)(nil), // 1: backend.proto.offering.inner.BatchGetServicesResponse
	(v1.RelatedDataEnum)(0),          // 2: backend.proto.offering.v1.RelatedDataEnum
	(*ServiceModel)(nil),             // 3: backend.proto.offering.inner.ServiceModel
}
var file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.offering.inner.BatchGetServicesRequest.related_data:type_name -> backend.proto.offering.v1.RelatedDataEnum
	3, // 1: backend.proto.offering.inner.BatchGetServicesResponse.services:type_name -> backend.proto.offering.inner.ServiceModel
	0, // 2: backend.proto.offering.inner.GroomingServiceService.BatchGetServices:input_type -> backend.proto.offering.inner.BatchGetServicesRequest
	1, // 3: backend.proto.offering.inner.GroomingServiceService.BatchGetServices:output_type -> backend.proto.offering.inner.BatchGetServicesResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_inner_grooming_service_service_proto_init() }
func file_backend_proto_offering_inner_grooming_service_service_proto_init() {
	if File_backend_proto_offering_inner_grooming_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_inner_grooming_service_proto_init()
	file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_inner_grooming_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_inner_grooming_service_service_proto = out.File
	file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = nil
	file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = nil
}
