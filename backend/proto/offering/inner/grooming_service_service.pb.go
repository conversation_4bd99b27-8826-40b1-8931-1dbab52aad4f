// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/inner/grooming_service_service.proto

// api-linter: core::0131::response-message-name=disabled
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get applicable service list request
type GetNewApplicableServiceListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// grooming_service 中的 service item type
	ServiceItemType *ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.offering.inner.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// business id, empty for all business in company
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// only return available services
	OnlyAvailable bool `protobuf:"varint,4,opt,name=only_available,json=onlyAvailable,proto3" json:"only_available,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// service type
	ServiceType ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,proto3,enum=backend.proto.offering.inner.ServiceType" json:"service_type,omitempty"`
	// pet info, for service filter
	Filter *ServiceApplicableFilter `protobuf:"bytes,7,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// keyword, search by name
	Keyword *string `protobuf:"bytes,8,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,10,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
	// customer address zipcode
	Zipcode *string `protobuf:"bytes,11,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// customer address coordinate
	Coordinate *latlng.LatLng `protobuf:"bytes,12,opt,name=coordinate,proto3,oneof" json:"coordinate,omitempty"`
	// related data mask。默认只加载基本信息
	RelatedDataMask *v1.ServiceRelatedDataMask `protobuf:"varint,13,opt,name=related_data_mask,json=relatedDataMask,proto3,enum=backend.proto.offering.v1.ServiceRelatedDataMask,oneof" json:"related_data_mask,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetNewApplicableServiceListRequest) Reset() {
	*x = GetNewApplicableServiceListRequest{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNewApplicableServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNewApplicableServiceListRequest) ProtoMessage() {}

func (x *GetNewApplicableServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNewApplicableServiceListRequest.ProtoReflect.Descriptor instead.
func (*GetNewApplicableServiceListRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetNewApplicableServiceListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetNewApplicableServiceListRequest) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *GetNewApplicableServiceListRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetNewApplicableServiceListRequest) GetOnlyAvailable() bool {
	if x != nil {
		return x.OnlyAvailable
	}
	return false
}

func (x *GetNewApplicableServiceListRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *GetNewApplicableServiceListRequest) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *GetNewApplicableServiceListRequest) GetFilter() *ServiceApplicableFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *GetNewApplicableServiceListRequest) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetNewApplicableServiceListRequest) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

func (x *GetNewApplicableServiceListRequest) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *GetNewApplicableServiceListRequest) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *GetNewApplicableServiceListRequest) GetRelatedDataMask() v1.ServiceRelatedDataMask {
	if x != nil && x.RelatedDataMask != nil {
		return *x.RelatedDataMask
	}
	return v1.ServiceRelatedDataMask(0)
}

// get applicable service list response
type GetNewApplicableServiceListResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service list
	ServiceList   []*ServiceModel `protobuf:"bytes,1,rep,name=service_list,json=serviceList,proto3" json:"service_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNewApplicableServiceListResponse) Reset() {
	*x = GetNewApplicableServiceListResponse{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNewApplicableServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNewApplicableServiceListResponse) ProtoMessage() {}

func (x *GetNewApplicableServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNewApplicableServiceListResponse.ProtoReflect.Descriptor instead.
func (*GetNewApplicableServiceListResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetNewApplicableServiceListResponse) GetServiceList() []*ServiceModel {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// (-- api-linter: core::0231::request-names-field=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// Batch get by ids request
type BatchGetServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// related data mask。默认只加载基本信息
	RelatedDataMask *v1.ServiceRelatedDataMask `protobuf:"varint,2,opt,name=related_data_mask,json=relatedDataMask,proto3,enum=backend.proto.offering.v1.ServiceRelatedDataMask,oneof" json:"related_data_mask,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BatchGetServicesRequest) Reset() {
	*x = BatchGetServicesRequest{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesRequest) ProtoMessage() {}

func (x *BatchGetServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetServicesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchGetServicesRequest) GetRelatedDataMask() v1.ServiceRelatedDataMask {
	if x != nil && x.RelatedDataMask != nil {
		return *x.RelatedDataMask
	}
	return v1.ServiceRelatedDataMask(0)
}

// (-- api-linter: core::0231::response-resource-field=disabled
//
//	aip.dev/not-precedent: 自定义 model --)
//
// Batch get by ids response
type BatchGetServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Services
	Services      []*ServiceModel `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesResponse) Reset() {
	*x = BatchGetServicesResponse{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesResponse) ProtoMessage() {}

func (x *BatchGetServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{3}
}

func (x *BatchGetServicesResponse) GetServices() []*ServiceModel {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_backend_proto_offering_inner_grooming_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc = "" +
	"\n" +
	";backend/proto/offering/inner/grooming_service_service.proto\x12\x1cbackend.proto.offering.inner\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x18google/type/latlng.proto\x1a3backend/proto/offering/inner/grooming_service.proto\x1a'backend/proto/offering/v1/service.proto\x1a\x1bbuf/validate/validate.proto\"\xb1\x06\n" +
	"\"GetNewApplicableServiceListRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12^\n" +
	"\x11service_item_type\x18\x02 \x01(\x0e2-.backend.proto.offering.inner.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12$\n" +
	"\vbusiness_id\x18\x03 \x01(\x03H\x01R\n" +
	"businessId\x88\x01\x01\x12%\n" +
	"\x0eonly_available\x18\x04 \x01(\bR\ronlyAvailable\x12\x1a\n" +
	"\x06pet_id\x18\x05 \x01(\x03H\x02R\x05petId\x88\x01\x01\x12L\n" +
	"\fservice_type\x18\x06 \x01(\x0e2).backend.proto.offering.inner.ServiceTypeR\vserviceType\x12R\n" +
	"\x06filter\x18\a \x01(\v25.backend.proto.offering.inner.ServiceApplicableFilterH\x03R\x06filter\x88\x01\x01\x12\x1d\n" +
	"\akeyword\x18\b \x01(\tH\x04R\akeyword\x88\x01\x01\x12\x1f\n" +
	"\binactive\x18\n" +
	" \x01(\bH\x05R\binactive\x88\x01\x01\x12\x1d\n" +
	"\azipcode\x18\v \x01(\tH\x06R\azipcode\x88\x01\x01\x128\n" +
	"\n" +
	"coordinate\x18\f \x01(\v2\x13.google.type.LatLngH\aR\n" +
	"coordinate\x88\x01\x01\x12b\n" +
	"\x11related_data_mask\x18\r \x01(\x0e21.backend.proto.offering.v1.ServiceRelatedDataMaskH\bR\x0frelatedDataMask\x88\x01\x01B\x14\n" +
	"\x12_service_item_typeB\x0e\n" +
	"\f_business_idB\t\n" +
	"\a_pet_idB\t\n" +
	"\a_filterB\n" +
	"\n" +
	"\b_keywordB\v\n" +
	"\t_inactiveB\n" +
	"\n" +
	"\b_zipcodeB\r\n" +
	"\v_coordinateB\x14\n" +
	"\x12_related_data_mask\"t\n" +
	"#GetNewApplicableServiceListResponse\x12M\n" +
	"\fservice_list\x18\x01 \x03(\v2*.backend.proto.offering.inner.ServiceModelR\vserviceList\"\xba\x01\n" +
	"\x17BatchGetServicesRequest\x12%\n" +
	"\x03ids\x18\x01 \x03(\x03B\x13\xbaH\x10\x92\x01\r\b\x01\x10\xe8\a\x18\x01\"\x04\"\x02 \x00R\x03ids\x12b\n" +
	"\x11related_data_mask\x18\x02 \x01(\x0e21.backend.proto.offering.v1.ServiceRelatedDataMaskH\x00R\x0frelatedDataMask\x88\x01\x01B\x14\n" +
	"\x12_related_data_mask\"b\n" +
	"\x18BatchGetServicesResponse\x12F\n" +
	"\bservices\x18\x01 \x03(\v2*.backend.proto.offering.inner.ServiceModelR\bservices2\xc1\x02\n" +
	"\x16GroomingServiceService\x12\xa2\x01\n" +
	"\x1bGetNewApplicableServiceList\<EMAIL>\x1aA.backend.proto.offering.inner.GetNewApplicableServiceListResponse\x12\x81\x01\n" +
	"\x10BatchGetServices\x125.backend.proto.offering.inner.BatchGetServicesRequest\x1a6.backend.proto.offering.inner.BatchGetServicesResponseBv\n" +
	"&com.moego.backend.proto.offering.innerP\x01ZJgithub.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpbb\x06proto3"

var (
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData
}

var file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = []any{
	(*GetNewApplicableServiceListRequest)(nil),  // 0: backend.proto.offering.inner.GetNewApplicableServiceListRequest
	(*GetNewApplicableServiceListResponse)(nil), // 1: backend.proto.offering.inner.GetNewApplicableServiceListResponse
	(*BatchGetServicesRequest)(nil),             // 2: backend.proto.offering.inner.BatchGetServicesRequest
	(*BatchGetServicesResponse)(nil),            // 3: backend.proto.offering.inner.BatchGetServicesResponse
	(ServiceItemType)(0),                        // 4: backend.proto.offering.inner.ServiceItemType
	(ServiceType)(0),                            // 5: backend.proto.offering.inner.ServiceType
	(*ServiceApplicableFilter)(nil),             // 6: backend.proto.offering.inner.ServiceApplicableFilter
	(*latlng.LatLng)(nil),                       // 7: google.type.LatLng
	(v1.ServiceRelatedDataMask)(0),              // 8: backend.proto.offering.v1.ServiceRelatedDataMask
	(*ServiceModel)(nil),                        // 9: backend.proto.offering.inner.ServiceModel
}
var file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = []int32{
	4,  // 0: backend.proto.offering.inner.GetNewApplicableServiceListRequest.service_item_type:type_name -> backend.proto.offering.inner.ServiceItemType
	5,  // 1: backend.proto.offering.inner.GetNewApplicableServiceListRequest.service_type:type_name -> backend.proto.offering.inner.ServiceType
	6,  // 2: backend.proto.offering.inner.GetNewApplicableServiceListRequest.filter:type_name -> backend.proto.offering.inner.ServiceApplicableFilter
	7,  // 3: backend.proto.offering.inner.GetNewApplicableServiceListRequest.coordinate:type_name -> google.type.LatLng
	8,  // 4: backend.proto.offering.inner.GetNewApplicableServiceListRequest.related_data_mask:type_name -> backend.proto.offering.v1.ServiceRelatedDataMask
	9,  // 5: backend.proto.offering.inner.GetNewApplicableServiceListResponse.service_list:type_name -> backend.proto.offering.inner.ServiceModel
	8,  // 6: backend.proto.offering.inner.BatchGetServicesRequest.related_data_mask:type_name -> backend.proto.offering.v1.ServiceRelatedDataMask
	9,  // 7: backend.proto.offering.inner.BatchGetServicesResponse.services:type_name -> backend.proto.offering.inner.ServiceModel
	0,  // 8: backend.proto.offering.inner.GroomingServiceService.GetNewApplicableServiceList:input_type -> backend.proto.offering.inner.GetNewApplicableServiceListRequest
	2,  // 9: backend.proto.offering.inner.GroomingServiceService.BatchGetServices:input_type -> backend.proto.offering.inner.BatchGetServicesRequest
	1,  // 10: backend.proto.offering.inner.GroomingServiceService.GetNewApplicableServiceList:output_type -> backend.proto.offering.inner.GetNewApplicableServiceListResponse
	3,  // 11: backend.proto.offering.inner.GroomingServiceService.BatchGetServices:output_type -> backend.proto.offering.inner.BatchGetServicesResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_inner_grooming_service_service_proto_init() }
func file_backend_proto_offering_inner_grooming_service_service_proto_init() {
	if File_backend_proto_offering_inner_grooming_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_inner_grooming_service_proto_init()
	file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_inner_grooming_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_inner_grooming_service_service_proto = out.File
	file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = nil
	file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = nil
}
