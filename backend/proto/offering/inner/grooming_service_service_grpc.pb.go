// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/inner/grooming_service_service.proto

// api-linter: core::0131::response-message-name=disabled
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GroomingServiceService_GetNewApplicableServiceList_FullMethodName = "/backend.proto.offering.inner.GroomingServiceService/GetNewApplicableServiceList"
	GroomingServiceService_BatchGetServices_FullMethodName            = "/backend.proto.offering.inner.GroomingServiceService/BatchGetServices"
)

// GroomingServiceServiceClient is the client API for GroomingServiceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service for legacy grooming service operations.
type GroomingServiceServiceClient interface {
	// get applicable service list
	GetNewApplicableServiceList(ctx context.Context, in *GetNewApplicableServiceListRequest, opts ...grpc.CallOption) (*GetNewApplicableServiceListResponse, error)
	// Batch get by ids
	BatchGetServices(ctx context.Context, in *BatchGetServicesRequest, opts ...grpc.CallOption) (*BatchGetServicesResponse, error)
}

type groomingServiceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroomingServiceServiceClient(cc grpc.ClientConnInterface) GroomingServiceServiceClient {
	return &groomingServiceServiceClient{cc}
}

func (c *groomingServiceServiceClient) GetNewApplicableServiceList(ctx context.Context, in *GetNewApplicableServiceListRequest, opts ...grpc.CallOption) (*GetNewApplicableServiceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNewApplicableServiceListResponse)
	err := c.cc.Invoke(ctx, GroomingServiceService_GetNewApplicableServiceList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingServiceServiceClient) BatchGetServices(ctx context.Context, in *BatchGetServicesRequest, opts ...grpc.CallOption) (*BatchGetServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetServicesResponse)
	err := c.cc.Invoke(ctx, GroomingServiceService_BatchGetServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroomingServiceServiceServer is the server API for GroomingServiceService service.
// All implementations must embed UnimplementedGroomingServiceServiceServer
// for forward compatibility.
//
// Service for legacy grooming service operations.
type GroomingServiceServiceServer interface {
	// get applicable service list
	GetNewApplicableServiceList(context.Context, *GetNewApplicableServiceListRequest) (*GetNewApplicableServiceListResponse, error)
	// Batch get by ids
	BatchGetServices(context.Context, *BatchGetServicesRequest) (*BatchGetServicesResponse, error)
	mustEmbedUnimplementedGroomingServiceServiceServer()
}

// UnimplementedGroomingServiceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGroomingServiceServiceServer struct{}

func (UnimplementedGroomingServiceServiceServer) GetNewApplicableServiceList(context.Context, *GetNewApplicableServiceListRequest) (*GetNewApplicableServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewApplicableServiceList not implemented")
}
func (UnimplementedGroomingServiceServiceServer) BatchGetServices(context.Context, *BatchGetServicesRequest) (*BatchGetServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetServices not implemented")
}
func (UnimplementedGroomingServiceServiceServer) mustEmbedUnimplementedGroomingServiceServiceServer() {
}
func (UnimplementedGroomingServiceServiceServer) testEmbeddedByValue() {}

// UnsafeGroomingServiceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroomingServiceServiceServer will
// result in compilation errors.
type UnsafeGroomingServiceServiceServer interface {
	mustEmbedUnimplementedGroomingServiceServiceServer()
}

func RegisterGroomingServiceServiceServer(s grpc.ServiceRegistrar, srv GroomingServiceServiceServer) {
	// If the following call pancis, it indicates UnimplementedGroomingServiceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GroomingServiceService_ServiceDesc, srv)
}

func _GroomingServiceService_GetNewApplicableServiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewApplicableServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingServiceServiceServer).GetNewApplicableServiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroomingServiceService_GetNewApplicableServiceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingServiceServiceServer).GetNewApplicableServiceList(ctx, req.(*GetNewApplicableServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingServiceService_BatchGetServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingServiceServiceServer).BatchGetServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroomingServiceService_BatchGetServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingServiceServiceServer).BatchGetServices(ctx, req.(*BatchGetServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GroomingServiceService_ServiceDesc is the grpc.ServiceDesc for GroomingServiceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroomingServiceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.inner.GroomingServiceService",
	HandlerType: (*GroomingServiceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNewApplicableServiceList",
			Handler:    _GroomingServiceService_GetNewApplicableServiceList_Handler,
		},
		{
			MethodName: "BatchGetServices",
			Handler:    _GroomingServiceService_BatchGetServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/inner/grooming_service_service.proto",
}
