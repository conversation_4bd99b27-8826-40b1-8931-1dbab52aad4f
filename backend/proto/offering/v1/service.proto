syntax = "proto3";

package backend.proto.offering.v1;

import "google/type/latlng.proto";
import "backend/proto/offering/v1/common.proto";
import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
message Service {
  // Primary key ID of the service
  int64 id = 1;

  // The type of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization.
  int64 organization_id = 3;

  // The ID of the care type associated with this service.
  int64 care_type_id = 4;

  // The ID of the category this service.
  optional int64 category_id = 5;

  // Name of the service, unique within the same company
  string name = 6;

  // Optional description of the service
  optional string description = 7;

  // A color code associated with the service for UI purposes.
  string color_code = 8;

  // The sorting order of the service.
  optional int64 sort = 9;

  // A list of image URLs for the service.
  repeated string images = 10;

  // The offering source of the service.
  OfferingSource source = 11;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  Status status = 12;

  // Is deleted
  bool is_deleted = 13;

  // The type of the service.
  Type type = 14;

  // The price of the service.
  google.type.Money price = 15;

  // The related tax id of the service.
  int64 tax_id = 17;

  // The timestamp when the service was created.
  google.protobuf.Timestamp create_time = 20;
  // The timestamp when the service was last updated.
  google.protobuf.Timestamp update_time = 21;
  // The timestamp when the service was deleted.
  optional google.protobuf.Timestamp delete_time = 22;

  // The available business scope for this service.
  AvailableBusiness available_business = 23;

  // Additional service/addon scope configuration
  optional AdditionalService additional_service = 24;

  // Available pet type and breed configuration
  optional AvailablePetTypeBreed available_type_breed = 25;

  // Available pet size configuration
  optional AvailablePetSize available_pet_size = 26;

  // Available pet coat type configuration
  optional AvailableCoatType available_coat_type = 27;

  // Available pet code configuration
  optional AvailablePetCode available_pet_code = 28;

  // Available pet weight configuration
  optional AvailablePetWeight available_pet_weight = 29;

  // Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
  repeated BusinessStaffOverride business_staff_overrides = 30;

  // Pet override configuration (price, duration overrides for specific pets)
  repeated PetOverride pet_overrides = 31;

  // Service attributes for better frontend type safety
  optional ServiceAttributes attributes = 99;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Active
    ACTIVE = 1;
    // Inactive
    INACTIVE = 2;
  }

  // Service type.
  enum Type {
    // Unspecified.
    TYPE_UNSPECIFIED = 0;
    // Service.
    SERVICE = 1;
    // Add-On.
    ADD_ON = 2;
  }
}

// Additional service/addon scope configuration
message AdditionalService {
  // List of care type IDs to include all services under those care types
  repeated int64 additional_care_type_ids = 1 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 100,
    items: {
      int64: {gt: 0}
    }
  }];

  // List of specific additional service/addon IDs
  repeated int64 additional_service_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000,
    items: {
      int64: {gt: 0}
    }
  }];
}


// Defines a service auto rollover.
message AutoRollover {
  // Enabled auto rollover
  bool enabled = 1;
  
  // The ID of the target service.
  optional int64 target_service_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
  // The number of minutes after the max duration to trigger auto rollover.
  optional int32 after_minute = 3 [(buf.validate.field).int32 = {
    gte: 0
  }];
}

// Defines available business configuration for a service
message AvailableBusiness {
  // Whether the service is available for all businesses
  bool is_all = 1;
  
  // List of specific business IDs when is_all is false
  repeated int64 business_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Defines available staff configuration for a service
message AvailableStaff {
  // Whether the service is available for all staff
  bool is_all = 1;
  
  // List of specific staff IDs when is_all is false
  repeated int64 staff_ids = 2;
}

// Defines available lodging configuration for a service
message AvailableLodgingType {
  // Whether the service is available for all lodging
  bool is_all = 1;
  
  // List of specific lodging IDs when is_all is false
  repeated int64 lodging_type_ids = 2;
}

// Defines service attributes for better frontend type safety
message ServiceAttributes {
  // Duration of the service.
  optional int32 duration = 1 [(buf.validate.field).int32 = {
    gte: 0
  }];

  // Max duration of the service.
  optional int32 max_duration = 2 [(buf.validate.field).int32 = {
    gte: 0
  }];

  // Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
  optional AutoRollover auto_rollover = 3;
  
  // Available staff configuration. available staff will be used to schedule the service.
  optional AvailableStaff available_staff = 4;
  
  // Available lodging type configuration. available lodging type will be used to schedule the service.
  optional AvailableLodgingType available_lodging_type = 5;
  
  // Whether the addon requires staff (only for ADD_ON type services)
  optional bool is_required_staff = 6;

  // Result validity period configuration (for services that produce time-sensitive results)
  optional ResultValidityPeriod result_validity_period = 7;

  // Auto assign staff (for evaluation services)
  optional bool auto_assign_staff = 8;

  // Online booking alias (for evaluation services)
  optional string ob_alias = 9;

  // Prerequisite rule configuration (for services that have prerequisite services)
  optional PrerequisiteRule prerequisite_rule = 10;

  // The billing price unit of the service.
  optional PriceUnit price_unit = 12;

  // Default service configuration for single-day services
  optional DefaultService default_service = 13;

  // Default service configuration for conditional cross-day services
  optional ConditionalDefaultService conditional_default_service = 14;
}

// Defines prerequisite rule configuration for services that have prerequisite care types or services
message PrerequisiteRule {
  // Enabled for business
  optional bool enabled = 1;

  // Enabled for online booking
  optional bool ob_enabled = 2;

  // The prerequisite services
  repeated int64 service_ids = 3 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Defines result validity period configuration for time-sensitive services
message ResultValidityPeriod {
  // The validity period type
  ValidityPeriodType period_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // The validity duration (only used when period_type is CONDITIONAL)
  optional ValidityDuration duration = 2;

  // Result validity period type
  enum ValidityPeriodType {
    // Unspecified
    VALIDITY_PERIOD_TYPE_UNSPECIFIED = 0;
    // Always valid - results never expire
    ALWAYS_VALID = 1;
    // Conditional validity - clear 'Pass' status if the pet hasn't had relevant services for a specified period
    CONDITIONAL = 2;
  }
}

// Defines validity duration configuration
message ValidityDuration {
  // The duration value
  int32 value = 1 [(buf.validate.field).int32 = {
    gte: 1
  }];

  // The duration unit
  DurationUnit unit = 2 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // Duration unit for validity period
  enum DurationUnit {
    // Unspecified
    DURATION_UNIT_UNSPECIFIED = 0;
    // Days
    DAYS = 1;
    // Weeks
    WEEKS = 2;
    // Months
    MONTHS = 3;
    // Years
    YEARS = 4;
  }
}

// Available pet type and breed configuration
message AvailablePetTypeBreed {
  // Whether the service is available for all pet types and breeds
  bool is_all = 1;
  
  // List of specific pet type configurations when is_all is false
  repeated AvailablePetType available_pet_types = 2;
}

// Pet type configuration
message AvailablePetType {
  // The ID of the pet type
  int64 pet_type_id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];
  
  // Whether the service is available for all breeds of this pet type
  bool is_all = 2;
  
  // List of specific breed IDs when is_all is false
  repeated int64 breed_ids = 3 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Available pet size configuration
message AvailablePetSize {
  // Whether the service is available for all pet sizes
  bool is_all = 1;
  
  // List of specific pet size IDs when is_all is false
  repeated int64 pet_size_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Available pet coat type configuration
message AvailableCoatType {
  // Whether the service is available for all pet coat types
  bool is_all = 1;
  
  // List of specific pet coat type IDs when is_all is false
  repeated int64 coat_type_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Available pet code configuration
message AvailablePetCode {
  // The rule type for pet code filtering (only used when no_restriction is false)
  AvailabilityRuleType rule_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];
  
  // List of pet code IDs based on the rule type
  repeated int64 pet_code_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Available pet weight configuration
message AvailablePetWeight {
  // Whether the service is available for all pet weights
  bool is_all = 1;
  
  // List of specific pet weight ranges when is_all is false
  repeated PetWeightRange pet_weight_ranges = 2;
}

// Pet weight range configuration
message PetWeightRange {
  // Minimum weight in pounds (inclusive)
  double min_weight = 1 [(buf.validate.field).double = {
    gte: 0.0
  }];
  
  // Maximum weight in pounds (inclusive)
  double max_weight = 2 [(buf.validate.field).double = {
    gt: 0.0
  }];
}

// Defines default service configuration for single-day services
message DefaultService {
  // Whether default service functionality is enabled
  bool enabled = 1;

  // The default services to apply
  repeated int64 service_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000,
    items: {
      int64: {gt: 0}
    }
  }];
}

// Defines default service/addon configuration for conditional cross-day services
message ConditionalDefaultService {
  // Whether default service functionality is enabled
  bool enabled = 1;

  // The condition for triggering default services
  Condition condition = 2;

  // The default services/addons to apply
  repeated ApplyRule apply_rules = 3 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000
  }];

  // Defines the condition for triggering default services
  message Condition {
    // The minimum stay length (nights/days) to trigger default services
    int32 min_stay_length = 1 [(buf.validate.field).int32 = {
      gte: 1
    }];

    // The stay length unit
    StayUnit unit = 2 [(buf.validate.field) = {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }];
  }

  // Defines how a default service/addon should be applied
  message ApplyRule {
    // The service/addon ID to bundle
    int64 service_id = 1 [(buf.validate.field) = {
      int64: {gt: 0}
    }];

    // The date type for applying this service
    DateType date_type = 2 [(buf.validate.field) = {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }];

    // The quantity per day (only used for add-on)
    optional int32 quantity_per_day = 3 [(buf.validate.field).int32 = {
      gte: 1
    }];
  }

  // Date type for default service
  enum DateType {
    // Unspecified
    DATE_TYPE_UNSPECIFIED = 0;
    // Everyday except the checkout day
    EVERYDAY_EXCEPT_CHECKOUT_DAY = 1;
    // Everyday
    EVERYDAY = 4;
    // Everyday except the check-in day
    EVERYDAY_EXCEPT_CHECKIN_DAY = 5;
    // Last day
    LAST_DAY = 6;
    // First day
    FIRST_DAY = 7;
  }

  // Stay length unit for default service rules
  enum StayUnit {
    // Unspecified
    STAY_UNIT_UNSPECIFIED = 0;
    // Days
    DAYS = 1;
    // Nights
    NIGHTS = 2;
  }
}

// Business override and staff override
message BusinessStaffOverride {
  // Business override
  BusinessOverride business_override = 1;
  
  // Staff override
  repeated StaffOverride staff_overrides = 2;
}

// Business override configuration for service pricing and settings
message BusinessOverride {
  // The business ID this override applies to
  int64 business_id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Price
  optional google.type.Money price = 2;

   // Tax ID
   optional int64 tax_id = 3 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Duration
  optional int32 duration = 4 [(buf.validate.field).int32 = {
    gte: 0
  }];

  // Max duration
  optional int32 max_duration = 5 [(buf.validate.field).int32 = {
    gte: 0
  }];
}

// Staff override configuration for service pricing and settings
message StaffOverride {
  // The staff ID this override applies to
  int64 staff_id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // The business ID this staff override applies to
  int64 business_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Price
  optional google.type.Money price = 3;

  // Duration
  optional int32 duration = 4 [(buf.validate.field).int32 = {
    gte: 0
  }];
}

// Override values for price, duration, and tax settings
message OverrideValues {
  // Price override (optional)
  optional google.type.Money price = 1;

  // Duration override (optional, only applicable for daycare and grooming care types)
  optional int32 duration = 2 [(buf.validate.field).int32 = {
    gte: 0
  }];

  // Tax ID override (optional)
  optional int64 tax_id = 3 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Max duration override (optional)
  optional int32 max_duration = 4 [(buf.validate.field).int32 = {
    gte: 0
  }];
}

// Pet override configuration for service pricing and settings
message PetOverride {
  // pet override id
  int64 id = 1 [(buf.validate.field) = {
  int64: {gt: 0}
      }];

  // The pet ID this override applies to
  int64 pet_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // The service ID this pet override applies to
  int64 service_id = 3 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Price
  optional google.type.Money price = 4;

  // Duration override (optional, only applicable for daycare and grooming care types)
  optional int32 duration = 5;
}


// customized service
message CustomizedService {
  // service
  Service service = 1;
  // 该条 customized service 对应的 query condition
  CustomizedServiceQueryCondition condition = 2;
  // 根据 condition 查出来的 override 后的相关信息
  OverrideInfo override_info = 3;

  // after override info
  message OverrideInfo {
    // price
    google.type.Money price = 2;
    // tax id
    int64 tax_id = 3;
    // duration
    optional int32 duration = 4;
    // max duration
    optional int32 max_duration = 5;
    // price override type
    OverrideType price_override_type = 6;
    // duration override type
    OverrideType duration_override_type = 7;
  }
}

// customized service query condition
message CustomizedServiceQueryCondition {
  // 虚拟 id，标识一个 condition
  optional int64 virtual_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
  // service id
  int64 service_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
  // business id
  optional int64 business_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
  // pet id
  optional int64 pet_id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
  // staff id
  optional int64 staff_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
  // customer address zipcode
  optional string zipcode = 6 [(buf.validate.field) = { string: { max_len: 50 } }];
  // customer address coordinate
  optional google.type.LatLng coordinate = 7;
}

// Override type
enum OverrideType {
  // Unspecified
  OVERRIDE_TYPE_UNSPECIFIED = 0;
  // Business
  BUSINESS = 1;
  // Pet
  PET = 2;
  // Staff
  STAFF = 3;
  // Zone
  ZONE = 4;
}

// Pet availability scope
enum PetAvailabilityScopeType {
  // Unspecified
  PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED = 0;
  // All pet types and breeds
  ALL_TYPES_AND_BREEDS = 1;
  // All pet breeds for specific pet type
  SPECIFIC_TYPE = 2;
  // Specific pet breed
  SPECIFIC_BREED = 3;
  // All pet sizes
  ALL_SIZES = 11;
  // Specific pet size
  SPECIFIC_SIZE = 12;
  // All pet coat types
  ALL_COAT_TYPES = 21;
  // Specific pet coat type
  SPECIFIC_COAT_TYPE = 22;
  // All pet codes
  ALL_CODES = 31;
  // Specific pet code
  SPECIFIC_CODE = 32;
  // Exclude all pet codes
  EXCLUDE_ALL_CODES = 33;
  // Exclude specific pet code
  EXCLUDE_SPECIFIC_CODE = 34;
}

// Pet availability rule type for availability configuration
enum AvailabilityRuleType {
  // Unspecified
  AVAILABILITY_RULE_TYPE_UNSPECIFIED = 0;
  // No restriction
  NO_RESTRICTION = 1;
  // Include (whitelist) - only allow specified items
  INCLUDE = 2;
  // Exclude (blacklist) - exclude specified items
  EXCLUDE = 3;
}

// Price unit.
enum PriceUnit {
  // Unspecified.
  PRICE_UNIT_UNSPECIFIED = 0;
  // Per session
  PER_SESSION = 1;
  // Per night
  PER_NIGHT = 2;
  // Per hour
  PER_HOUR = 3;
  // Per day
  PER_DAY = 4;
}

// Service related data enum for controlling which related tables to load.
enum RelatedDataEnum {
  // Unspecified
  RELATED_DATA_ENUM_UNSPECIFIED = 0;
  // Load all related data
  ALL = 1;
  // Load available business scope
  AVAILABLE_BUSINESS_SCOPE = 2;
  // Load available staff configuration
  AVAILABLE_STAFF_SCOPE = 3;
  // Load available lodging type configuration
  AVAILABLE_LODGING_SCOPE = 4;
  // Load available pet type and breed configuration
  PET_AVAILABILITY = 5;
  // Load additional service configuration
  ADDITIONAL_SERVICE = 6;
  // Load business and staff overrides
  BUSINESS_STAFF_OVERRIDES = 7;
  // Load pet overrides
  PET_OVERRIDES = 8;
  // Load auto rollover configuration
  AUTO_ROLLOVER_CONFIGURATION = 9;
  // Load service attributes
  // only contains fields in attribute table, not include table attributes
  ATTRIBUTES = 10;
}