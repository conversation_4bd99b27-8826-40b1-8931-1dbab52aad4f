// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/addon_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AddOnService_CreateAddOn_FullMethodName         = "/backend.proto.offering.v1.AddOnService/CreateAddOn"
	AddOnService_GetAddOn_FullMethodName            = "/backend.proto.offering.v1.AddOnService/GetAddOn"
	AddOnService_UpdateAddOn_FullMethodName         = "/backend.proto.offering.v1.AddOnService/UpdateAddOn"
	AddOnService_DeleteAddOn_FullMethodName         = "/backend.proto.offering.v1.AddOnService/DeleteAddOn"
	AddOnService_ListAddOns_FullMethodName          = "/backend.proto.offering.v1.AddOnService/ListAddOns"
	AddOnService_ListAvailableAddOns_FullMethodName = "/backend.proto.offering.v1.AddOnService/ListAvailableAddOns"
	AddOnService_BatchUpdateAddOns_FullMethodName   = "/backend.proto.offering.v1.AddOnService/BatchUpdateAddOns"
	AddOnService_ListAddOnCategories_FullMethodName = "/backend.proto.offering.v1.AddOnService/ListAddOnCategories"
	AddOnService_SaveAddOnCategories_FullMethodName = "/backend.proto.offering.v1.AddOnService/SaveAddOnCategories"
)

// AddOnServiceClient is the client API for AddOnService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing AddOn resources.
type AddOnServiceClient interface {
	// 创建一个 AddOn
	CreateAddOn(ctx context.Context, in *CreateAddOnRequest, opts ...grpc.CallOption) (*CreateAddOnResponse, error)
	// 获取 AddOn
	GetAddOn(ctx context.Context, in *GetAddOnRequest, opts ...grpc.CallOption) (*GetAddOnResponse, error)
	// 更新 AddOn
	UpdateAddOn(ctx context.Context, in *UpdateAddOnRequest, opts ...grpc.CallOption) (*UpdateAddOnResponse, error)
	// 删除 AddOn
	DeleteAddOn(ctx context.Context, in *DeleteAddOnRequest, opts ...grpc.CallOption) (*DeleteAddOnResponse, error)
	// 列表查询 AddOn
	ListAddOns(ctx context.Context, in *ListAddOnsRequest, opts ...grpc.CallOption) (*ListAddOnsResponse, error)
	// ListAvailableAddOns 全场景查询可用 AddOn 列表。同 ListAvailableServices 接口
	ListAvailableAddOns(ctx context.Context, in *ListAvailableAddOnsRequest, opts ...grpc.CallOption) (*ListAvailableAddOnsResponse, error)
	// 批量更新 AddOn
	BatchUpdateAddOns(ctx context.Context, in *BatchUpdateAddOnsRequest, opts ...grpc.CallOption) (*BatchUpdateAddOnsResponse, error)
	// 获取 AddOn 分类列表
	ListAddOnCategories(ctx context.Context, in *ListAddOnCategoriesRequest, opts ...grpc.CallOption) (*ListAddOnCategoriesResponse, error)
	// 保存 AddOn 分类
	SaveAddOnCategories(ctx context.Context, in *SaveAddOnCategoriesRequest, opts ...grpc.CallOption) (*SaveAddOnCategoriesResponse, error)
}

type addOnServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAddOnServiceClient(cc grpc.ClientConnInterface) AddOnServiceClient {
	return &addOnServiceClient{cc}
}

func (c *addOnServiceClient) CreateAddOn(ctx context.Context, in *CreateAddOnRequest, opts ...grpc.CallOption) (*CreateAddOnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAddOnResponse)
	err := c.cc.Invoke(ctx, AddOnService_CreateAddOn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) GetAddOn(ctx context.Context, in *GetAddOnRequest, opts ...grpc.CallOption) (*GetAddOnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAddOnResponse)
	err := c.cc.Invoke(ctx, AddOnService_GetAddOn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) UpdateAddOn(ctx context.Context, in *UpdateAddOnRequest, opts ...grpc.CallOption) (*UpdateAddOnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAddOnResponse)
	err := c.cc.Invoke(ctx, AddOnService_UpdateAddOn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) DeleteAddOn(ctx context.Context, in *DeleteAddOnRequest, opts ...grpc.CallOption) (*DeleteAddOnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAddOnResponse)
	err := c.cc.Invoke(ctx, AddOnService_DeleteAddOn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) ListAddOns(ctx context.Context, in *ListAddOnsRequest, opts ...grpc.CallOption) (*ListAddOnsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAddOnsResponse)
	err := c.cc.Invoke(ctx, AddOnService_ListAddOns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) ListAvailableAddOns(ctx context.Context, in *ListAvailableAddOnsRequest, opts ...grpc.CallOption) (*ListAvailableAddOnsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAvailableAddOnsResponse)
	err := c.cc.Invoke(ctx, AddOnService_ListAvailableAddOns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) BatchUpdateAddOns(ctx context.Context, in *BatchUpdateAddOnsRequest, opts ...grpc.CallOption) (*BatchUpdateAddOnsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateAddOnsResponse)
	err := c.cc.Invoke(ctx, AddOnService_BatchUpdateAddOns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) ListAddOnCategories(ctx context.Context, in *ListAddOnCategoriesRequest, opts ...grpc.CallOption) (*ListAddOnCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAddOnCategoriesResponse)
	err := c.cc.Invoke(ctx, AddOnService_ListAddOnCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addOnServiceClient) SaveAddOnCategories(ctx context.Context, in *SaveAddOnCategoriesRequest, opts ...grpc.CallOption) (*SaveAddOnCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveAddOnCategoriesResponse)
	err := c.cc.Invoke(ctx, AddOnService_SaveAddOnCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AddOnServiceServer is the server API for AddOnService service.
// All implementations must embed UnimplementedAddOnServiceServer
// for forward compatibility.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing AddOn resources.
type AddOnServiceServer interface {
	// 创建一个 AddOn
	CreateAddOn(context.Context, *CreateAddOnRequest) (*CreateAddOnResponse, error)
	// 获取 AddOn
	GetAddOn(context.Context, *GetAddOnRequest) (*GetAddOnResponse, error)
	// 更新 AddOn
	UpdateAddOn(context.Context, *UpdateAddOnRequest) (*UpdateAddOnResponse, error)
	// 删除 AddOn
	DeleteAddOn(context.Context, *DeleteAddOnRequest) (*DeleteAddOnResponse, error)
	// 列表查询 AddOn
	ListAddOns(context.Context, *ListAddOnsRequest) (*ListAddOnsResponse, error)
	// ListAvailableAddOns 全场景查询可用 AddOn 列表。同 ListAvailableServices 接口
	ListAvailableAddOns(context.Context, *ListAvailableAddOnsRequest) (*ListAvailableAddOnsResponse, error)
	// 批量更新 AddOn
	BatchUpdateAddOns(context.Context, *BatchUpdateAddOnsRequest) (*BatchUpdateAddOnsResponse, error)
	// 获取 AddOn 分类列表
	ListAddOnCategories(context.Context, *ListAddOnCategoriesRequest) (*ListAddOnCategoriesResponse, error)
	// 保存 AddOn 分类
	SaveAddOnCategories(context.Context, *SaveAddOnCategoriesRequest) (*SaveAddOnCategoriesResponse, error)
	mustEmbedUnimplementedAddOnServiceServer()
}

// UnimplementedAddOnServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAddOnServiceServer struct{}

func (UnimplementedAddOnServiceServer) CreateAddOn(context.Context, *CreateAddOnRequest) (*CreateAddOnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAddOn not implemented")
}
func (UnimplementedAddOnServiceServer) GetAddOn(context.Context, *GetAddOnRequest) (*GetAddOnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddOn not implemented")
}
func (UnimplementedAddOnServiceServer) UpdateAddOn(context.Context, *UpdateAddOnRequest) (*UpdateAddOnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddOn not implemented")
}
func (UnimplementedAddOnServiceServer) DeleteAddOn(context.Context, *DeleteAddOnRequest) (*DeleteAddOnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddOn not implemented")
}
func (UnimplementedAddOnServiceServer) ListAddOns(context.Context, *ListAddOnsRequest) (*ListAddOnsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddOns not implemented")
}
func (UnimplementedAddOnServiceServer) ListAvailableAddOns(context.Context, *ListAvailableAddOnsRequest) (*ListAvailableAddOnsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableAddOns not implemented")
}
func (UnimplementedAddOnServiceServer) BatchUpdateAddOns(context.Context, *BatchUpdateAddOnsRequest) (*BatchUpdateAddOnsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateAddOns not implemented")
}
func (UnimplementedAddOnServiceServer) ListAddOnCategories(context.Context, *ListAddOnCategoriesRequest) (*ListAddOnCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddOnCategories not implemented")
}
func (UnimplementedAddOnServiceServer) SaveAddOnCategories(context.Context, *SaveAddOnCategoriesRequest) (*SaveAddOnCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAddOnCategories not implemented")
}
func (UnimplementedAddOnServiceServer) mustEmbedUnimplementedAddOnServiceServer() {}
func (UnimplementedAddOnServiceServer) testEmbeddedByValue()                      {}

// UnsafeAddOnServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AddOnServiceServer will
// result in compilation errors.
type UnsafeAddOnServiceServer interface {
	mustEmbedUnimplementedAddOnServiceServer()
}

func RegisterAddOnServiceServer(s grpc.ServiceRegistrar, srv AddOnServiceServer) {
	// If the following call pancis, it indicates UnimplementedAddOnServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AddOnService_ServiceDesc, srv)
}

func _AddOnService_CreateAddOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAddOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).CreateAddOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_CreateAddOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).CreateAddOn(ctx, req.(*CreateAddOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_GetAddOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).GetAddOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_GetAddOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).GetAddOn(ctx, req.(*GetAddOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_UpdateAddOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAddOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).UpdateAddOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_UpdateAddOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).UpdateAddOn(ctx, req.(*UpdateAddOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_DeleteAddOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).DeleteAddOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_DeleteAddOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).DeleteAddOn(ctx, req.(*DeleteAddOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_ListAddOns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddOnsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).ListAddOns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_ListAddOns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).ListAddOns(ctx, req.(*ListAddOnsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_ListAvailableAddOns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableAddOnsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).ListAvailableAddOns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_ListAvailableAddOns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).ListAvailableAddOns(ctx, req.(*ListAvailableAddOnsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_BatchUpdateAddOns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateAddOnsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).BatchUpdateAddOns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_BatchUpdateAddOns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).BatchUpdateAddOns(ctx, req.(*BatchUpdateAddOnsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_ListAddOnCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddOnCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).ListAddOnCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_ListAddOnCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).ListAddOnCategories(ctx, req.(*ListAddOnCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddOnService_SaveAddOnCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAddOnCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddOnServiceServer).SaveAddOnCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddOnService_SaveAddOnCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddOnServiceServer).SaveAddOnCategories(ctx, req.(*SaveAddOnCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AddOnService_ServiceDesc is the grpc.ServiceDesc for AddOnService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AddOnService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.AddOnService",
	HandlerType: (*AddOnServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAddOn",
			Handler:    _AddOnService_CreateAddOn_Handler,
		},
		{
			MethodName: "GetAddOn",
			Handler:    _AddOnService_GetAddOn_Handler,
		},
		{
			MethodName: "UpdateAddOn",
			Handler:    _AddOnService_UpdateAddOn_Handler,
		},
		{
			MethodName: "DeleteAddOn",
			Handler:    _AddOnService_DeleteAddOn_Handler,
		},
		{
			MethodName: "ListAddOns",
			Handler:    _AddOnService_ListAddOns_Handler,
		},
		{
			MethodName: "ListAvailableAddOns",
			Handler:    _AddOnService_ListAvailableAddOns_Handler,
		},
		{
			MethodName: "BatchUpdateAddOns",
			Handler:    _AddOnService_BatchUpdateAddOns_Handler,
		},
		{
			MethodName: "ListAddOnCategories",
			Handler:    _AddOnService_ListAddOnCategories_Handler,
		},
		{
			MethodName: "SaveAddOnCategories",
			Handler:    _AddOnService_SaveAddOnCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/addon_service.proto",
}
