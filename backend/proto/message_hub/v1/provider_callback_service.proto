syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

import "buf/validate/validate.proto";

// ProviderCallbackService handles callbacks from different providers.
service ProviderCallbackService {
  // HandleAwsMessagingStatusCallback receives status updates from AWS.
  rpc HandleAwsMessagingStatusCallback(HandleAwsMessagingStatusCallbackRequest) returns (HandleAwsMessagingStatusCallbackResponse);

  // HandleTwilioSmsStatusCallback receives status updates from Twilio.
  // see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
  rpc HandleTwilioSmsStatusCallback(HandleTwilioSmsStatusCallbackRequest) returns (HandleTwilioSmsStatusCallbackResponse);
}

// HandleAwsMessagingStatusCallbackRequest defines the structure for AWS Pinpoint SMS/Voice callback.
// see: https://docs.aws.amazon.com/sms-voice/latest/userguide/configuration-sets-event-format.html
message HandleAwsMessagingStatusCallbackRequest {
  // The type of event.
  string event_type = 1;
  // The version of the event.
  string event_version = 2;
  // The time when the event was reported, shown as Unix time in milliseconds.
  int64 event_timestamp = 3;
  // True if this is the final status for the message.
  // There are intermediate message statuses and it can take up to 72 hours for the final message status to be received.
  bool is_final = 4;
  // The phone number that sent the message.
  optional string origination_phone_number = 5;
  // The recipient's phone number.
  optional string destination_phone_number = 6;
  // The country that's associated with the recipient's phone number, shown in ISO 3166-1 alpha-2 format.
  optional string iso_country_code = 7;
  // Indicates if the message was sent internationally.
  optional bool is_international_send = 8;
  // Mobile Country Codes identifies the country which a phone number belongs to. This field is optional and may not be present.
  optional string mcc = 9;
  // Mobile Network Codes identifies a mobile network operator. This field is optional and may not be present.
  optional string mnc = 10;
  // The name of the carrier.
  optional string carrier_name = 11;
  // The unique ID that AWS End User Messaging SMS generates when it accepts the message.
  string message_id = 12;
  // The time when the SMS message request was received, shown as Unix time in milliseconds.
  int64 message_request_timestamp = 13;
  // The encoding of the message. Possible values are GSM and Unicode.
  // For more information on message encoding, see https://docs.aws.amazon.com/sms-voice/latest/userguide/sms-limitations-character.html.
  string message_encoding = 14;
  // The type of message. Possible values are Promotional and Transactional.
  string message_type = 15;
  // The status of the message (e.g., DELIVERED).
  string message_status = 16;
  // A description of the message status.
  string message_status_description = 17;
  // The number of message parts that AWS End User Messaging SMS created in order to send the message.
  optional int32 total_message_parts = 18;
  // The total price for the message.
  optional double total_message_price = 19;
  // The total cost of carrier fees for a message.
  optional double total_carrier_fee = 20;
  // context (custom data)
  AwsMessagingStatusCallbackContext context = 21;
}

// aws callback context
message AwsMessagingStatusCallbackContext {
  // id
  string id = 1;
}

// An empty response
message HandleAwsMessagingStatusCallbackResponse {}

// Represents the data received from a Twilio callback.
message HandleTwilioSmsStatusCallbackRequest {
  // The unique identifier for the message.
  string message_sid = 1;
  // The status of the message (e.g., sent, delivered, failed).
  string message_status = 2;
  // The recipient's phone number.
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  string to = 3;
  // The sender's phone number (the Twilio number).
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  // (-- api-linter: core::0140::reserved-words=disabled
  //     aip.dev/not-precedent: Twilio use this field name --)
  string from = 4;
  // The Twilio account SID.
  string account_sid = 5;
  // The error code if the message failed.
  optional string error_code = 6;

  // Custom twilio callback data set by message hub
  CustomTwilioCallbackData custom = 7;
}

// An empty response, as Twilio typically expects a 200 OK.
message HandleTwilioSmsStatusCallbackResponse {}

// custom twilio callback data
message CustomTwilioCallbackData {
  // id
  string id = 1 [(buf.validate.field).string.min_len = 1];
}