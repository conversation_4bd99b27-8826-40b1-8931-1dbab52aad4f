load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "messagehubpb_proto",
    srcs = [
        "callback.proto",
        "callback_service.proto",
        "message_hub_service.proto",
        "provider_callback_service.proto",
        "send_state.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@googleapis//google/type:phone_number_proto",
    ],
)

go_proto_library(
    name = "messagehubpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1",
    proto = ":messagehubpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@org_golang_google_genproto//googleapis/type/phone_number",
    ],
)

go_library(
    name = "message_hub",
    embed = [":messagehubpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1",
    visibility = ["//visibility:public"],
)
