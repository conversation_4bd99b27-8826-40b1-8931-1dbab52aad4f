// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/provider_callback_service.proto

package messagehubpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// HandleAwsMessagingStatusCallbackRequest defines the structure for AWS Pinpoint SMS/Voice callback.
// see: https://docs.aws.amazon.com/sms-voice/latest/userguide/configuration-sets-event-format.html
type HandleAwsMessagingStatusCallbackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of event.
	EventType string `protobuf:"bytes,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	// The version of the event.
	EventVersion string `protobuf:"bytes,2,opt,name=event_version,json=eventVersion,proto3" json:"event_version,omitempty"`
	// The time when the event was reported, shown as Unix time in milliseconds.
	EventTimestamp int64 `protobuf:"varint,3,opt,name=event_timestamp,json=eventTimestamp,proto3" json:"event_timestamp,omitempty"`
	// True if this is the final status for the message.
	// There are intermediate message statuses and it can take up to 72 hours for the final message status to be received.
	IsFinal bool `protobuf:"varint,4,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	// The phone number that sent the message.
	OriginationPhoneNumber *string `protobuf:"bytes,5,opt,name=origination_phone_number,json=originationPhoneNumber,proto3,oneof" json:"origination_phone_number,omitempty"`
	// The recipient's phone number.
	DestinationPhoneNumber *string `protobuf:"bytes,6,opt,name=destination_phone_number,json=destinationPhoneNumber,proto3,oneof" json:"destination_phone_number,omitempty"`
	// The country that's associated with the recipient's phone number, shown in ISO 3166-1 alpha-2 format.
	IsoCountryCode *string `protobuf:"bytes,7,opt,name=iso_country_code,json=isoCountryCode,proto3,oneof" json:"iso_country_code,omitempty"`
	// Indicates if the message was sent internationally.
	IsInternationalSend *bool `protobuf:"varint,8,opt,name=is_international_send,json=isInternationalSend,proto3,oneof" json:"is_international_send,omitempty"`
	// Mobile Country Codes identifies the country which a phone number belongs to. This field is optional and may not be present.
	Mcc *string `protobuf:"bytes,9,opt,name=mcc,proto3,oneof" json:"mcc,omitempty"`
	// Mobile Network Codes identifies a mobile network operator. This field is optional and may not be present.
	Mnc *string `protobuf:"bytes,10,opt,name=mnc,proto3,oneof" json:"mnc,omitempty"`
	// The name of the carrier.
	CarrierName *string `protobuf:"bytes,11,opt,name=carrier_name,json=carrierName,proto3,oneof" json:"carrier_name,omitempty"`
	// The unique ID that AWS End User Messaging SMS generates when it accepts the message.
	MessageId string `protobuf:"bytes,12,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// The time when the SMS message request was received, shown as Unix time in milliseconds.
	MessageRequestTimestamp int64 `protobuf:"varint,13,opt,name=message_request_timestamp,json=messageRequestTimestamp,proto3" json:"message_request_timestamp,omitempty"`
	// The encoding of the message. Possible values are GSM and Unicode.
	// For more information on message encoding, see https://docs.aws.amazon.com/sms-voice/latest/userguide/sms-limitations-character.html.
	MessageEncoding string `protobuf:"bytes,14,opt,name=message_encoding,json=messageEncoding,proto3" json:"message_encoding,omitempty"`
	// The type of message. Possible values are Promotional and Transactional.
	MessageType string `protobuf:"bytes,15,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// The status of the message (e.g., DELIVERED).
	MessageStatus string `protobuf:"bytes,16,opt,name=message_status,json=messageStatus,proto3" json:"message_status,omitempty"`
	// A description of the message status.
	MessageStatusDescription string `protobuf:"bytes,17,opt,name=message_status_description,json=messageStatusDescription,proto3" json:"message_status_description,omitempty"`
	// The number of message parts that AWS End User Messaging SMS created in order to send the message.
	TotalMessageParts *int32 `protobuf:"varint,18,opt,name=total_message_parts,json=totalMessageParts,proto3,oneof" json:"total_message_parts,omitempty"`
	// The total price for the message.
	TotalMessagePrice *float64 `protobuf:"fixed64,19,opt,name=total_message_price,json=totalMessagePrice,proto3,oneof" json:"total_message_price,omitempty"`
	// The total cost of carrier fees for a message.
	TotalCarrierFee *float64 `protobuf:"fixed64,20,opt,name=total_carrier_fee,json=totalCarrierFee,proto3,oneof" json:"total_carrier_fee,omitempty"`
	// context (custom data)
	Context       *AwsMessagingStatusCallbackContext `protobuf:"bytes,21,opt,name=context,proto3" json:"context,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleAwsMessagingStatusCallbackRequest) Reset() {
	*x = HandleAwsMessagingStatusCallbackRequest{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleAwsMessagingStatusCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleAwsMessagingStatusCallbackRequest) ProtoMessage() {}

func (x *HandleAwsMessagingStatusCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleAwsMessagingStatusCallbackRequest.ProtoReflect.Descriptor instead.
func (*HandleAwsMessagingStatusCallbackRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetEventVersion() string {
	if x != nil {
		return x.EventVersion
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetEventTimestamp() int64 {
	if x != nil {
		return x.EventTimestamp
	}
	return 0
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetOriginationPhoneNumber() string {
	if x != nil && x.OriginationPhoneNumber != nil {
		return *x.OriginationPhoneNumber
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetDestinationPhoneNumber() string {
	if x != nil && x.DestinationPhoneNumber != nil {
		return *x.DestinationPhoneNumber
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetIsoCountryCode() string {
	if x != nil && x.IsoCountryCode != nil {
		return *x.IsoCountryCode
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetIsInternationalSend() bool {
	if x != nil && x.IsInternationalSend != nil {
		return *x.IsInternationalSend
	}
	return false
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMcc() string {
	if x != nil && x.Mcc != nil {
		return *x.Mcc
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMnc() string {
	if x != nil && x.Mnc != nil {
		return *x.Mnc
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetCarrierName() string {
	if x != nil && x.CarrierName != nil {
		return *x.CarrierName
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageRequestTimestamp() int64 {
	if x != nil {
		return x.MessageRequestTimestamp
	}
	return 0
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageEncoding() string {
	if x != nil {
		return x.MessageEncoding
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageStatus() string {
	if x != nil {
		return x.MessageStatus
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetMessageStatusDescription() string {
	if x != nil {
		return x.MessageStatusDescription
	}
	return ""
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetTotalMessageParts() int32 {
	if x != nil && x.TotalMessageParts != nil {
		return *x.TotalMessageParts
	}
	return 0
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetTotalMessagePrice() float64 {
	if x != nil && x.TotalMessagePrice != nil {
		return *x.TotalMessagePrice
	}
	return 0
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetTotalCarrierFee() float64 {
	if x != nil && x.TotalCarrierFee != nil {
		return *x.TotalCarrierFee
	}
	return 0
}

func (x *HandleAwsMessagingStatusCallbackRequest) GetContext() *AwsMessagingStatusCallbackContext {
	if x != nil {
		return x.Context
	}
	return nil
}

// aws callback context
type AwsMessagingStatusCallbackContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AwsMessagingStatusCallbackContext) Reset() {
	*x = AwsMessagingStatusCallbackContext{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AwsMessagingStatusCallbackContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwsMessagingStatusCallbackContext) ProtoMessage() {}

func (x *AwsMessagingStatusCallbackContext) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwsMessagingStatusCallbackContext.ProtoReflect.Descriptor instead.
func (*AwsMessagingStatusCallbackContext) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{1}
}

func (x *AwsMessagingStatusCallbackContext) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// An empty response
type HandleAwsMessagingStatusCallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleAwsMessagingStatusCallbackResponse) Reset() {
	*x = HandleAwsMessagingStatusCallbackResponse{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleAwsMessagingStatusCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleAwsMessagingStatusCallbackResponse) ProtoMessage() {}

func (x *HandleAwsMessagingStatusCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleAwsMessagingStatusCallbackResponse.ProtoReflect.Descriptor instead.
func (*HandleAwsMessagingStatusCallbackResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{2}
}

// Represents the data received from a Twilio callback.
type HandleTwilioSmsStatusCallbackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier for the message.
	MessageSid string `protobuf:"bytes,1,opt,name=message_sid,json=messageSid,proto3" json:"message_sid,omitempty"`
	// The status of the message (e.g., sent, delivered, failed).
	MessageStatus string `protobuf:"bytes,2,opt,name=message_status,json=messageStatus,proto3" json:"message_status,omitempty"`
	// The recipient's phone number.
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	To string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	// The sender's phone number (the Twilio number).
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	//
	// (-- api-linter: core::0140::reserved-words=disabled
	//
	//	aip.dev/not-precedent: Twilio use this field name --)
	From string `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	// The Twilio account SID.
	AccountSid string `protobuf:"bytes,5,opt,name=account_sid,json=accountSid,proto3" json:"account_sid,omitempty"`
	// The error code if the message failed.
	ErrorCode *string `protobuf:"bytes,6,opt,name=error_code,json=errorCode,proto3,oneof" json:"error_code,omitempty"`
	// Custom twilio callback data set by message hub
	Custom        *CustomTwilioCallbackData `protobuf:"bytes,7,opt,name=custom,proto3" json:"custom,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleTwilioSmsStatusCallbackRequest) Reset() {
	*x = HandleTwilioSmsStatusCallbackRequest{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleTwilioSmsStatusCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleTwilioSmsStatusCallbackRequest) ProtoMessage() {}

func (x *HandleTwilioSmsStatusCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleTwilioSmsStatusCallbackRequest.ProtoReflect.Descriptor instead.
func (*HandleTwilioSmsStatusCallbackRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{3}
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetMessageSid() string {
	if x != nil {
		return x.MessageSid
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetMessageStatus() string {
	if x != nil {
		return x.MessageStatus
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetAccountSid() string {
	if x != nil {
		return x.AccountSid
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetErrorCode() string {
	if x != nil && x.ErrorCode != nil {
		return *x.ErrorCode
	}
	return ""
}

func (x *HandleTwilioSmsStatusCallbackRequest) GetCustom() *CustomTwilioCallbackData {
	if x != nil {
		return x.Custom
	}
	return nil
}

// An empty response, as Twilio typically expects a 200 OK.
type HandleTwilioSmsStatusCallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleTwilioSmsStatusCallbackResponse) Reset() {
	*x = HandleTwilioSmsStatusCallbackResponse{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleTwilioSmsStatusCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleTwilioSmsStatusCallbackResponse) ProtoMessage() {}

func (x *HandleTwilioSmsStatusCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleTwilioSmsStatusCallbackResponse.ProtoReflect.Descriptor instead.
func (*HandleTwilioSmsStatusCallbackResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{4}
}

// custom twilio callback data
type CustomTwilioCallbackData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomTwilioCallbackData) Reset() {
	*x = CustomTwilioCallbackData{}
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomTwilioCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomTwilioCallbackData) ProtoMessage() {}

func (x *CustomTwilioCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomTwilioCallbackData.ProtoReflect.Descriptor instead.
func (*CustomTwilioCallbackData) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP(), []int{5}
}

func (x *CustomTwilioCallbackData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_backend_proto_message_hub_v1_provider_callback_service_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDesc = "" +
	"\n" +
	"<backend/proto/message_hub/v1/provider_callback_service.proto\x12\x1cbackend.proto.message_hub.v1\x1a\x1bbuf/validate/validate.proto\"\xc1\t\n" +
	"'HandleAwsMessagingStatusCallbackRequest\x12\x1d\n" +
	"\n" +
	"event_type\x18\x01 \x01(\tR\teventType\x12#\n" +
	"\revent_version\x18\x02 \x01(\tR\feventVersion\x12'\n" +
	"\x0fevent_timestamp\x18\x03 \x01(\x03R\x0eeventTimestamp\x12\x19\n" +
	"\bis_final\x18\x04 \x01(\bR\aisFinal\x12=\n" +
	"\x18origination_phone_number\x18\x05 \x01(\tH\x00R\x16originationPhoneNumber\x88\x01\x01\x12=\n" +
	"\x18destination_phone_number\x18\x06 \x01(\tH\x01R\x16destinationPhoneNumber\x88\x01\x01\x12-\n" +
	"\x10iso_country_code\x18\a \x01(\tH\x02R\x0eisoCountryCode\x88\x01\x01\x127\n" +
	"\x15is_international_send\x18\b \x01(\bH\x03R\x13isInternationalSend\x88\x01\x01\x12\x15\n" +
	"\x03mcc\x18\t \x01(\tH\x04R\x03mcc\x88\x01\x01\x12\x15\n" +
	"\x03mnc\x18\n" +
	" \x01(\tH\x05R\x03mnc\x88\x01\x01\x12&\n" +
	"\fcarrier_name\x18\v \x01(\tH\x06R\vcarrierName\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"message_id\x18\f \x01(\tR\tmessageId\x12:\n" +
	"\x19message_request_timestamp\x18\r \x01(\x03R\x17messageRequestTimestamp\x12)\n" +
	"\x10message_encoding\x18\x0e \x01(\tR\x0fmessageEncoding\x12!\n" +
	"\fmessage_type\x18\x0f \x01(\tR\vmessageType\x12%\n" +
	"\x0emessage_status\x18\x10 \x01(\tR\rmessageStatus\x12<\n" +
	"\x1amessage_status_description\x18\x11 \x01(\tR\x18messageStatusDescription\x123\n" +
	"\x13total_message_parts\x18\x12 \x01(\x05H\aR\x11totalMessageParts\x88\x01\x01\x123\n" +
	"\x13total_message_price\x18\x13 \x01(\x01H\bR\x11totalMessagePrice\x88\x01\x01\x12/\n" +
	"\x11total_carrier_fee\x18\x14 \x01(\x01H\tR\x0ftotalCarrierFee\x88\x01\x01\x12Y\n" +
	"\acontext\x18\x15 \x01(\v2?.backend.proto.message_hub.v1.AwsMessagingStatusCallbackContextR\acontextB\x1b\n" +
	"\x19_origination_phone_numberB\x1b\n" +
	"\x19_destination_phone_numberB\x13\n" +
	"\x11_iso_country_codeB\x18\n" +
	"\x16_is_international_sendB\x06\n" +
	"\x04_mccB\x06\n" +
	"\x04_mncB\x0f\n" +
	"\r_carrier_nameB\x16\n" +
	"\x14_total_message_partsB\x16\n" +
	"\x14_total_message_priceB\x14\n" +
	"\x12_total_carrier_fee\"3\n" +
	"!AwsMessagingStatusCallbackContext\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"*\n" +
	"(HandleAwsMessagingStatusCallbackResponse\"\xb6\x02\n" +
	"$HandleTwilioSmsStatusCallbackRequest\x12\x1f\n" +
	"\vmessage_sid\x18\x01 \x01(\tR\n" +
	"messageSid\x12%\n" +
	"\x0emessage_status\x18\x02 \x01(\tR\rmessageStatus\x12\x0e\n" +
	"\x02to\x18\x03 \x01(\tR\x02to\x12\x12\n" +
	"\x04from\x18\x04 \x01(\tR\x04from\x12\x1f\n" +
	"\vaccount_sid\x18\x05 \x01(\tR\n" +
	"accountSid\x12\"\n" +
	"\n" +
	"error_code\x18\x06 \x01(\tH\x00R\terrorCode\x88\x01\x01\x12N\n" +
	"\x06custom\x18\a \x01(\v26.backend.proto.message_hub.v1.CustomTwilioCallbackDataR\x06customB\r\n" +
	"\v_error_code\"'\n" +
	"%HandleTwilioSmsStatusCallbackResponse\"3\n" +
	"\x18CustomTwilioCallbackData\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id2\xf8\x02\n" +
	"\x17ProviderCallbackService\x12\xb1\x01\n" +
	" HandleAwsMessagingStatusCallback\x12E.backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackRequest\x1aF.backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackResponse\x12\xa8\x01\n" +
	"\x1dHandleTwilioSmsStatusCallback\x12B.backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackRequest\x1aC.backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackResponseBs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDescData
}

var file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_backend_proto_message_hub_v1_provider_callback_service_proto_goTypes = []any{
	(*HandleAwsMessagingStatusCallbackRequest)(nil),  // 0: backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackRequest
	(*AwsMessagingStatusCallbackContext)(nil),        // 1: backend.proto.message_hub.v1.AwsMessagingStatusCallbackContext
	(*HandleAwsMessagingStatusCallbackResponse)(nil), // 2: backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackResponse
	(*HandleTwilioSmsStatusCallbackRequest)(nil),     // 3: backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackRequest
	(*HandleTwilioSmsStatusCallbackResponse)(nil),    // 4: backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackResponse
	(*CustomTwilioCallbackData)(nil),                 // 5: backend.proto.message_hub.v1.CustomTwilioCallbackData
}
var file_backend_proto_message_hub_v1_provider_callback_service_proto_depIdxs = []int32{
	1, // 0: backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackRequest.context:type_name -> backend.proto.message_hub.v1.AwsMessagingStatusCallbackContext
	5, // 1: backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackRequest.custom:type_name -> backend.proto.message_hub.v1.CustomTwilioCallbackData
	0, // 2: backend.proto.message_hub.v1.ProviderCallbackService.HandleAwsMessagingStatusCallback:input_type -> backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackRequest
	3, // 3: backend.proto.message_hub.v1.ProviderCallbackService.HandleTwilioSmsStatusCallback:input_type -> backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackRequest
	2, // 4: backend.proto.message_hub.v1.ProviderCallbackService.HandleAwsMessagingStatusCallback:output_type -> backend.proto.message_hub.v1.HandleAwsMessagingStatusCallbackResponse
	4, // 5: backend.proto.message_hub.v1.ProviderCallbackService.HandleTwilioSmsStatusCallback:output_type -> backend.proto.message_hub.v1.HandleTwilioSmsStatusCallbackResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_provider_callback_service_proto_init() }
func file_backend_proto_message_hub_v1_provider_callback_service_proto_init() {
	if File_backend_proto_message_hub_v1_provider_callback_service_proto != nil {
		return
	}
	file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_provider_callback_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_message_hub_v1_provider_callback_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_provider_callback_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_message_hub_v1_provider_callback_service_proto_msgTypes,
	}.Build()
	File_backend_proto_message_hub_v1_provider_callback_service_proto = out.File
	file_backend_proto_message_hub_v1_provider_callback_service_proto_goTypes = nil
	file_backend_proto_message_hub_v1_provider_callback_service_proto_depIdxs = nil
}
