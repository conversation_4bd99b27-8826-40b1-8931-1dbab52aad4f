// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/message_hub/v1/provider_callback_service.proto

package messagehubpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ProviderCallbackService_HandleAwsMessagingStatusCallback_FullMethodName = "/backend.proto.message_hub.v1.ProviderCallbackService/HandleAwsMessagingStatusCallback"
	ProviderCallbackService_HandleTwilioSmsStatusCallback_FullMethodName    = "/backend.proto.message_hub.v1.ProviderCallbackService/HandleTwilioSmsStatusCallback"
)

// ProviderCallbackServiceClient is the client API for ProviderCallbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ProviderCallbackService handles callbacks from different providers.
type ProviderCallbackServiceClient interface {
	// HandleAwsMessagingStatusCallback receives status updates from AWS.
	HandleAwsMessagingStatusCallback(ctx context.Context, in *HandleAwsMessagingStatusCallbackRequest, opts ...grpc.CallOption) (*HandleAwsMessagingStatusCallbackResponse, error)
	// HandleTwilioSmsStatusCallback receives status updates from Twilio.
	// see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
	HandleTwilioSmsStatusCallback(ctx context.Context, in *HandleTwilioSmsStatusCallbackRequest, opts ...grpc.CallOption) (*HandleTwilioSmsStatusCallbackResponse, error)
}

type providerCallbackServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProviderCallbackServiceClient(cc grpc.ClientConnInterface) ProviderCallbackServiceClient {
	return &providerCallbackServiceClient{cc}
}

func (c *providerCallbackServiceClient) HandleAwsMessagingStatusCallback(ctx context.Context, in *HandleAwsMessagingStatusCallbackRequest, opts ...grpc.CallOption) (*HandleAwsMessagingStatusCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleAwsMessagingStatusCallbackResponse)
	err := c.cc.Invoke(ctx, ProviderCallbackService_HandleAwsMessagingStatusCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *providerCallbackServiceClient) HandleTwilioSmsStatusCallback(ctx context.Context, in *HandleTwilioSmsStatusCallbackRequest, opts ...grpc.CallOption) (*HandleTwilioSmsStatusCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleTwilioSmsStatusCallbackResponse)
	err := c.cc.Invoke(ctx, ProviderCallbackService_HandleTwilioSmsStatusCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProviderCallbackServiceServer is the server API for ProviderCallbackService service.
// All implementations must embed UnimplementedProviderCallbackServiceServer
// for forward compatibility.
//
// ProviderCallbackService handles callbacks from different providers.
type ProviderCallbackServiceServer interface {
	// HandleAwsMessagingStatusCallback receives status updates from AWS.
	HandleAwsMessagingStatusCallback(context.Context, *HandleAwsMessagingStatusCallbackRequest) (*HandleAwsMessagingStatusCallbackResponse, error)
	// HandleTwilioSmsStatusCallback receives status updates from Twilio.
	// see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
	HandleTwilioSmsStatusCallback(context.Context, *HandleTwilioSmsStatusCallbackRequest) (*HandleTwilioSmsStatusCallbackResponse, error)
	mustEmbedUnimplementedProviderCallbackServiceServer()
}

// UnimplementedProviderCallbackServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProviderCallbackServiceServer struct{}

func (UnimplementedProviderCallbackServiceServer) HandleAwsMessagingStatusCallback(context.Context, *HandleAwsMessagingStatusCallbackRequest) (*HandleAwsMessagingStatusCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleAwsMessagingStatusCallback not implemented")
}
func (UnimplementedProviderCallbackServiceServer) HandleTwilioSmsStatusCallback(context.Context, *HandleTwilioSmsStatusCallbackRequest) (*HandleTwilioSmsStatusCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleTwilioSmsStatusCallback not implemented")
}
func (UnimplementedProviderCallbackServiceServer) mustEmbedUnimplementedProviderCallbackServiceServer() {
}
func (UnimplementedProviderCallbackServiceServer) testEmbeddedByValue() {}

// UnsafeProviderCallbackServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProviderCallbackServiceServer will
// result in compilation errors.
type UnsafeProviderCallbackServiceServer interface {
	mustEmbedUnimplementedProviderCallbackServiceServer()
}

func RegisterProviderCallbackServiceServer(s grpc.ServiceRegistrar, srv ProviderCallbackServiceServer) {
	// If the following call pancis, it indicates UnimplementedProviderCallbackServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProviderCallbackService_ServiceDesc, srv)
}

func _ProviderCallbackService_HandleAwsMessagingStatusCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleAwsMessagingStatusCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProviderCallbackServiceServer).HandleAwsMessagingStatusCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProviderCallbackService_HandleAwsMessagingStatusCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProviderCallbackServiceServer).HandleAwsMessagingStatusCallback(ctx, req.(*HandleAwsMessagingStatusCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProviderCallbackService_HandleTwilioSmsStatusCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleTwilioSmsStatusCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProviderCallbackServiceServer).HandleTwilioSmsStatusCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProviderCallbackService_HandleTwilioSmsStatusCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProviderCallbackServiceServer).HandleTwilioSmsStatusCallback(ctx, req.(*HandleTwilioSmsStatusCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ProviderCallbackService_ServiceDesc is the grpc.ServiceDesc for ProviderCallbackService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProviderCallbackService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.message_hub.v1.ProviderCallbackService",
	HandlerType: (*ProviderCallbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleAwsMessagingStatusCallback",
			Handler:    _ProviderCallbackService_HandleAwsMessagingStatusCallback_Handler,
		},
		{
			MethodName: "HandleTwilioSmsStatusCallback",
			Handler:    _ProviderCallbackService_HandleTwilioSmsStatusCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/message_hub/v1/provider_callback_service.proto",
}
