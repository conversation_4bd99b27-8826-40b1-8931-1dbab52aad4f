// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/tools/v1/deploy_platform.proto

package toolspb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for SkipCanary
type SkipDeployTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the deployment task
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Reason for skipping the canary
	Reason        string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SkipDeployTaskRequest) Reset() {
	*x = SkipDeployTaskRequest{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkipDeployTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipDeployTaskRequest) ProtoMessage() {}

func (x *SkipDeployTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipDeployTaskRequest.ProtoReflect.Descriptor instead.
func (*SkipDeployTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{0}
}

func (x *SkipDeployTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SkipDeployTaskRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Response message for SkipCanary
type SkipDeployTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SkipDeployTaskResponse) Reset() {
	*x = SkipDeployTaskResponse{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkipDeployTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipDeployTaskResponse) ProtoMessage() {}

func (x *SkipDeployTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipDeployTaskResponse.ProtoReflect.Descriptor instead.
func (*SkipDeployTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{1}
}

// Request message for Rollback
type RollbackDeployTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the deployment task
	// (-- api-linter: core::0162::rollback-request-name-reference=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Revision ID of the deployment
	RevisionId string `protobuf:"bytes,2,opt,name=revision_id,json=revisionId,proto3" json:"revision_id,omitempty"`
	// Reason for rolling back the deployment
	Reason        string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RollbackDeployTaskRequest) Reset() {
	*x = RollbackDeployTaskRequest{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RollbackDeployTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackDeployTaskRequest) ProtoMessage() {}

func (x *RollbackDeployTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackDeployTaskRequest.ProtoReflect.Descriptor instead.
func (*RollbackDeployTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{2}
}

func (x *RollbackDeployTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RollbackDeployTaskRequest) GetRevisionId() string {
	if x != nil {
		return x.RevisionId
	}
	return ""
}

func (x *RollbackDeployTaskRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Response message for Rollback
type RollbackDeployTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RollbackDeployTaskResponse) Reset() {
	*x = RollbackDeployTaskResponse{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RollbackDeployTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackDeployTaskResponse) ProtoMessage() {}

func (x *RollbackDeployTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackDeployTaskResponse.ProtoReflect.Descriptor instead.
func (*RollbackDeployTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{3}
}

// Request message for HandleCanaryEventWebhookEvent
type HandleCanaryEventWebhookEventRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the canary.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Namespace of the canary.
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Phase of the canary analysis.
	Phase string `protobuf:"bytes,3,opt,name=phase,proto3" json:"phase,omitempty"`
	// Hash from the TrackedConfigs and LastAppliedSpec of the Canary.
	// Can be used to identify a Canary for a specific configuration of the deployed resources.
	Checksum string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
	// BuildId of the Workload.
	BuildId string `protobuf:"bytes,5,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// Type of the event.
	Type *string `protobuf:"bytes,6,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// FailedChecks
	FailedChecks *int32 `protobuf:"varint,7,opt,name=failed_checks,json=failedChecks,proto3,oneof" json:"failed_checks,omitempty"`
	// CanaryWeight
	CanaryWeight *int32 `protobuf:"varint,8,opt,name=canary_weight,json=canaryWeight,proto3,oneof" json:"canary_weight,omitempty"`
	// Iterations
	Iterations *int32 `protobuf:"varint,9,opt,name=iterations,proto3,oneof" json:"iterations,omitempty"`
	// RemainingTime
	Remaining *int64 `protobuf:"varint,10,opt,name=remaining,proto3,oneof" json:"remaining,omitempty"`
	// Metadata (key-value pairs) for this webhook (optional).
	Metadata      map[string]string `protobuf:"bytes,11,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCanaryEventWebhookEventRequest) Reset() {
	*x = HandleCanaryEventWebhookEventRequest{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCanaryEventWebhookEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCanaryEventWebhookEventRequest) ProtoMessage() {}

func (x *HandleCanaryEventWebhookEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCanaryEventWebhookEventRequest.ProtoReflect.Descriptor instead.
func (*HandleCanaryEventWebhookEventRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{4}
}

func (x *HandleCanaryEventWebhookEventRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *HandleCanaryEventWebhookEventRequest) GetFailedChecks() int32 {
	if x != nil && x.FailedChecks != nil {
		return *x.FailedChecks
	}
	return 0
}

func (x *HandleCanaryEventWebhookEventRequest) GetCanaryWeight() int32 {
	if x != nil && x.CanaryWeight != nil {
		return *x.CanaryWeight
	}
	return 0
}

func (x *HandleCanaryEventWebhookEventRequest) GetIterations() int32 {
	if x != nil && x.Iterations != nil {
		return *x.Iterations
	}
	return 0
}

func (x *HandleCanaryEventWebhookEventRequest) GetRemaining() int64 {
	if x != nil && x.Remaining != nil {
		return *x.Remaining
	}
	return 0
}

func (x *HandleCanaryEventWebhookEventRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Response message for HandleCanaryEventWebhookEvent
type HandleCanaryEventWebhookEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCanaryEventWebhookEventResponse) Reset() {
	*x = HandleCanaryEventWebhookEventResponse{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCanaryEventWebhookEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCanaryEventWebhookEventResponse) ProtoMessage() {}

func (x *HandleCanaryEventWebhookEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCanaryEventWebhookEventResponse.ProtoReflect.Descriptor instead.
func (*HandleCanaryEventWebhookEventResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{5}
}

// Request message for HandleCanaryWebhookEvent
type HandleCanaryWebhookEventRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the canary.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Namespace of the canary.
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Phase of the canary analysis.
	Phase string `protobuf:"bytes,3,opt,name=phase,proto3" json:"phase,omitempty"`
	// Hash from the TrackedConfigs and LastAppliedSpec of the Canary.
	// Can be used to identify a Canary for a specific configuration of the deployed resources.
	Checksum string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
	// BuildId of the Workload.
	BuildId string `protobuf:"bytes,5,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// Type of the event.
	Type string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	// Metadata (key-value pairs) for this webhook (optional).
	Metadata      map[string]string `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCanaryWebhookEventRequest) Reset() {
	*x = HandleCanaryWebhookEventRequest{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCanaryWebhookEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCanaryWebhookEventRequest) ProtoMessage() {}

func (x *HandleCanaryWebhookEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCanaryWebhookEventRequest.ProtoReflect.Descriptor instead.
func (*HandleCanaryWebhookEventRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{6}
}

func (x *HandleCanaryWebhookEventRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HandleCanaryWebhookEventRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Response message for HandleCanaryWebhookEvent
type HandleCanaryWebhookEventResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Rollback
	Rollback bool `protobuf:"varint,1,opt,name=rollback,proto3" json:"rollback,omitempty"`
	// Skip
	Skip bool `protobuf:"varint,2,opt,name=skip,proto3" json:"skip,omitempty"`
	// ManualTrafficControl
	ManualTrafficControl *HandleCanaryWebhookEventResponse_ManualTrafficControl `protobuf:"bytes,3,opt,name=manual_traffic_control,json=manualTrafficControl,proto3" json:"manual_traffic_control,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *HandleCanaryWebhookEventResponse) Reset() {
	*x = HandleCanaryWebhookEventResponse{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCanaryWebhookEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCanaryWebhookEventResponse) ProtoMessage() {}

func (x *HandleCanaryWebhookEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCanaryWebhookEventResponse.ProtoReflect.Descriptor instead.
func (*HandleCanaryWebhookEventResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{7}
}

func (x *HandleCanaryWebhookEventResponse) GetRollback() bool {
	if x != nil {
		return x.Rollback
	}
	return false
}

func (x *HandleCanaryWebhookEventResponse) GetSkip() bool {
	if x != nil {
		return x.Skip
	}
	return false
}

func (x *HandleCanaryWebhookEventResponse) GetManualTrafficControl() *HandleCanaryWebhookEventResponse_ManualTrafficControl {
	if x != nil {
		return x.ManualTrafficControl
	}
	return nil
}

// DeployTask represents a deployment task
// (-- api-linter: core::0123::resource-annotation=disabled
//
//	aip.dev/not-precedent: We need to do this because reasons. --)
type DeployTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Task key
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Task title
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// Project key
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// Creator of the task
	Creator string `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
	// Creation timestamp
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Last update timestamp
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Current state of the task
	State string `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`
	// Current phase of the task
	CurrentPhase string `protobuf:"bytes,9,opt,name=current_phase,json=currentPhase,proto3" json:"current_phase,omitempty"`
	// Parameters for the task
	Parameters string `protobuf:"bytes,10,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// List of deployment phases
	Phases []*DeployTask_DeployPhase `protobuf:"bytes,11,rep,name=phases,proto3" json:"phases,omitempty"`
	// List of deployment logs
	Logs          []*DeployTask_DeployLog `protobuf:"bytes,12,rep,name=logs,proto3" json:"logs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployTask) Reset() {
	*x = DeployTask{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployTask) ProtoMessage() {}

func (x *DeployTask) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployTask.ProtoReflect.Descriptor instead.
func (*DeployTask) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{8}
}

func (x *DeployTask) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeployTask) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DeployTask) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DeployTask) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DeployTask) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DeployTask) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *DeployTask) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DeployTask) GetCurrentPhase() string {
	if x != nil {
		return x.CurrentPhase
	}
	return ""
}

func (x *DeployTask) GetParameters() string {
	if x != nil {
		return x.Parameters
	}
	return ""
}

func (x *DeployTask) GetPhases() []*DeployTask_DeployPhase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *DeployTask) GetLogs() []*DeployTask_DeployLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

// Request message for HandleSlackInteractionsEvent
type HandleSlackInteractionsEventRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Payload of the Slack event
	Payload       string `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleSlackInteractionsEventRequest) Reset() {
	*x = HandleSlackInteractionsEventRequest{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleSlackInteractionsEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleSlackInteractionsEventRequest) ProtoMessage() {}

func (x *HandleSlackInteractionsEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleSlackInteractionsEventRequest.ProtoReflect.Descriptor instead.
func (*HandleSlackInteractionsEventRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{9}
}

func (x *HandleSlackInteractionsEventRequest) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

// Response message for HandleSlackInteractionsEvent
type HandleSlackInteractionsEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleSlackInteractionsEventResponse) Reset() {
	*x = HandleSlackInteractionsEventResponse{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleSlackInteractionsEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleSlackInteractionsEventResponse) ProtoMessage() {}

func (x *HandleSlackInteractionsEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleSlackInteractionsEventResponse.ProtoReflect.Descriptor instead.
func (*HandleSlackInteractionsEventResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{10}
}

type HandleCanaryWebhookEventResponse_ManualTrafficControl struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Timestamp
	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Weight
	Weight float64 `protobuf:"fixed64,2,opt,name=weight,proto3" json:"weight,omitempty"`
	// Paused
	Paused        bool `protobuf:"varint,3,opt,name=Paused,proto3" json:"Paused,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) Reset() {
	*x = HandleCanaryWebhookEventResponse_ManualTrafficControl{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCanaryWebhookEventResponse_ManualTrafficControl) ProtoMessage() {}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCanaryWebhookEventResponse_ManualTrafficControl.ProtoReflect.Descriptor instead.
func (*HandleCanaryWebhookEventResponse_ManualTrafficControl) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{7, 0}
}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) GetWeight() float64 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *HandleCanaryWebhookEventResponse_ManualTrafficControl) GetPaused() bool {
	if x != nil {
		return x.Paused
	}
	return false
}

// DeployPhase represents a phase of a deployment task
type DeployTask_DeployPhase struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the associated deployment task
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// Name of the phase
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// type of the phase
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// Current state of the phase
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	// Parameters for the phase
	Parameters string `protobuf:"bytes,6,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// Start timestamp of the phase
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// End timestamp of the phase (nullable)
	EndTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// Reason for the modification
	Reason        *string `protobuf:"bytes,9,opt,name=reason,proto3,oneof" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployTask_DeployPhase) Reset() {
	*x = DeployTask_DeployPhase{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployTask_DeployPhase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployTask_DeployPhase) ProtoMessage() {}

func (x *DeployTask_DeployPhase) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployTask_DeployPhase.ProtoReflect.Descriptor instead.
func (*DeployTask_DeployPhase) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{8, 0}
}

func (x *DeployTask_DeployPhase) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployTask_DeployPhase) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DeployTask_DeployPhase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeployTask_DeployPhase) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeployTask_DeployPhase) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DeployTask_DeployPhase) GetParameters() string {
	if x != nil {
		return x.Parameters
	}
	return ""
}

func (x *DeployTask_DeployPhase) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *DeployTask_DeployPhase) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *DeployTask_DeployPhase) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

// DeployLog represents a log entry for a deployment task
type DeployTask_DeployLog struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the associated deployment task
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// Log timestamp
	LogTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=log_time,json=logTime,proto3" json:"log_time,omitempty"`
	// Type of the log entry
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// State of the deployment task at the time of the log entry
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	// Phase of the deployment task at the time of the log entry
	Phase string `protobuf:"bytes,6,opt,name=phase,proto3" json:"phase,omitempty"`
	// Message associated with the log entry
	Message       string `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployTask_DeployLog) Reset() {
	*x = DeployTask_DeployLog{}
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployTask_DeployLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployTask_DeployLog) ProtoMessage() {}

func (x *DeployTask_DeployLog) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployTask_DeployLog.ProtoReflect.Descriptor instead.
func (*DeployTask_DeployLog) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP(), []int{8, 1}
}

func (x *DeployTask_DeployLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployTask_DeployLog) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DeployTask_DeployLog) GetLogTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LogTime
	}
	return nil
}

func (x *DeployTask_DeployLog) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeployTask_DeployLog) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DeployTask_DeployLog) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *DeployTask_DeployLog) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_backend_proto_tools_v1_deploy_platform_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_deploy_platform_proto_rawDesc = "" +
	"\n" +
	",backend/proto/tools/v1/deploy_platform.proto\x12\x16backend.proto.tools.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"M\n" +
	"\x15SkipDeployTaskRequest\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\x02R\x04name\x12\x1b\n" +
	"\x06reason\x18\x02 \x01(\tB\x03\xe0A\x02R\x06reason\"\x18\n" +
	"\x16SkipDeployTaskResponse\"w\n" +
	"\x19RollbackDeployTaskRequest\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\x02R\x04name\x12$\n" +
	"\vrevision_id\x18\x02 \x01(\tB\x03\xe0A\x02R\n" +
	"revisionId\x12\x1b\n" +
	"\x06reason\x18\x03 \x01(\tB\x03\xe0A\x02R\x06reason\"\x1c\n" +
	"\x1aRollbackDeployTaskResponse\"\x80\x05\n" +
	"$HandleCanaryEventWebhookEventRequest\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\x02R\x04name\x12!\n" +
	"\tnamespace\x18\x02 \x01(\tB\x03\xe0A\x02R\tnamespace\x12\x19\n" +
	"\x05phase\x18\x03 \x01(\tB\x03\xe0A\x02R\x05phase\x12\x1f\n" +
	"\bchecksum\x18\x04 \x01(\tB\x03\xe0A\x02R\bchecksum\x12\x1e\n" +
	"\bbuild_id\x18\x05 \x01(\tB\x03\xe0A\x02R\abuildId\x12\x1c\n" +
	"\x04type\x18\x06 \x01(\tB\x03\xe0A\x01H\x00R\x04type\x88\x01\x01\x12-\n" +
	"\rfailed_checks\x18\a \x01(\x05B\x03\xe0A\x01H\x01R\ffailedChecks\x88\x01\x01\x12-\n" +
	"\rcanary_weight\x18\b \x01(\x05B\x03\xe0A\x01H\x02R\fcanaryWeight\x88\x01\x01\x12(\n" +
	"\n" +
	"iterations\x18\t \x01(\x05B\x03\xe0A\x01H\x03R\n" +
	"iterations\x88\x01\x01\x12&\n" +
	"\tremaining\x18\n" +
	" \x01(\x03B\x03\xe0A\x01H\x04R\tremaining\x88\x01\x01\x12k\n" +
	"\bmetadata\x18\v \x03(\v2J.backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest.MetadataEntryB\x03\xe0A\x01R\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\a\n" +
	"\x05_typeB\x10\n" +
	"\x0e_failed_checksB\x10\n" +
	"\x0e_canary_weightB\r\n" +
	"\v_iterationsB\f\n" +
	"\n" +
	"_remaining\"'\n" +
	"%HandleCanaryEventWebhookEventResponse\"\xf7\x02\n" +
	"\x1fHandleCanaryWebhookEventRequest\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tB\x03\xe0A\x02R\x04name\x12!\n" +
	"\tnamespace\x18\x02 \x01(\tB\x03\xe0A\x02R\tnamespace\x12\x19\n" +
	"\x05phase\x18\x03 \x01(\tB\x03\xe0A\x02R\x05phase\x12\x1f\n" +
	"\bchecksum\x18\x04 \x01(\tB\x03\xe0A\x02R\bchecksum\x12\x1e\n" +
	"\bbuild_id\x18\x05 \x01(\tB\x03\xe0A\x02R\abuildId\x12\x17\n" +
	"\x04type\x18\a \x01(\tB\x03\xe0A\x02R\x04type\x12f\n" +
	"\bmetadata\x18\x06 \x03(\v2E.backend.proto.tools.v1.HandleCanaryWebhookEventRequest.MetadataEntryB\x03\xe0A\x01R\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xdc\x02\n" +
	" HandleCanaryWebhookEventResponse\x12\x1f\n" +
	"\brollback\x18\x01 \x01(\bB\x03\xe0A\x02R\brollback\x12\x17\n" +
	"\x04skip\x18\x02 \x01(\bB\x03\xe0A\x02R\x04skip\x12\x88\x01\n" +
	"\x16manual_traffic_control\x18\x03 \x01(\v2M.backend.proto.tools.v1.HandleCanaryWebhookEventResponse.ManualTrafficControlB\x03\xe0A\x02R\x14manualTrafficControl\x1as\n" +
	"\x14ManualTrafficControl\x12!\n" +
	"\ttimestamp\x18\x01 \x01(\x03B\x03\xe0A\x02R\ttimestamp\x12\x1b\n" +
	"\x06weight\x18\x02 \x01(\x01B\x03\xe0A\x02R\x06weight\x12\x1b\n" +
	"\x06Paused\x18\x03 \x01(\bB\x03\xe0A\x02R\x06Paused\"\xf8\b\n" +
	"\n" +
	"DeployTask\x12\x13\n" +
	"\x02id\x18\x01 \x01(\x03B\x03\xe0A\x02R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tB\x03\xe0A\x02R\x04name\x12\x19\n" +
	"\x05title\x18\x03 \x01(\tB\x03\xe0A\x02R\x05title\x12%\n" +
	"\vdescription\x18\x04 \x01(\tB\x03\xe0A\x02R\vdescription\x12\x1d\n" +
	"\acreator\x18\x05 \x01(\tB\x03\xe0A\x02R\acreator\x12@\n" +
	"\vcreate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01R\n" +
	"updateTime\x12\x19\n" +
	"\x05state\x18\b \x01(\tB\x03\xe0A\x03R\x05state\x12(\n" +
	"\rcurrent_phase\x18\t \x01(\tB\x03\xe0A\x01R\fcurrentPhase\x12#\n" +
	"\n" +
	"parameters\x18\n" +
	" \x01(\tB\x03\xe0A\x01R\n" +
	"parameters\x12K\n" +
	"\x06phases\x18\v \x03(\v2..backend.proto.tools.v1.DeployTask.DeployPhaseB\x03\xe0A\x01R\x06phases\x12E\n" +
	"\x04logs\x18\f \x03(\v2,.backend.proto.tools.v1.DeployTask.DeployLogB\x03\xe0A\x01R\x04logs\x1a\xed\x02\n" +
	"\vDeployPhase\x12\x13\n" +
	"\x02id\x18\x01 \x01(\x03B\x03\xe0A\x01R\x02id\x12\x1c\n" +
	"\atask_id\x18\x02 \x01(\x03B\x03\xe0A\x01R\x06taskId\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tB\x03\xe0A\x01R\x04name\x12\x17\n" +
	"\x04type\x18\x04 \x01(\tB\x03\xe0A\x01R\x04type\x12\x19\n" +
	"\x05state\x18\x05 \x01(\tB\x03\xe0A\x03R\x05state\x12#\n" +
	"\n" +
	"parameters\x18\x06 \x01(\tB\x03\xe0A\x01R\n" +
	"parameters\x12>\n" +
	"\n" +
	"start_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01R\tstartTime\x12?\n" +
	"\bend_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01H\x00R\aendTime\x88\x01\x01\x12 \n" +
	"\x06reason\x18\t \x01(\tB\x03\xe0A\x01H\x01R\x06reason\x88\x01\x01B\v\n" +
	"\t_end_timeB\t\n" +
	"\a_reason\x1a\xe8\x01\n" +
	"\tDeployLog\x12\x13\n" +
	"\x02id\x18\x01 \x01(\x03B\x03\xe0A\x01R\x02id\x12\x1c\n" +
	"\atask_id\x18\x02 \x01(\x03B\x03\xe0A\x01R\x06taskId\x12:\n" +
	"\blog_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x01R\alogTime\x12\x17\n" +
	"\x04type\x18\x04 \x01(\tB\x03\xe0A\x01R\x04type\x12\x19\n" +
	"\x05state\x18\x05 \x01(\tB\x03\xe0A\x03R\x05state\x12\x19\n" +
	"\x05phase\x18\x06 \x01(\tB\x03\xe0A\x01R\x05phase\x12\x1d\n" +
	"\amessage\x18\a \x01(\tB\x03\xe0A\x01R\amessage\"D\n" +
	"#HandleSlackInteractionsEventRequest\x12\x1d\n" +
	"\apayload\x18\x02 \x01(\tB\x03\xe0A\x02R\apayload\"&\n" +
	"$HandleSlackInteractionsEventResponse2\xd0\x05\n" +
	"\x15DeployPlatformService\x12o\n" +
	"\x0eSkipDeployTask\x12-.backend.proto.tools.v1.SkipDeployTaskRequest\x1a..backend.proto.tools.v1.SkipDeployTaskResponse\x12{\n" +
	"\x12RollbackDeployTask\x121.backend.proto.tools.v1.RollbackDeployTaskRequest\x1a2.backend.proto.tools.v1.RollbackDeployTaskResponse\x12\x9c\x01\n" +
	"\x1dHandleCanaryEventWebhookEvent\x12<.backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest\x1a=.backend.proto.tools.v1.HandleCanaryEventWebhookEventResponse\x12\x8d\x01\n" +
	"\x18HandleCanaryWebhookEvent\x127.backend.proto.tools.v1.HandleCanaryWebhookEventRequest\x1a8.backend.proto.tools.v1.HandleCanaryWebhookEventResponse\x12\x99\x01\n" +
	"\x1cHandleSlackInteractionsEvent\x12;.backend.proto.tools.v1.HandleSlackInteractionsEventRequest\x1a<.backend.proto.tools.v1.HandleSlackInteractionsEventResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_deploy_platform_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_deploy_platform_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_deploy_platform_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_deploy_platform_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_deploy_platform_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_deploy_platform_proto_rawDesc), len(file_backend_proto_tools_v1_deploy_platform_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_deploy_platform_proto_rawDescData
}

var file_backend_proto_tools_v1_deploy_platform_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_backend_proto_tools_v1_deploy_platform_proto_goTypes = []any{
	(*SkipDeployTaskRequest)(nil),                 // 0: backend.proto.tools.v1.SkipDeployTaskRequest
	(*SkipDeployTaskResponse)(nil),                // 1: backend.proto.tools.v1.SkipDeployTaskResponse
	(*RollbackDeployTaskRequest)(nil),             // 2: backend.proto.tools.v1.RollbackDeployTaskRequest
	(*RollbackDeployTaskResponse)(nil),            // 3: backend.proto.tools.v1.RollbackDeployTaskResponse
	(*HandleCanaryEventWebhookEventRequest)(nil),  // 4: backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest
	(*HandleCanaryEventWebhookEventResponse)(nil), // 5: backend.proto.tools.v1.HandleCanaryEventWebhookEventResponse
	(*HandleCanaryWebhookEventRequest)(nil),       // 6: backend.proto.tools.v1.HandleCanaryWebhookEventRequest
	(*HandleCanaryWebhookEventResponse)(nil),      // 7: backend.proto.tools.v1.HandleCanaryWebhookEventResponse
	(*DeployTask)(nil),                            // 8: backend.proto.tools.v1.DeployTask
	(*HandleSlackInteractionsEventRequest)(nil),   // 9: backend.proto.tools.v1.HandleSlackInteractionsEventRequest
	(*HandleSlackInteractionsEventResponse)(nil),  // 10: backend.proto.tools.v1.HandleSlackInteractionsEventResponse
	nil, // 11: backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest.MetadataEntry
	nil, // 12: backend.proto.tools.v1.HandleCanaryWebhookEventRequest.MetadataEntry
	(*HandleCanaryWebhookEventResponse_ManualTrafficControl)(nil), // 13: backend.proto.tools.v1.HandleCanaryWebhookEventResponse.ManualTrafficControl
	(*DeployTask_DeployPhase)(nil),                                // 14: backend.proto.tools.v1.DeployTask.DeployPhase
	(*DeployTask_DeployLog)(nil),                                  // 15: backend.proto.tools.v1.DeployTask.DeployLog
	(*timestamppb.Timestamp)(nil),                                 // 16: google.protobuf.Timestamp
}
var file_backend_proto_tools_v1_deploy_platform_proto_depIdxs = []int32{
	11, // 0: backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest.metadata:type_name -> backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest.MetadataEntry
	12, // 1: backend.proto.tools.v1.HandleCanaryWebhookEventRequest.metadata:type_name -> backend.proto.tools.v1.HandleCanaryWebhookEventRequest.MetadataEntry
	13, // 2: backend.proto.tools.v1.HandleCanaryWebhookEventResponse.manual_traffic_control:type_name -> backend.proto.tools.v1.HandleCanaryWebhookEventResponse.ManualTrafficControl
	16, // 3: backend.proto.tools.v1.DeployTask.create_time:type_name -> google.protobuf.Timestamp
	16, // 4: backend.proto.tools.v1.DeployTask.update_time:type_name -> google.protobuf.Timestamp
	14, // 5: backend.proto.tools.v1.DeployTask.phases:type_name -> backend.proto.tools.v1.DeployTask.DeployPhase
	15, // 6: backend.proto.tools.v1.DeployTask.logs:type_name -> backend.proto.tools.v1.DeployTask.DeployLog
	16, // 7: backend.proto.tools.v1.DeployTask.DeployPhase.start_time:type_name -> google.protobuf.Timestamp
	16, // 8: backend.proto.tools.v1.DeployTask.DeployPhase.end_time:type_name -> google.protobuf.Timestamp
	16, // 9: backend.proto.tools.v1.DeployTask.DeployLog.log_time:type_name -> google.protobuf.Timestamp
	0,  // 10: backend.proto.tools.v1.DeployPlatformService.SkipDeployTask:input_type -> backend.proto.tools.v1.SkipDeployTaskRequest
	2,  // 11: backend.proto.tools.v1.DeployPlatformService.RollbackDeployTask:input_type -> backend.proto.tools.v1.RollbackDeployTaskRequest
	4,  // 12: backend.proto.tools.v1.DeployPlatformService.HandleCanaryEventWebhookEvent:input_type -> backend.proto.tools.v1.HandleCanaryEventWebhookEventRequest
	6,  // 13: backend.proto.tools.v1.DeployPlatformService.HandleCanaryWebhookEvent:input_type -> backend.proto.tools.v1.HandleCanaryWebhookEventRequest
	9,  // 14: backend.proto.tools.v1.DeployPlatformService.HandleSlackInteractionsEvent:input_type -> backend.proto.tools.v1.HandleSlackInteractionsEventRequest
	1,  // 15: backend.proto.tools.v1.DeployPlatformService.SkipDeployTask:output_type -> backend.proto.tools.v1.SkipDeployTaskResponse
	3,  // 16: backend.proto.tools.v1.DeployPlatformService.RollbackDeployTask:output_type -> backend.proto.tools.v1.RollbackDeployTaskResponse
	5,  // 17: backend.proto.tools.v1.DeployPlatformService.HandleCanaryEventWebhookEvent:output_type -> backend.proto.tools.v1.HandleCanaryEventWebhookEventResponse
	7,  // 18: backend.proto.tools.v1.DeployPlatformService.HandleCanaryWebhookEvent:output_type -> backend.proto.tools.v1.HandleCanaryWebhookEventResponse
	10, // 19: backend.proto.tools.v1.DeployPlatformService.HandleSlackInteractionsEvent:output_type -> backend.proto.tools.v1.HandleSlackInteractionsEventResponse
	15, // [15:20] is the sub-list for method output_type
	10, // [10:15] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_deploy_platform_proto_init() }
func file_backend_proto_tools_v1_deploy_platform_proto_init() {
	if File_backend_proto_tools_v1_deploy_platform_proto != nil {
		return
	}
	file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_tools_v1_deploy_platform_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_deploy_platform_proto_rawDesc), len(file_backend_proto_tools_v1_deploy_platform_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_deploy_platform_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_deploy_platform_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_deploy_platform_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_deploy_platform_proto = out.File
	file_backend_proto_tools_v1_deploy_platform_proto_goTypes = nil
	file_backend_proto_tools_v1_deploy_platform_proto_depIdxs = nil
}
