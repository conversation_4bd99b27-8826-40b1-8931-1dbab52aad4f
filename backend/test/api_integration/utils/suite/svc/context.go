package svc

import (
	"context"
	"fmt"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/trace"
	"github.com/stretchr/testify/suite"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)


var (
	// MoegoServerCustomer moego-server-customer, 但是集群里的服务名是 moego-service-customer
	MoegoServerCustomer = NewHttpSvc("moego-service-customer", "9201")
	// MoegoServerBusiness moego-server-business, 但是集群里的服务名是 moego-service-business
	MoegoServerBusiness = NewHttpSvc("moego-service-business", "9203")
	// MoegoServerPayment moego-server-payment, 但是集群里的服务名是 moego-service-payment
	MoegoServerPayment = NewHttpSvc("moego-service-payment", "9204")
    // MoegoServerMessage moego-server-message, 但是集群里的服务名是 moego-service-message
	MoegoServerMessage = NewHttpSvc("moego-service-message", "9205")
	// MoegoServerGrooming moego-server-grooming, 但是集群里的服务名是 moego-service-grooming
	MoegoServerGrooming = NewHttpSvc("moego-service-grooming", "9206")
	// MoegoServerRetail moego-server-retail, 但是集群里的服务名是 moego-service-retail
	MoegoServerRetail = NewHttpSvc("moego-service-retail", "9207")


	// MoegoSvcAccount moego-svc-account
	MoegoSvcAccount = NewGrpcSvc("moego-svc-account", "9090")
	// TODO: 还有很多 svc 服务，用到的时候再补充
)

type Svc struct {
	Name string
	Port string
}

func (svc *Svc) GetHost() string {
	return fmt.Sprintf("%s.%s:%s", svc.Name, "ns-testing", svc.Port)
}

type HttpSvc struct {
	Svc
}

func NewHttpSvc(name, port string) HttpSvc {
	return HttpSvc{Svc{Name: name, Port: port}}
}

type GrpcSvc struct {
	Svc
}

func NewGrpcSvc(name, port string) GrpcSvc {
	return GrpcSvc{Svc{Name: name, Port: port}}
}

type Context struct {
	suite      *suite.Suite
	Ctx        context.Context
}

func (c *Context) Setup(suite *suite.Suite) {
	c.Ctx = trace.NewContextWithTrace()
	log.InfoContext(c.Ctx, "Setup suite")

	c.suite = suite
}

func (c *Context) Teardown() {
	// Finish the root span
	span, ok := tracer.SpanFromContext(c.Ctx)
	if ok {
		defer span.Finish()
	}

	log.InfoContext(c.Ctx, "Teardown suite")
}

func (c *Context) NewHttpServerRequest(svc HttpSvc) *api.Request {
	request := api.NewRequest(c.Ctx, svc.GetHost()).SetInsecure(true)
	return request
}

func NewGrpcServiceClient[T any](svc GrpcSvc, constructor func(grpc.ClientConnInterface) T) T {
	conn, err := grpc.NewClient(svc.GetHost(), grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		panic(err)
	}
	return constructor(conn)
}
