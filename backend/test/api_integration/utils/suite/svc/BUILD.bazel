load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "svc",
    srcs = ["context.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/svc",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/api",
        "//backend/test/api_integration/utils/trace",
        "@com_github_datadog_dd_trace_go_v2//ddtrace/tracer",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
    ],
)
