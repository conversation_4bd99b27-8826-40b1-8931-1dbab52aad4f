package account

import (
	"testing"

	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/svc"
	"github.com/stretchr/testify/suite"
)

type AccountServiceTestSuite struct {
	suite.Suite
	// 测试内部 svc 接口时，使用 svc 包里的 context
	svc.Context

	accountService accountsvcpb.AccountServiceClient
}

func (s *AccountServiceTestSuite) SetupSuite() {
	// 必须先 setup context
	s.Context.Setup(&s.Suite)

	// 如果需要使用 svc 层 grpc service，需要在 SetupSuite 阶段 New 一个 client
	// 服务地址已经在 svc 包里定义，直接引用即可
	s.accountService = svc.NewGrpcServiceClient(svc.MoegoSvcAccount, accountsvcpb.NewAccountServiceClient)
}

func (s *AccountServiceTestSuite) TearDownSuite() {
	s.Context.Teardown()
}

func (s *AccountServiceTestSuite) TestGetAccount() {
	// grpc 方法，跟服务间调用时的写法一样，构造入参，调用接口方法即可
	req := &accountsvcpb.GetAccountRequest{
		Identifier: &accountsvcpb.GetAccountRequest_Email{
			Email: "<EMAIL>",
		},
	}
	account, err := s.accountService.GetAccount(s.Ctx, req)
	s.Require().NoError(err)
	s.Require().Equal(account.Email, req.GetEmail())
}

func TestAccountServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AccountServiceTestSuite))
}