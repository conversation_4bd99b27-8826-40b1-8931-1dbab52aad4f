load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "ob_test",
    srcs = ["ob_test.go"],
    tags = ["online_booking"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/proto/test_account/v1:test_account",
        "//backend/test/api_integration/def/customer/model",
        "//backend/test/api_integration/utils/suite/bookingdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "bd_ob_test",
    srcs = ["bd_ob_test.go"],
    tags = ["online_booking"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/proto/test_account/v1:test_account",
        "//backend/test/api_integration/def/customer/model",
        "//backend/test/api_integration/utils/suite/bookingdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "ob_name_test",
    srcs = ["ob_name_test.go"],
    tags = ["internal"],
    deps = [
        "//backend/test/api_integration/utils/suite/svc",
        "@com_github_stretchr_testify//suite",
    ],
)
