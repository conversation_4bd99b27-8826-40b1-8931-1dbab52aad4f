package env

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetEnv(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		want     string
	}{
		{
			name:     "MOEGO_ENVIRONMENT is set to production",
			envValue: PRODUCTION,
			want:     PRODUCTION,
		},
		{
			name:     "MOEGO_ENVIRONMENT is set to staging",
			envValue: STAGING,
			want:     STAGING,
		},
		{
			name:     "MOEGO_ENVIRONMENT is set to testing",
			envValue: TESTING,
			want:     TESTING,
		},
		{
			name:     "MOEGO_ENVIRONMENT is not set",
			envValue: "",
			want:     LOCAL,
		},
		{
			name:     "MOEGO_ENVIRONMENT is set to an arbitrary value",
			envValue: "custom",
			want:     "custom",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				t.Setenv(MOEGO_ENVIRONMENT, tt.envValue)
			} else {
				// Simulate unset variable
				t.Setenv(MOEGO_ENVIRONMENT, "")
			}
			assert.Equal(t, tt.want, GetEnv())
		})
	}
}

func TestGetGreyName(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		want     string
	}{
		{
			name:     "APP_VERSION is not set",
			envValue: "",
			want:     "",
		},
		{
			name:     "APP_VERSION has no prefix",
			envValue: "my-feature",
			want:     "",
		},
		{
			name:     "APP_VERSION has feature- prefix",
			envValue: "feature-my-feature",
			want:     "my-feature",
		},
		{
			name:     "APP_VERSION has bugfix- prefix",
			envValue: "bugfix-my-bugfix",
			want:     "my-bugfix",
		},
		{
			name:     "APP_VERSION has only prefix",
			envValue: "feature-",
			want:     "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv("APP_VERSION", tt.envValue)
			assert.Equal(t, tt.want, GetGreyName())
		})
	}
}

func TestGetDomainHost(t *testing.T) {
	tests := []struct {
		name        string
		env         string
		appVersion  string
		domain      string
		want        string
	}{
		{
			name:   "PRODUCTION environment",
			env:    PRODUCTION,
			domain: GO,
			want:   "go.moego.pet",
		},
		{
			name:   "STAGING environment",
			env:    STAGING,
			domain: MY,
			want:   "my.s1.moego.dev",
		},
		{
			name:   "TESTING environment without grey name",
			env:    TESTING,
			domain: CLIENT,
			want:   "client.t2.moego.dev",
		},
		{
			name:       "TESTING environment with grey name",
			env:        TESTING,
			appVersion: "feature-new-feature",
			domain:     MIS,
			want:       "new-feature-grey-mis.t2.moego.dev",
		},
		{
			name:   "LOCAL environment (default case)",
			env:    LOCAL,
			domain: GO,
			want:   "go.t2.moego.dev",
		},
		{
			name:   "Default environment",
			env:    "any-other-env",
			domain: GO,
			want:   "go.t2.moego.dev",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv(MOEGO_ENVIRONMENT, tt.env)
			if tt.appVersion != "" {
				t.Setenv("APP_VERSION", tt.appVersion)
			} else {
				t.Setenv("APP_VERSION", "")
			}
			assert.Equal(t, tt.want, GetDomainHost(tt.domain))
		})
	}
}

func TestIsProduction(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		want     bool
	}{
		{
			name:     "Environment is production",
			envValue: PRODUCTION,
			want:     true,
		},
		{
			name:     "Environment is not production",
			envValue: STAGING,
			want:     false,
		},
		{
			name:     "Environment is not set (defaults to local)",
			envValue: "",
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Setenv(MOEGO_ENVIRONMENT, tt.envValue)
			assert.Equal(t, tt.want, IsProduction())
		})
	}
}
