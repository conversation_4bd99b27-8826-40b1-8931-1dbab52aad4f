package env

import (
	"fmt"
	"os"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	// env key
	MOEGO_ENVIRONMENT = "MOEGO_ENVIRONMENT"
	APP_VERSION = "APP_VERSION"

	// domains
	GO  = "go"
	MY = "my"
	CLIENT = "client"
	MIS = "mis"

	// environments
	LOCAL      = "local"
	STAGING    = "staging"
	PRODUCTION = "production"
	TESTING    = "testing"
)

func GetEnv() string {
	env := os.Getenv(MOEGO_ENVIRONMENT)
	if len(env) == 0 {
		env = LOCAL
		log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
	}

	return env
}

func GetGreyName() string {
	grey := os.Getenv(APP_VERSION)
	if len(grey) == 0 {
		return ""
	}

	// remove leading "feature-" or "bugfix-"
	if grey[:8] == "feature-" {
		return grey[8:]
	}
	if grey[:7] == "bugfix-" {
		return grey[7:]
	}

	return ""
}

func GetDomainHost(domain string) string {
	switch GetEnv() {
	case PRODUCTION:
		return domain + ".moego.pet"
	case STAGING:
		return domain + ".s1.moego.dev"
	case TESTING:
		{
			grey := GetGreyName()
			if len(grey) > 0 {
				return fmt.Sprintf("%s-grey-%s.t2.moego.dev", grey, domain)
			}

			return domain + ".t2.moego.dev"
		}
	default:
		return domain + ".t2.moego.dev"
	}
}

func IsProduction() bool {
	return GetEnv() == PRODUCTION
}
