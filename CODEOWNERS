# This file is auto-generated by scripts/generate_codeowners.sh
# Do not edit this file manually. Edit metadata.yaml files in each app directory instead.
# 
# 格式说明：
# /path/to/directory/ @owner1 @owner2
# 表示该目录下的所有文件都由指定的所有者管理

# Code owners for authn service
/backend/app/authn/ <EMAIL> 

# Code owners for cs_page_watcher service
/backend/app/cs_page_watcher/ <EMAIL> <EMAIL> <EMAIL> <EMAIL> 

# Code owners for customer service
/backend/app/customer/ <EMAIL> <EMAIL> 

# Code owners for customer_portal service
/backend/app/customer_portal/ <EMAIL> 

# Code owners for devops-auth service
/backend/app/devops-auth/ <EMAIL> 

# Code owners for message_hub service
/backend/app/message_hub/ <EMAIL> 

# Code owners for offering service
/backend/app/offering/ <EMAIL> <EMAIL>

# Code owners for onboarding service
/backend/app/onboarding/ <EMAIL> 

# Code owners for voice_agent service
/backend/app/voice_agent/ <EMAIL> 

# Default code owners for everything else
* <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>
